# 🔍 DIAGNÓSTICO: Stage No Persiste en Base de Datos

## 🚨 Problema Específico

**SÍNTOMA**: El select cambia a "Entregado" (10) y se ve el cambio, pero cuando vuelves a buscar la venta aparece "Pendiente" (9) porque el stage no se guardó en la base de datos.

## 🔧 Herramientas de Diagnóstico Agregadas

### **1. Logs Detallados en Frontend**
```javascript
=== GUARDAR GRADUACIÓN ===
Stage seleccionado del DOM: 10
Stage en objeto graduación: 10
¿Stage cambió de 9?: true
Graduación completa que se enviará: {stage: "10", odEsfera: "-1.25", ...}
```

### **2. Logs Detallados en Backend**
```php
AGREGAR GRADUACION DEBUG - Datos completos: {"stage":"10","odEsfera":"-1.25",...}
AGREGAR GRADUACION DEBUG - Stage recibido: 10
AGREGAR GRADUACION DEBUG - Tipo de stage: string
AGREGAR GRADUACION DEBUG - Stage establecido en orden: 10
AGREGAR GRADUACION DEBUG - Stage después del flush: 10
AGREGAR GRADUACION DEBUG - Orden guardada con ID: 456
```

### **3. Endpoint Identificado**
- ✅ **Se usa**: `/agregar_graduacion` (para crear Y modificar)
- ❌ **NO se usa**: `/guardar-graduacion` (no se llama desde frontend)

## 🧪 Pasos para Diagnosticar

### **Paso 1: Probar Cambio de Stage**
1. **Crear nueva venta** con armazón
2. **Agregar graduación** con stage "Pendiente" (9)
3. **Guardar venta**
4. **Volver a abrir la venta**
5. **Modificar graduación** → Cambiar stage a "Entregado" (10)
6. **Guardar graduación**
7. **Verificar logs** en consola y servidor

### **Paso 2: Verificar Logs del Frontend**
**En consola del navegador, buscar:**
```
Stage seleccionado del DOM: 10
Stage en objeto graduación: 10
¿Stage cambió de 9?: true
```

### **Paso 3: Verificar Logs del Backend**
**En terminal del servidor, buscar:**
```
AGREGAR GRADUACION DEBUG - Stage recibido: 10
AGREGAR GRADUACION DEBUG - Stage establecido en orden: 10
AGREGAR GRADUACION DEBUG - Stage después del flush: 10
```

### **Paso 4: Verificar Base de Datos**
```sql
-- Verificar que el stage se haya guardado
SELECT idordenlaboratorio, stage, actualizacion 
FROM ordenLaboratorio 
ORDER BY actualizacion DESC 
LIMIT 5;

-- Verificar orden específica
SELECT stage, actualizacion 
FROM ordenLaboratorio 
WHERE idordenlaboratorio = [ID_DE_LA_ORDEN];
```

## 🔍 Posibles Problemas y Diagnósticos

### **Problema 1: Stage No Se Envía**
**SÍNTOMA**: Frontend muestra stage 10 pero logs del backend muestran stage 9
**DIAGNÓSTICO**: 
- Verificar logs de "Graduación completa que se enviará"
- Verificar que el select tenga el valor correcto antes de enviar

### **Problema 2: Backend No Recibe Stage**
**SÍNTOMA**: Logs del backend no muestran stage 10
**DIAGNÓSTICO**:
- Verificar que los datos lleguen completos
- Verificar formato JSON del request

### **Problema 3: Stage Se Recibe Pero No Se Establece**
**SÍNTOMA**: Backend recibe stage 10 pero "Stage establecido" muestra 9
**DIAGNÓSTICO**:
- Verificar que `$orden->setStage()` funcione correctamente
- Verificar que no haya validaciones que cambien el stage

### **Problema 4: Stage Se Establece Pero No Se Persiste**
**SÍNTOMA**: "Stage establecido" muestra 10 pero "Stage después del flush" muestra 9
**DIAGNÓSTICO**:
- Verificar que el flush funcione correctamente
- Verificar que no haya triggers en BD que cambien el stage
- Verificar permisos de escritura en BD

### **Problema 5: Stage Se Persiste Pero Se Pierde**
**SÍNTOMA**: Todo parece correcto pero al recargar vuelve a 9
**DIAGNÓSTICO**:
- Verificar que se esté consultando la orden correcta
- Verificar que no haya múltiples órdenes para el mismo producto

## 🧪 Pruebas Específicas

### **Prueba A: Verificar Envío de Datos**
```javascript
// En consola cuando modal esté abierto:
console.log("Stage actual:", $("#stage").val());
// Cambiar a 10 y verificar
$("#stage").val("10");
console.log("Stage después del cambio:", $("#stage").val());
```

### **Prueba B: Verificar Request**
```javascript
// En Network tab del navegador:
// 1. Abrir Network tab
// 2. Guardar graduación
// 3. Buscar request a /agregar_graduacion
// 4. Verificar que el body contenga "stage":"10"
```

### **Prueba C: Verificar Base de Datos Directamente**
```sql
-- Antes de modificar
SELECT stage FROM ordenLaboratorio WHERE idordenlaboratorio = [ID];

-- Después de modificar
SELECT stage FROM ordenLaboratorio WHERE idordenlaboratorio = [ID];
```

### **Prueba D: Verificar Múltiples Órdenes**
```sql
-- Verificar si hay múltiples órdenes para el mismo producto
SELECT ol.idordenlaboratorio, ol.stage, ol.actualizacion, sv.idstockventa
FROM ordenLaboratorio ol
JOIN stockventaordenlaboratorio svol ON ol.idordenlaboratorio = svol.ordenlaboratorioIdordenlaboratorio
JOIN stockventa sv ON svol.stockventaIdstockventa = sv.idstockventa
WHERE sv.idstockventa = [ID_STOCKVENTA]
ORDER BY ol.actualizacion DESC;
```

## 📋 Checklist de Verificación

### **Frontend:**
- [ ] Select cambia visualmente a "Entregado"
- [ ] Logs muestran stage 10 en objeto graduación
- [ ] Request se envía sin errores
- [ ] Response es exitosa

### **Backend:**
- [ ] Logs muestran datos completos recibidos
- [ ] Stage se recibe como "10"
- [ ] Stage se establece en orden
- [ ] Flush se ejecuta sin errores
- [ ] Stage persiste después del flush

### **Base de Datos:**
- [ ] Campo stage se actualiza a "10"
- [ ] Fecha de actualización es reciente
- [ ] No hay múltiples órdenes conflictivas
- [ ] Consulta posterior devuelve stage "10"

## 🎯 Resultado Esperado

**Flujo Exitoso Completo:**

1. ✅ **Cambiar select** a "Entregado" (10)
2. ✅ **Frontend recolecta** stage 10 correctamente
3. ✅ **Backend recibe** stage 10
4. ✅ **Stage se establece** en orden
5. ✅ **Stage se persiste** en BD
6. ✅ **Al recargar venta** → Stage sigue siendo 10

## 🚨 Si Sigue Sin Funcionar

### **Verificaciones Adicionales:**

1. **Verificar transacciones** de BD
2. **Verificar que no haya rollback** automático
3. **Verificar permisos** de usuario de BD
4. **Verificar que la orden** sea la correcta
5. **Verificar que no haya** validaciones adicionales

### **Consulta de Emergencia:**
```sql
-- Actualizar manualmente para probar
UPDATE ordenLaboratorio 
SET stage = '10', actualizacion = NOW() 
WHERE idordenlaboratorio = [ID];

-- Verificar que se haya actualizado
SELECT stage, actualizacion 
FROM ordenLaboratorio 
WHERE idordenlaboratorio = [ID];
```

**¡Con estos logs detallados deberíamos poder identificar exactamente dónde se pierde el stage!** 🔍
