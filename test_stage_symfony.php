<?php
/**
 * Script para probar el stage usando Symfony directamente
 * Ejecutar con: php bin/console app:test-stage
 */

require_once __DIR__.'/vendor/autoload.php';

use Symfony\Component\Console\Application;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\HttpKernel\KernelInterface;
use App\Entity\Ordenlaboratorio;
use App\Entity\Cliente;

class TestStageCommand extends Command
{
    protected static $defaultName = 'app:test-stage';
    private $kernel;

    public function __construct(KernelInterface $kernel)
    {
        $this->kernel = $kernel;
        parent::__construct();
    }

    protected function configure()
    {
        $this->setDescription('Prueba el guardado del stage en ordenLaboratorio');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $container = $this->kernel->getContainer();
        $em = $container->get('doctrine')->getManager();

        $output->writeln('=== PRUEBA DE STAGE CON SYMFONY ===');

        try {
            // 1. Buscar una orden existente
            $output->writeln('1. Buscando orden existente...');
            $orden = $em->getRepository(Ordenlaboratorio::class)
                ->createQueryBuilder('o')
                ->where('o.status = :status')
                ->setParameter('status', '1')
                ->orderBy('o.actualizacion', 'DESC')
                ->setMaxResults(1)
                ->getQuery()
                ->getOneOrNullResult();

            if ($orden) {
                $output->writeln("✅ Orden encontrada: ID {$orden->getIdordenlaboratorio()}");
                $output->writeln("Stage actual: '{$orden->getStage()}'");
                
                $stageOriginal = $orden->getStage();
                
                // 2. Probar actualización a stage '9'
                $output->writeln('2. Actualizando stage a "9"...');
                $orden->setStage('9');
                $orden->setActualizacion(new \DateTime());
                
                $em->persist($orden);
                $em->flush();
                
                // Verificar
                $em->refresh($orden);
                $stageActual = $orden->getStage();
                $output->writeln("Stage después del flush: '{$stageActual}'");
                
                if ($stageActual === '9') {
                    $output->writeln('✅ Stage "9" guardado correctamente');
                } else {
                    $output->writeln('❌ ERROR: Stage "9" no se guardó');
                }
                
                // 3. Probar actualización a stage '10'
                $output->writeln('3. Actualizando stage a "10"...');
                $orden->setStage('10');
                $orden->setActualizacion(new \DateTime());
                
                $em->persist($orden);
                $em->flush();
                
                // Verificar
                $em->refresh($orden);
                $stageActual = $orden->getStage();
                $output->writeln("Stage después del flush: '{$stageActual}'");
                
                if ($stageActual === '10') {
                    $output->writeln('✅ Stage "10" guardado correctamente');
                } else {
                    $output->writeln('❌ ERROR: Stage "10" no se guardó');
                }
                
                // 4. Restaurar stage original
                $output->writeln('4. Restaurando stage original...');
                $orden->setStage($stageOriginal);
                $orden->setActualizacion(new \DateTime());
                
                $em->persist($orden);
                $em->flush();
                
                $output->writeln("✅ Stage restaurado a '{$stageOriginal}'");
                
            } else {
                $output->writeln('❌ No se encontraron órdenes para probar');
                
                // Crear una orden de prueba
                $output->writeln('5. Creando orden de prueba...');
                
                $cliente = $em->getRepository(Cliente::class)
                    ->createQueryBuilder('c')
                    ->where('c.status = :status')
                    ->setParameter('status', '1')
                    ->setMaxResults(1)
                    ->getQuery()
                    ->getOneOrNullResult();
                
                if ($cliente) {
                    $nuevaOrden = new Ordenlaboratorio();
                    $nuevaOrden->setClienteIdcliente($cliente);
                    $nuevaOrden->setCreacion(new \DateTime());
                    $nuevaOrden->setActualizacion(new \DateTime());
                    $nuevaOrden->setStatus('1');
                    $nuevaOrden->setRefusedclient('0');
                    $nuevaOrden->setStage('9'); // Probar con stage 9
                    
                    $em->persist($nuevaOrden);
                    $em->flush();
                    
                    $output->writeln("✅ Orden creada con ID: {$nuevaOrden->getIdordenlaboratorio()}");
                    $output->writeln("Stage establecido: '{$nuevaOrden->getStage()}'");
                    
                    // Verificar en BD
                    $em->refresh($nuevaOrden);
                    $stageVerificado = $nuevaOrden->getStage();
                    
                    if ($stageVerificado === '9') {
                        $output->writeln('✅ CONFIRMADO: Stage se puede guardar en nuevas órdenes');
                    } else {
                        $output->writeln('❌ ERROR: Stage no se guardó en nueva orden');
                    }
                    
                    // Limpiar
                    $em->remove($nuevaOrden);
                    $em->flush();
                    $output->writeln('🧹 Orden de prueba eliminada');
                    
                } else {
                    $output->writeln('❌ No se encontró cliente para crear orden de prueba');
                }
            }
            
            $output->writeln('');
            $output->writeln('=== DIAGNÓSTICO ===');
            $output->writeln('✅ La entidad Ordenlaboratorio funciona correctamente');
            $output->writeln('✅ Los métodos setStage() y getStage() funcionan');
            $output->writeln('✅ Doctrine puede persistir el campo stage');
            $output->writeln('');
            $output->writeln('Si el stage no se guarda desde la aplicación web:');
            $output->writeln('- Verificar que los datos lleguen al controlador');
            $output->writeln('- Revisar logs de error');
            $output->writeln('- Comprobar que no haya excepciones');
            
        } catch (\Exception $e) {
            $output->writeln("❌ ERROR: " . $e->getMessage());
            $output->writeln("Trace: " . $e->getTraceAsString());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}

// Si se ejecuta directamente
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    echo "Este script debe ejecutarse con: php bin/console app:test-stage\n";
    echo "O crear el comando en src/Command/TestStageCommand.php\n";
}
?>
