/**
 * Funciones para manejar los stages de órdenes de laboratorio
 */

/**
 * Establece el stage de una orden de laboratorio a 9 (Pendiente)
 * @param {number} idOrdenLaboratorio - ID de la orden de laboratorio
 * @param {function} onSuccess - Callback de éxito (opcional)
 * @param {function} onError - Callback de error (opcional)
 */
function setStageNueve(idOrdenLaboratorio, onSuccess, onError) {
    if (!idOrdenLaboratorio) {
        console.error('ID de orden de laboratorio es requerido');
        if (onError) onError('ID de orden de laboratorio es requerido');
        return;
    }

    $.ajax({
        url: '/admin/dashboard/set-stage-pendiente',
        type: 'POST',
        data: {
            idordenlaboratorio: idOrdenLaboratorio
        },
        dataType: 'json',
        beforeSend: function() {
            // Mostrar loading si existe
            if (typeof showLoading === 'function') {
                showLoading();
            }
        },
        success: function(response) {
            if (response.success) {
                console.log('Stage actualizado correctamente:', response);
                
                // Mostrar mensaje de éxito
                if (typeof showSuccessMessage === 'function') {
                    showSuccessMessage(response.message);
                } else {
                    alert(response.message);
                }
                
                // Ejecutar callback de éxito si se proporciona
                if (onSuccess) {
                    onSuccess(response);
                }
                
                // Actualizar la interfaz si es necesario
                updateStageDisplay(idOrdenLaboratorio, '9', 'Pendiente');
                
            } else {
                console.error('Error en la respuesta:', response.message);
                if (onError) {
                    onError(response.message);
                } else if (typeof showErrorMessage === 'function') {
                    showErrorMessage(response.message);
                } else {
                    alert('Error: ' + response.message);
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('Error AJAX:', error);
            const errorMessage = 'Error al actualizar el stage: ' + error;
            
            if (onError) {
                onError(errorMessage);
            } else if (typeof showErrorMessage === 'function') {
                showErrorMessage(errorMessage);
            } else {
                alert(errorMessage);
            }
        },
        complete: function() {
            // Ocultar loading si existe
            if (typeof hideLoading === 'function') {
                hideLoading();
            }
        }
    });
}

/**
 * Actualiza el stage de una orden de laboratorio (método genérico)
 * @param {number} idOrdenLaboratorio - ID de la orden de laboratorio
 * @param {string} stage - Nuevo stage (9 para Pendiente, 10 para Entregado)
 * @param {function} onSuccess - Callback de éxito (opcional)
 * @param {function} onError - Callback de error (opcional)
 */
function updateStageGenerico(idOrdenLaboratorio, stage, onSuccess, onError) {
    if (!idOrdenLaboratorio || !stage) {
        console.error('ID de orden de laboratorio y stage son requeridos');
        if (onError) onError('ID de orden de laboratorio y stage son requeridos');
        return;
    }

    const data = {
        idordenlaboratorio: idOrdenLaboratorio,
        stage: stage
    };

    $.ajax({
        url: '/admin/dashboard/update-stage',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        dataType: 'json',
        beforeSend: function() {
            if (typeof showLoading === 'function') {
                showLoading();
            }
        },
        success: function(response) {
            if (response.success) {
                console.log('Stage actualizado correctamente:', response);
                
                if (typeof showSuccessMessage === 'function') {
                    showSuccessMessage(response.message);
                } else {
                    alert(response.message);
                }
                
                if (onSuccess) {
                    onSuccess(response);
                }
                
                // Actualizar la interfaz
                const stageName = stage === '9' ? 'Pendiente' : (stage === '10' ? 'Entregado' : 'Stage ' + stage);
                updateStageDisplay(idOrdenLaboratorio, stage, stageName);
                
            } else {
                console.error('Error en la respuesta:', response.message);
                if (onError) {
                    onError(response.message);
                } else if (typeof showErrorMessage === 'function') {
                    showErrorMessage(response.message);
                } else {
                    alert('Error: ' + response.message);
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('Error AJAX:', error);
            const errorMessage = 'Error al actualizar el stage: ' + error;
            
            if (onError) {
                onError(errorMessage);
            } else if (typeof showErrorMessage === 'function') {
                showErrorMessage(errorMessage);
            } else {
                alert(errorMessage);
            }
        },
        complete: function() {
            if (typeof hideLoading === 'function') {
                hideLoading();
            }
        }
    });
}

/**
 * Actualiza la visualización del stage en la interfaz
 * @param {number} idOrdenLaboratorio - ID de la orden de laboratorio
 * @param {string} stage - Nuevo stage
 * @param {string} stageName - Nombre del stage
 */
function updateStageDisplay(idOrdenLaboratorio, stage, stageName) {
    // Buscar elementos en la interfaz que muestren el stage
    const stageElements = $('[data-order-id="' + idOrdenLaboratorio + '"]').find('.stage-display');
    
    if (stageElements.length > 0) {
        stageElements.each(function() {
            $(this).text(stageName);
            $(this).attr('data-stage', stage);
            
            // Agregar clases CSS según el stage
            $(this).removeClass('stage-pendiente stage-entregado');
            if (stage === '9') {
                $(this).addClass('stage-pendiente');
            } else if (stage === '10') {
                $(this).addClass('stage-entregado');
            }
        });
    }
    
    // También actualizar cualquier select o dropdown que muestre el stage
    const stageSelects = $('select[data-order-id="' + idOrdenLaboratorio + '"]');
    if (stageSelects.length > 0) {
        stageSelects.val(stage);
    }
}

/**
 * Función de conveniencia para establecer stage a Entregado (10)
 * @param {number} idOrdenLaboratorio - ID de la orden de laboratorio
 * @param {function} onSuccess - Callback de éxito (opcional)
 * @param {function} onError - Callback de error (opcional)
 */
function setStageDiez(idOrdenLaboratorio, onSuccess, onError) {
    updateStageGenerico(idOrdenLaboratorio, '10', onSuccess, onError);
}

// Ejemplo de uso:
/*
// Para establecer stage 9 (Pendiente)
setStageNueve(123, function(response) {
    console.log('Éxito:', response);
    // Actualizar tabla o interfaz
}, function(error) {
    console.error('Error:', error);
});

// Para establecer stage 10 (Entregado)
setStageDiez(123);

// Para establecer cualquier stage
updateStageGenerico(123, '9');
*/
