<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de Colores de Productos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
        }
        
        .producto-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        
        /* Estilos copiados de nueva-venta.html.twig */
        .producto-stage-pendiente {
            background-color: #fff3cd !important;
            border-left: 4px solid #ffc107 !important;
        }

        .producto-stage-terminado {
            background-color: #d4edda !important;
            border-left: 4px solid #28a745 !important;
        }

        .fa-glasses.stage-pendiente {
            color: #ffc107 !important;
        }

        .fa-glasses.stage-terminado {
            color: #28a745 !important;
        }
        
        .fa-glasses.text-success {
            color: #28a745 !important;
        }
        
        .btn-test {
            margin: 5px;
        }
        
        .stage-info {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .demo-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">🎨 Prueba de Colores de Productos por Stage</h1>
        
        <div class="demo-section">
            <h3>📋 Productos de Demostración</h3>
            <p>Estos productos simulan la lista "Productos Seleccionados" con diferentes stages:</p>
            
            <!-- Producto sin graduación -->
            <div id="producto-1" class="producto-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>Armazón Ray-Ban Classic</strong>
                        <div class="stage-info">Sin graduación</div>
                    </div>
                    <div>
                        <i class="fas fa-glasses" style="color: #6c757d;"></i>
                        <span class="badge bg-secondary">Sin graduación</span>
                    </div>
                </div>
            </div>
            
            <!-- Producto con graduación pendiente -->
            <div id="producto-2" class="producto-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>Armazón Oakley Sport</strong>
                        <div class="stage-info">Stage: <span id="stage-2">9</span> - <span id="desc-2">Pendiente de graduar</span></div>
                    </div>
                    <div>
                        <i class="fas fa-glasses text-success"></i>
                        <span class="badge bg-warning" id="badge-2">Pendiente</span>
                    </div>
                </div>
            </div>
            
            <!-- Producto con graduación terminada -->
            <div id="producto-3" class="producto-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>Armazón Gucci Premium</strong>
                        <div class="stage-info">Stage: <span id="stage-3">10</span> - <span id="desc-3">Terminado</span></div>
                    </div>
                    <div>
                        <i class="fas fa-glasses text-success"></i>
                        <span class="badge bg-success" id="badge-3">Terminado</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🧪 Controles de Prueba</h3>
            <p>Usa estos botones para cambiar los stages y ver los colores en acción:</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>Producto 2 (Oakley Sport):</h5>
                    <button class="btn btn-warning btn-test" onclick="cambiarStage(2, '9')">
                        🟡 Pendiente (9)
                    </button>
                    <button class="btn btn-success btn-test" onclick="cambiarStage(2, '10')">
                        🟢 Terminado (10)
                    </button>
                    <button class="btn btn-secondary btn-test" onclick="quitarGraduacion(2)">
                        ⚪ Sin graduación
                    </button>
                </div>
                
                <div class="col-md-6">
                    <h5>Producto 3 (Gucci Premium):</h5>
                    <button class="btn btn-warning btn-test" onclick="cambiarStage(3, '9')">
                        🟡 Pendiente (9)
                    </button>
                    <button class="btn btn-success btn-test" onclick="cambiarStage(3, '10')">
                        🟢 Terminado (10)
                    </button>
                    <button class="btn btn-secondary btn-test" onclick="quitarGraduacion(3)">
                        ⚪ Sin graduación
                    </button>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>📖 Guía de Colores</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="producto-item producto-stage-pendiente">
                        <i class="fas fa-glasses stage-pendiente"></i>
                        <strong>🟡 Pendiente de graduar</strong>
                        <div class="stage-info">Stage 9 - Fondo amarillo claro</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="producto-item producto-stage-terminado">
                        <i class="fas fa-glasses stage-terminado"></i>
                        <strong>🟢 Terminado</strong>
                        <div class="stage-info">Stage 10 - Fondo verde claro</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="producto-item">
                        <i class="fas fa-glasses" style="color: #6c757d;"></i>
                        <strong>⚪ Sin graduación</strong>
                        <div class="stage-info">Sin stage - Fondo blanco</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>✅ Instrucciones para Probar en la Aplicación Real</h3>
            <ol>
                <li><strong>Ir a "Nueva Venta"</strong></li>
                <li><strong>Agregar un armazón</strong> a la lista de productos</li>
                <li><strong>Hacer clic en el icono de graduación</strong> 👓</li>
                <li><strong>Seleccionar "Pendiente de graduar"</strong> en el select de etapas</li>
                <li><strong>Llenar algunos campos</strong> de graduación</li>
                <li><strong>Guardar graduación</strong></li>
                <li><strong>Verificar</strong> que el producto se pone amarillo 🟡</li>
                <li><strong>Cambiar a "Terminado"</strong> y verificar que se pone verde 🟢</li>
            </ol>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Función copiada de nueva-venta.html.twig
        function actualizarColorProductoPorStage(idProducto, stage) {
            const productoElement = document.getElementById("producto-" + idProducto);
            const iconoGraduacion = productoElement.querySelector(".fa-glasses");
            
            // Remover clases anteriores
            productoElement.classList.remove("producto-stage-pendiente", "producto-stage-terminado");
            iconoGraduacion.classList.remove("stage-pendiente", "stage-terminado");
            
            // Agregar clase según el stage
            if (stage === '9') {
                // Pendiente de graduar - Amarillo
                productoElement.classList.add("producto-stage-pendiente");
                iconoGraduacion.classList.add("stage-pendiente");
                console.log("Producto " + idProducto + " marcado como PENDIENTE (amarillo)");
            } else if (stage === '10') {
                // Terminado - Verde
                productoElement.classList.add("producto-stage-terminado");
                iconoGraduacion.classList.add("stage-terminado");
                console.log("Producto " + idProducto + " marcado como TERMINADO (verde)");
            }
        }
        
        function cambiarStage(idProducto, stage) {
            actualizarColorProductoPorStage(idProducto, stage);
            
            // Actualizar información en pantalla
            document.getElementById("stage-" + idProducto).textContent = stage;
            const desc = stage === '9' ? 'Pendiente de graduar' : 'Terminado';
            document.getElementById("desc-" + idProducto).textContent = desc;
            
            const badge = document.getElementById("badge-" + idProducto);
            if (stage === '9') {
                badge.textContent = 'Pendiente';
                badge.className = 'badge bg-warning';
            } else {
                badge.textContent = 'Terminado';
                badge.className = 'badge bg-success';
            }
            
            // Mostrar en consola
            console.log(`Producto ${idProducto} cambiado a stage ${stage}`);
        }
        
        function quitarGraduacion(idProducto) {
            const productoElement = document.getElementById("producto-" + idProducto);
            const iconoGraduacion = productoElement.querySelector(".fa-glasses");
            
            // Remover todas las clases de stage
            productoElement.classList.remove("producto-stage-pendiente", "producto-stage-terminado");
            iconoGraduacion.classList.remove("stage-pendiente", "stage-terminado", "text-success");
            iconoGraduacion.style.color = "#6c757d";
            
            // Actualizar información
            document.getElementById("stage-" + idProducto).textContent = "-";
            document.getElementById("desc-" + idProducto).textContent = "Sin graduación";
            
            const badge = document.getElementById("badge-" + idProducto);
            badge.textContent = 'Sin graduación';
            badge.className = 'badge bg-secondary';
            
            console.log(`Producto ${idProducto} sin graduación`);
        }
        
        // Inicializar colores al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 Página de prueba de colores cargada');
            
            // Aplicar colores iniciales
            actualizarColorProductoPorStage(2, '9'); // Producto 2 pendiente
            actualizarColorProductoPorStage(3, '10'); // Producto 3 terminado
            
            console.log('✅ Colores iniciales aplicados');
        });
    </script>
</body>
</html>
