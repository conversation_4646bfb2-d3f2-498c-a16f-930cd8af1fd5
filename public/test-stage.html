<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de Stage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        .stage-selector {
            margin: 10px 0;
        }
        select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Prueba de Stage en Órdenes de Laboratorio</h1>
        
        <div class="info">
            <strong>Objetivo:</strong> Verificar que el campo 'stage' se puede guardar correctamente en la base de datos.
        </div>

        <div class="test-section">
            <h3>1. Prueba Directa de Stage</h3>
            <p>Esta prueba actualiza directamente una orden existente con un nuevo stage.</p>
            
            <div class="stage-selector">
                <label>Seleccionar Stage:</label>
                <select id="stageSelect">
                    <option value="9">9 - Pendiente de graduar</option>
                    <option value="10">10 - Terminado</option>
                    <option value="1">1 - Inicial</option>
                </select>
                <button onclick="testStage()">Probar Stage</button>
            </div>
            
            <div id="testResult"></div>
        </div>

        <div class="test-section">
            <h3>2. Simulación de Graduación</h3>
            <p>Esta prueba simula el envío de datos de graduación como lo haría el modal.</p>
            
            <button onclick="testGraduacionCompleta()">Simular Graduación Completa</button>
            <div id="graduacionResult"></div>
        </div>

        <div class="test-section">
            <h3>3. Logs en Tiempo Real</h3>
            <p>Revisa la consola del navegador y los logs del servidor para ver el flujo de datos.</p>
            <button onclick="clearLogs()">Limpiar Logs</button>
            <div id="logsContainer"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            console.log(message);
            const logsContainer = document.getElementById('logsContainer');
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            logsContainer.appendChild(logEntry);
        }

        function clearLogs() {
            document.getElementById('logsContainer').innerHTML = '';
            document.getElementById('testResult').innerHTML = '';
            document.getElementById('graduacionResult').innerHTML = '';
        }

        async function testStage() {
            const stage = document.getElementById('stageSelect').value;
            const resultDiv = document.getElementById('testResult');
            
            log(`Probando stage: ${stage}`, 'info');
            
            try {
                const response = await fetch('/test-stage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ stage: stage })
                });
                
                const data = await response.json();
                
                if (data.exito) {
                    log('✅ Prueba exitosa', 'success');
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Prueba Exitosa</h4>
                            <p><strong>Orden ID:</strong> ${data.orden_id}</p>
                            <p><strong>Stage Original:</strong> ${data.stage_original}</p>
                            <p><strong>Stage Enviado:</strong> ${data.stage_enviado}</p>
                            <p><strong>Stage Guardado:</strong> ${data.stage_guardado}</p>
                            <p><strong>Guardado Correctamente:</strong> ${data.guardado_correctamente ? '✅ SÍ' : '❌ NO'}</p>
                        </div>
                    `;
                } else {
                    log('❌ Error en la prueba', 'error');
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Error</h4>
                            <p>${data.mensaje}</p>
                        </div>
                    `;
                }
                
                log('Respuesta completa:', 'info');
                log('<pre>' + JSON.stringify(data, null, 2) + '</pre>', 'info');
                
            } catch (error) {
                log('❌ Error de conexión: ' + error.message, 'error');
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Error de Conexión</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testGraduacionCompleta() {
            const resultDiv = document.getElementById('graduacionResult');
            
            log('Simulando graduación completa...', 'info');
            
            const graduacionData = {
                idVenta: 1, // ID de prueba
                idProducto: 1, // ID de prueba
                odEsfera: '-1.25',
                odCilindro: '-0.50',
                odEje: '90',
                odAdicion: '*****',
                oiEsfera: '-1.50',
                oiCilindro: '-0.75',
                oiEje: '85',
                oiAdicion: '*****',
                distanciaPupilar: '62',
                altura: '18',
                _aco: '14.5',
                diagnosis: 'Miopía astigmática',
                notas: 'Prueba de graduación completa',
                stage: '9' // ¡Aquí está el stage!
            };
            
            log('Datos de graduación:', 'info');
            log('<pre>' + JSON.stringify(graduacionData, null, 2) + '</pre>', 'info');
            
            try {
                const response = await fetch('/agregar-graduacion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(graduacionData)
                });
                
                const data = await response.json();
                
                if (data.exito) {
                    log('✅ Graduación guardada exitosamente', 'success');
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Graduación Guardada</h4>
                            <p><strong>Mensaje:</strong> ${data.msj}</p>
                            <p><strong>ID Orden:</strong> ${data.idOrden}</p>
                            <p><strong>Nota:</strong> Revisa los logs del servidor para ver si el stage se guardó</p>
                        </div>
                    `;
                } else {
                    log('❌ Error al guardar graduación', 'error');
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Error</h4>
                            <p>${data.msj}</p>
                        </div>
                    `;
                }
                
                log('Respuesta de graduación:', 'info');
                log('<pre>' + JSON.stringify(data, null, 2) + '</pre>', 'info');
                
            } catch (error) {
                log('❌ Error de conexión: ' + error.message, 'error');
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Error de Conexión</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Inicializar
        log('🧪 Página de prueba cargada', 'info');
        log('Instrucciones:', 'info');
        log('1. Usa "Probar Stage" para verificar que el campo stage funciona', 'info');
        log('2. Usa "Simular Graduación" para probar el flujo completo', 'info');
        log('3. Revisa los logs del servidor con: tail -f var/log/dev.log', 'info');
    </script>
</body>
</html>
