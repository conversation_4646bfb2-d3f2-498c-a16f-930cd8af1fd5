<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba Modal de Stage</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        
        .producto-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        
        /* Estilos copiados de nueva-venta.html.twig */
        .producto-stage-pendiente {
            background-color: #fff3cd !important;
            border-left: 4px solid #ffc107 !important;
        }

        .producto-stage-terminado {
            background-color: #d4edda !important;
            border-left: 4px solid #28a745 !important;
        }

        .fa-glasses.stage-pendiente {
            color: #ffc107 !important;
        }

        .fa-glasses.stage-terminado {
            color: #28a745 !important;
        }
        
        .fa-glasses.text-success {
            color: #28a745 !important;
        }
        
        .logs-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .log-info { background-color: #d1ecf1; color: #0c5460; }
        .log-success { background-color: #d4edda; color: #155724; }
        .log-warning { background-color: #fff3cd; color: #856404; }
        .log-error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">🧪 Prueba Modal de Stage</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>📦 Producto de Prueba</h3>
                <div id="producto-123" class="producto-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>Armazón de Prueba</strong>
                            <div class="text-muted">ID: 123</div>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-primary" onclick="abrirModal()">
                                <i class="fas fa-glasses"></i> Graduación
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h5>Controles de Prueba:</h5>
                    <button class="btn btn-warning btn-sm" onclick="cambiarColor('9')">🟡 Pendiente</button>
                    <button class="btn btn-success btn-sm" onclick="cambiarColor('10')">🟢 Terminado</button>
                    <button class="btn btn-secondary btn-sm" onclick="limpiarColor()">⚪ Limpiar</button>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>📋 Logs de Debug</h3>
                <div id="logs" class="logs-container"></div>
                <button class="btn btn-sm btn-outline-secondary mt-2" onclick="limpiarLogs()">Limpiar Logs</button>
            </div>
        </div>
    </div>

    <!-- Modal de Graduación -->
    <div class="modal fade" id="modal-graduacion" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">👓 Graduación del Producto</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Select de Etapas -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="stage" class="form-label fw-bold">
                                <i class="fas fa-tasks"></i> Etapa
                            </label>
                            <select class="form-select" id="stage" name="stage">
                                <option value="9" selected>Pendiente de graduar</option>
                                <option value="10">Terminado</option>
                            </select>
                            <div class="form-text">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    Selecciona la etapa actual de la orden de laboratorio
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Campos básicos de graduación -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="od-esfera" class="form-label">OD Esfera</label>
                            <input type="text" class="form-control" id="od-esfera" placeholder="-1.25">
                        </div>
                        <div class="col-md-6">
                            <label for="oi-esfera" class="form-label">OI Esfera</label>
                            <input type="text" class="form-control" id="oi-esfera" placeholder="-1.50">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="guardarGraduacion()">Guardar Graduación</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Variables globales
        let graduacionesPendientes = {};
        
        // Función de logging
        function log(message, type = 'info') {
            console.log(message);
            const logsContainer = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
        
        function limpiarLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        // Función copiada de nueva-venta.html.twig
        function actualizarColorProductoPorStage(idProducto, stage, idRow = null) {
            log("=== ACTUALIZAR COLOR PRODUCTO ===", "info");
            log("ID Producto: " + idProducto, "info");
            log("ID Row: " + (idRow || "null"), "info");
            log("Stage: " + stage, "info");
            
            // Intentar encontrar el elemento usando idRow primero, luego idProducto
            let productoElement;
            if (idRow) {
                productoElement = $("#" + idRow);
                log("Buscando por idRow: " + idRow + " - Encontrado: " + (productoElement.length > 0), "info");
            }
            
            if (!productoElement || productoElement.length === 0) {
                productoElement = $("#producto-" + idProducto);
                log("Buscando por idProducto: #producto-" + idProducto + " - Encontrado: " + (productoElement.length > 0), "info");
            }
            
            if (productoElement.length === 0) {
                log("❌ No se encontró el elemento", "error");
                return;
            }
            
            const iconoGraduacion = productoElement.find(".fa-glasses");
            log("Icono graduación encontrado: " + (iconoGraduacion.length > 0), "info");
            
            // Remover clases anteriores
            productoElement.removeClass("producto-stage-pendiente producto-stage-terminado");
            iconoGraduacion.removeClass("stage-pendiente stage-terminado");
            
            // Agregar clase según el stage
            if (stage === '9') {
                // Pendiente de graduar - Amarillo
                productoElement.addClass("producto-stage-pendiente");
                iconoGraduacion.addClass("stage-pendiente");
                log("✅ Producto " + idProducto + " marcado como PENDIENTE (amarillo)", "success");
            } else if (stage === '10') {
                // Terminado - Verde
                productoElement.addClass("producto-stage-terminado");
                iconoGraduacion.addClass("stage-terminado");
                log("✅ Producto " + idProducto + " marcado como TERMINADO (verde)", "success");
            } else {
                log("⚪ Stage no reconocido: " + stage, "warning");
            }
        }
        
        // Evento para detectar cambios en el select de stage
        $(document).on("change", "#stage", function() {
            const stageSeleccionado = $(this).val();
            log("=== CAMBIO EN SELECT DE STAGE ===", "info");
            log("Nuevo stage seleccionado: " + stageSeleccionado, "info");
            
            // Mostrar descripción del stage
            const descripcion = stageSeleccionado === '9' ? 'Pendiente de graduar' : 
                               stageSeleccionado === '10' ? 'Terminado' : 'Desconocido';
            log("Descripción: " + descripcion, "info");
        });
        
        function abrirModal() {
            log("=== ABRIR MODAL ===", "info");
            
            // Establecer valores por defecto
            $("#stage").val("9");
            $("#od-esfera").val("");
            $("#oi-esfera").val("");
            
            // Mostrar el modal
            const modal = new bootstrap.Modal(document.getElementById('modal-graduacion'));
            modal.show();
            
            log("Modal abierto con stage por defecto: 9", "info");
        }
        
        function guardarGraduacion() {
            log("=== GUARDAR GRADUACIÓN ===", "info");
            
            const idProducto = "123";
            const idRow = "producto-123";
            const stageSeleccionado = $("#stage").val();
            
            log("ID Producto: " + idProducto, "info");
            log("ID Row: " + idRow, "info");
            log("Stage seleccionado: " + stageSeleccionado, "info");
            
            const graduacion = {
                odEsfera: $("#od-esfera").val(),
                oiEsfera: $("#oi-esfera").val(),
                stage: stageSeleccionado || "9"
            };
            
            log("Graduación completa: " + JSON.stringify(graduacion), "info");
            
            // Guardar en memoria
            graduacionesPendientes[idRow] = graduacion;
            graduacionesPendientes[idProducto] = graduacion;
            
            // Actualizar color del producto según el stage
            log("Llamando actualizarColorProductoPorStage con: " + idProducto + ", " + graduacion.stage + ", " + idRow, "info");
            actualizarColorProductoPorStage(idProducto, graduacion.stage || "9", idRow);
            
            // Cerrar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('modal-graduacion'));
            modal.hide();
            
            log("✅ Graduación guardada exitosamente", "success");
        }
        
        // Funciones de prueba
        function cambiarColor(stage) {
            log("=== CAMBIO MANUAL DE COLOR ===", "info");
            actualizarColorProductoPorStage("123", stage, "producto-123");
        }
        
        function limpiarColor() {
            log("=== LIMPIAR COLOR ===", "info");
            const producto = $("#producto-123");
            producto.removeClass("producto-stage-pendiente producto-stage-terminado");
            const icono = producto.find(".fa-glasses");
            icono.removeClass("stage-pendiente stage-terminado");
            log("Color limpiado", "info");
        }
        
        // Inicializar
        $(document).ready(function() {
            log("🧪 Página de prueba cargada", "success");
            log("Instrucciones:", "info");
            log("1. Haz clic en 'Graduación' para abrir el modal", "info");
            log("2. Cambia el select de 'Etapa'", "info");
            log("3. Haz clic en 'Guardar Graduación'", "info");
            log("4. Verifica que el producto cambie de color", "info");
        });
    </script>
</body>
</html>
