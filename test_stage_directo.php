<?php
/**
 * Script para probar directamente si podemos insertar/actualizar el stage en la BD
 */

// Configuración de la base de datos
$host = 'localhost';
$dbname = 'pv360';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== PRUEBA DIRECTA DE STAGE EN BD ===\n\n";
    
    // 1. Verificar estructura de la tabla
    echo "1. ESTRUCTURA DE LA TABLA ordenLaboratorio:\n";
    $stmt = $pdo->prepare("DESCRIBE ordenLaboratorio");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $stageColumn = null;
    foreach ($columns as $column) {
        if ($column['Field'] === 'stage') {
            $stageColumn = $column;
            break;
        }
    }
    
    if ($stageColumn) {
        echo "✅ Columna 'stage' encontrada:\n";
        echo "   - Tipo: {$stageColumn['Type']}\n";
        echo "   - Null: {$stageColumn['Null']}\n";
        echo "   - Default: {$stageColumn['Default']}\n";
        echo "   - Extra: {$stageColumn['Extra']}\n\n";
    } else {
        echo "❌ Columna 'stage' NO encontrada en la tabla\n\n";
        exit;
    }
    
    // 2. Buscar una orden existente para probar
    echo "2. BUSCANDO ORDEN EXISTENTE PARA PROBAR:\n";
    $stmt = $pdo->prepare("
        SELECT idordenLaboratorio, stage, creacion 
        FROM ordenLaboratorio 
        WHERE status = '1' 
        ORDER BY actualizacion DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $orden = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($orden) {
        echo "✅ Orden encontrada:\n";
        echo "   - ID: {$orden['idordenLaboratorio']}\n";
        echo "   - Stage actual: '{$orden['stage']}'\n";
        echo "   - Creación: {$orden['creacion']}\n\n";
        
        $ordenId = $orden['idordenLaboratorio'];
        $stageOriginal = $orden['stage'];
    } else {
        echo "❌ No se encontraron órdenes para probar\n\n";
        exit;
    }
    
    // 3. Probar actualización directa del stage
    echo "3. PROBANDO ACTUALIZACIÓN DIRECTA DEL STAGE:\n";
    
    // Probar con stage '9'
    $nuevoStage = '9';
    echo "Actualizando stage a '{$nuevoStage}'...\n";
    
    $stmt = $pdo->prepare("
        UPDATE ordenLaboratorio 
        SET stage = :stage, actualizacion = NOW() 
        WHERE idordenLaboratorio = :id
    ");
    $result = $stmt->execute([
        'stage' => $nuevoStage,
        'id' => $ordenId
    ]);
    
    if ($result) {
        echo "✅ Actualización exitosa\n";
        
        // Verificar que se guardó
        $stmt = $pdo->prepare("SELECT stage FROM ordenLaboratorio WHERE idordenLaboratorio = :id");
        $stmt->execute(['id' => $ordenId]);
        $stageGuardado = $stmt->fetch(PDO::FETCH_ASSOC)['stage'];
        
        echo "Stage guardado en BD: '{$stageGuardado}'\n";
        
        if ($stageGuardado === $nuevoStage) {
            echo "✅ CONFIRMADO: El stage se puede guardar correctamente\n\n";
        } else {
            echo "❌ ERROR: Stage no se guardó correctamente\n\n";
        }
    } else {
        echo "❌ Error en la actualización\n\n";
    }
    
    // 4. Probar con stage '10'
    $nuevoStage = '10';
    echo "Actualizando stage a '{$nuevoStage}'...\n";
    
    $stmt = $pdo->prepare("
        UPDATE ordenLaboratorio 
        SET stage = :stage, actualizacion = NOW() 
        WHERE idordenLaboratorio = :id
    ");
    $result = $stmt->execute([
        'stage' => $nuevoStage,
        'id' => $ordenId
    ]);
    
    if ($result) {
        echo "✅ Actualización exitosa\n";
        
        // Verificar que se guardó
        $stmt = $pdo->prepare("SELECT stage FROM ordenLaboratorio WHERE idordenLaboratorio = :id");
        $stmt->execute(['id' => $ordenId]);
        $stageGuardado = $stmt->fetch(PDO::FETCH_ASSOC)['stage'];
        
        echo "Stage guardado en BD: '{$stageGuardado}'\n";
        
        if ($stageGuardado === $nuevoStage) {
            echo "✅ CONFIRMADO: El stage '10' también se puede guardar\n\n";
        } else {
            echo "❌ ERROR: Stage '10' no se guardó correctamente\n\n";
        }
    } else {
        echo "❌ Error en la actualización\n\n";
    }
    
    // 5. Restaurar stage original
    echo "5. RESTAURANDO STAGE ORIGINAL:\n";
    $stmt = $pdo->prepare("
        UPDATE ordenLaboratorio 
        SET stage = :stage, actualizacion = NOW() 
        WHERE idordenLaboratorio = :id
    ");
    $stmt->execute([
        'stage' => $stageOriginal,
        'id' => $ordenId
    ]);
    echo "✅ Stage restaurado a '{$stageOriginal}'\n\n";
    
    // 6. Probar inserción de nueva orden
    echo "6. PROBANDO INSERCIÓN DE NUEVA ORDEN CON STAGE:\n";
    
    // Buscar un cliente para la prueba
    $stmt = $pdo->prepare("SELECT idcliente FROM cliente WHERE status = '1' LIMIT 1");
    $stmt->execute();
    $cliente = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($cliente) {
        $clienteId = $cliente['idcliente'];
        
        $stmt = $pdo->prepare("
            INSERT INTO ordenLaboratorio (
                cliente_idcliente, 
                creacion, 
                actualizacion, 
                status, 
                refusedClient, 
                stage
            ) VALUES (
                :cliente_id, 
                NOW(), 
                NOW(), 
                '1', 
                '0', 
                :stage
            )
        ");
        
        $testStage = '9';
        $result = $stmt->execute([
            'cliente_id' => $clienteId,
            'stage' => $testStage
        ]);
        
        if ($result) {
            $nuevaOrdenId = $pdo->lastInsertId();
            echo "✅ Nueva orden creada con ID: {$nuevaOrdenId}\n";
            
            // Verificar stage
            $stmt = $pdo->prepare("SELECT stage FROM ordenLaboratorio WHERE idordenLaboratorio = :id");
            $stmt->execute(['id' => $nuevaOrdenId]);
            $stageVerificado = $stmt->fetch(PDO::FETCH_ASSOC)['stage'];
            
            echo "Stage en nueva orden: '{$stageVerificado}'\n";
            
            if ($stageVerificado === $testStage) {
                echo "✅ CONFIRMADO: Se puede insertar stage en nuevas órdenes\n";
            } else {
                echo "❌ ERROR: Stage no se insertó correctamente\n";
            }
            
            // Limpiar - eliminar orden de prueba
            $stmt = $pdo->prepare("DELETE FROM ordenLaboratorio WHERE idordenLaboratorio = :id");
            $stmt->execute(['id' => $nuevaOrdenId]);
            echo "🧹 Orden de prueba eliminada\n\n";
            
        } else {
            echo "❌ Error al crear nueva orden\n\n";
        }
    } else {
        echo "❌ No se encontró cliente para la prueba\n\n";
    }
    
    // 7. Diagnóstico final
    echo "7. DIAGNÓSTICO FINAL:\n";
    echo "✅ La columna 'stage' existe y es funcional\n";
    echo "✅ Se pueden hacer UPDATE con stage\n";
    echo "✅ Se pueden hacer INSERT con stage\n";
    echo "\n";
    echo "🔍 SI EL STAGE NO SE GUARDA DESDE LA APLICACIÓN:\n";
    echo "   - Verificar que los datos lleguen al controlador\n";
    echo "   - Revisar logs de error de PHP/Symfony\n";
    echo "   - Verificar que no haya excepciones silenciosas\n";
    echo "   - Comprobar que el flush() se ejecute correctamente\n";
    echo "\n";
    echo "📝 PRÓXIMOS PASOS:\n";
    echo "   1. Revisar logs: tail -f /var/log/apache2/error.log\n";
    echo "   2. Probar crear una venta con graduación\n";
    echo "   3. Verificar que aparezcan los logs de debug\n";
    echo "   4. Comprobar si hay errores de validación\n";
    
} catch (PDOException $e) {
    echo "❌ Error de conexión: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== FIN DE LA PRUEBA ===\n";
?>
