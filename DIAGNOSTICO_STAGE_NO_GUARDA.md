# 🔍 DIAGNÓSTICO: Stage No Se Guarda en Base de Datos

## 🎯 Problema Actual

A pesar de haber implementado:
- ✅ Select de etapas en el modal
- ✅ Envío del stage en AJAX
- ✅ Procesamiento del stage en ambos controladores
- ✅ Logs de debug agregados

**El stage aún no se guarda en la base de datos.**

## 🛠️ Herramientas de Diagnóstico Creadas

### **1. Logs de Debug Agregados**

#### **En JavaScript (nueva-venta.html.twig):**
```javascript
console.log("=== ENVIANDO GRADUACION ===");
console.log("Producto ID:", idProducto);
console.log("Stage enviado:", graduacionData.stage);
console.log("Datos completos:", graduacionData);
```

#### **En PHP (VentasController.php):**
```php
error_log("=== AGREGAR GRADUACION DEBUG ===");
error_log("Datos recibidos: " . print_r($data, true));
error_log("Stage recibido: " . ($data['stage'] ?? 'NO DEFINIDO'));
error_log("Estableciendo stage: " . $stageValue);
error_log("Stage establecido en orden: " . $orden->getStage());
error_log("Orden guardada con ID: " . $orden->getIdordenlaboratorio());
error_log("Stage final en BD: " . $orden->getStage());
```

### **2. Endpoint de Prueba**
- **URL**: `/test-stage`
- **Función**: Probar directamente el guardado del stage
- **Uso**: Enviar POST con `{"stage": "9"}` o `{"stage": "10"}`

### **3. Página de Prueba HTML**
- **Archivo**: `public/test-stage.html`
- **URL**: `http://localhost/test-stage.html`
- **Funciones**:
  - Prueba directa de stage
  - Simulación de graduación completa
  - Logs en tiempo real

### **4. Scripts de Verificación**
- `test_stage_directo.php` - Prueba directa con PDO
- `test_stage_symfony.php` - Prueba con Symfony/Doctrine
- `test_stage_guardado.php` - Verificación de datos guardados

## 🔍 Pasos de Diagnóstico

### **Paso 1: Verificar Logs en Tiempo Real**
```bash
# Terminal 1: Logs de Symfony
tail -f var/log/dev.log

# Terminal 2: Crear una venta con graduación
# Ir a Nueva Venta → Agregar armazón → Graduación → Guardar
```

### **Paso 2: Usar la Página de Prueba**
1. Abrir: `http://localhost/test-stage.html`
2. Hacer clic en "Probar Stage"
3. Revisar logs en consola y servidor
4. Probar "Simular Graduación Completa"

### **Paso 3: Verificar Datos en BD**
```sql
-- Ver órdenes más recientes
SELECT 
    idordenLaboratorio, 
    stage, 
    actualizacion,
    status
FROM ordenLaboratorio 
ORDER BY actualizacion DESC 
LIMIT 5;

-- Ver si hay órdenes con stage 9 o 10
SELECT COUNT(*) as total, stage 
FROM ordenLaboratorio 
WHERE status = '1' 
GROUP BY stage;
```

## 🚨 Posibles Causas del Problema

### **1. Datos No Llegan al Controlador**
- **Síntoma**: No aparecen logs de "AGREGAR GRADUACION DEBUG"
- **Causa**: Error en el AJAX o ruta incorrecta
- **Solución**: Verificar consola del navegador

### **2. Excepción Silenciosa**
- **Síntoma**: Logs aparecen pero se cortan abruptamente
- **Causa**: Error de validación o constraint de BD
- **Solución**: Revisar logs de error completos

### **3. Transacción No Commitea**
- **Síntoma**: Logs muestran stage correcto pero no se guarda
- **Causa**: Error después del flush() o rollback automático
- **Solución**: Verificar que no hay errores después del flush

### **4. Problema de Entidad/Mapping**
- **Síntoma**: Error al llamar setStage()
- **Causa**: Problema en la definición de la entidad
- **Solución**: Verificar que la entidad esté bien mapeada

### **5. Constraint de Base de Datos**
- **Síntoma**: Error al hacer flush()
- **Causa**: Valor de stage no válido según constraint
- **Solución**: Verificar definición de columna en BD

## 📋 Checklist de Verificación

### **Frontend (JavaScript)**
- [ ] El select de stage aparece en el modal
- [ ] El valor se captura correctamente en `graduacionesPendientes`
- [ ] Los logs de consola muestran el stage enviado
- [ ] El AJAX se ejecuta sin errores

### **Backend (PHP)**
- [ ] Los logs muestran "AGREGAR GRADUACION DEBUG"
- [ ] El stage se recibe correctamente en `$data['stage']`
- [ ] El método `setStage()` se ejecuta sin errores
- [ ] El `flush()` se ejecuta sin excepciones

### **Base de Datos**
- [ ] La columna `stage` existe y es del tipo correcto
- [ ] No hay constraints que impidan los valores '9' y '10'
- [ ] Las órdenes se crean/actualizan correctamente
- [ ] El campo `stage` se actualiza en la tabla

## 🎯 Próximos Pasos

### **1. Ejecutar Diagnóstico Inmediato**
```bash
# Abrir logs
tail -f var/log/dev.log

# En otra terminal, abrir la página de prueba
# http://localhost/test-stage.html

# Probar ambas funciones y revisar logs
```

### **2. Si No Aparecen Logs**
- Verificar que la aplicación esté en modo debug
- Comprobar permisos de escritura en `var/log/`
- Revisar configuración de logging en Symfony

### **3. Si Aparecen Logs Pero No Se Guarda**
- Verificar que no hay excepciones después del flush
- Comprobar constraints de la tabla `ordenLaboratorio`
- Revisar si hay triggers o procedimientos que interfieran

### **4. Si Todo Parece Correcto**
- Verificar que estamos consultando la BD correcta
- Comprobar que no hay cache de entidades
- Revisar configuración de Doctrine

## 💡 Comando de Diagnóstico Rápido

```bash
# Crear venta de prueba y revisar logs simultáneamente
tail -f var/log/dev.log | grep -E "(AGREGAR GRADUACION|Stage|stage|GUARDAR VENTA)"
```

## 🎉 Confirmación de Éxito

Sabremos que el problema está resuelto cuando:
1. ✅ Los logs muestran el stage siendo procesado
2. ✅ No hay errores en el flush()
3. ✅ La consulta SQL muestra el stage guardado
4. ✅ La página de prueba confirma el guardado

**¡Con estas herramientas deberíamos poder identificar exactamente dónde está fallando el proceso!** 🔍
