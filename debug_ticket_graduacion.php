<?php
/**
 * Script de debug para verificar por qué no aparece el ticket de graduación
 */

// Configuración de la base de datos (ajusta según tu configuración)
$host = 'localhost';
$dbname = 'pv360'; // Ajusta el nombre de tu base de datos
$username = 'root'; // Ajusta tu usuario
$password = ''; // Ajusta tu contraseña

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== DEBUG: TICKET DE GRADUACIÓN NO APARECE ===\n\n";
    
    // 1. Verificar ventas recientes
    echo "1. VENTAS RECIENTES (últimas 10):\n";
    $stmt = $pdo->prepare("
        SELECT 
            v.idventa,
            v.folio,
            v.creacion,
            v.ticketgraduacion,
            v.status,
            u.nombreVendedor as vendedor
        FROM venta v
        LEFT JOIN usuario u ON v.usuario_idusuario = u.idusuario
        WHERE v.status = '1'
        ORDER BY v.creacion DESC
        LIMIT 10
    ");
    $stmt->execute();
    $ventas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($ventas as $venta) {
        $ticketGrad = $venta['ticketgraduacion'] ? '✅ SÍ' : '❌ NO';
        echo "  - Venta {$venta['idventa']} (Folio: {$venta['folio']}) - Ticket Graduación: {$ticketGrad}\n";
        echo "    Vendedor: {$venta['vendedor']}, Fecha: {$venta['creacion']}\n\n";
    }
    
    // 2. Verificar stockventa de ventas recientes
    echo "2. STOCKVENTA CON ÓRDENES DE LABORATORIO:\n";
    $stmt = $pdo->prepare("
        SELECT 
            v.folio,
            sv.idstockventa,
            p.modelo as producto,
            svol.idstockventaordenlaboratorio,
            ol.idordenLaboratorio,
            ol.stage,
            ol.creacion as orden_creacion
        FROM venta v
        INNER JOIN stockVenta sv ON v.idventa = sv.venta_idventa
        INNER JOIN stock s ON sv.stock_idstock = s.idstock
        INNER JOIN producto p ON s.producto_idproducto = p.idproducto
        LEFT JOIN stockVentaOrdenLaboratorio svol ON sv.idstockventa = svol.stockVenta_idstockVenta
        LEFT JOIN ordenLaboratorio ol ON svol.ordenLaboratorio_idordenLaboratorio = ol.idordenLaboratorio
        WHERE v.status = '1' AND v.creacion >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY v.creacion DESC, sv.idstockventa
        LIMIT 20
    ");
    $stmt->execute();
    $stockventas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($stockventas) > 0) {
        foreach ($stockventas as $sv) {
            $tieneOrden = $sv['idordenLaboratorio'] ? '✅ SÍ' : '❌ NO';
            $stage = $sv['stage'] ? "Stage: {$sv['stage']}" : 'Sin stage';
            echo "  - Folio: {$sv['folio']}, Producto: {$sv['producto']}\n";
            echo "    StockVenta ID: {$sv['idstockventa']}, Tiene Orden: {$tieneOrden}\n";
            if ($sv['idordenLaboratorio']) {
                echo "    Orden ID: {$sv['idordenLaboratorio']}, {$stage}, Creada: {$sv['orden_creacion']}\n";
            }
            echo "\n";
        }
    } else {
        echo "  ❌ No se encontraron stockventas recientes\n\n";
    }
    
    // 3. Verificar graduaciones pendientes en memoria (simulación)
    echo "3. VERIFICACIÓN DE LÓGICA DE TICKET:\n";
    
    // Simular la lógica del controlador
    $ventasConTicketGraduacion = 0;
    $ventasSinTicketGraduacion = 0;
    
    foreach ($ventas as $venta) {
        // Verificar si la venta tiene stockventaordenlaboratorio
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM stockVenta sv
            INNER JOIN stockVentaOrdenLaboratorio svol ON sv.idstockventa = svol.stockVenta_idstockVenta
            WHERE sv.venta_idventa = :ventaId AND sv.status = '1'
        ");
        $stmt->execute(['ventaId' => $venta['idventa']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            $ventasConTicketGraduacion++;
            echo "  ✅ Venta {$venta['folio']} DEBERÍA tener ticket de graduación ({$result['count']} órdenes)\n";
        } else {
            $ventasSinTicketGraduacion++;
            echo "  ❌ Venta {$venta['folio']} NO debería tener ticket de graduación\n";
        }
    }
    
    echo "\nRESUMEN:\n";
    echo "- Ventas que DEBERÍAN tener ticket: {$ventasConTicketGraduacion}\n";
    echo "- Ventas que NO deberían tener ticket: {$ventasSinTicketGraduacion}\n\n";
    
    // 4. Verificar archivos de ticket en el sistema de archivos
    echo "4. VERIFICACIÓN DE ARCHIVOS DE TICKET:\n";
    $carpetaTickets = '/ruta/a/tickets'; // Ajusta esta ruta
    
    foreach ($ventas as $venta) {
        if ($venta['ticketgraduacion']) {
            $archivoTicket = $carpetaTickets . '/' . $venta['ticketgraduacion'];
            $existe = file_exists($archivoTicket) ? '✅ Existe' : '❌ No existe';
            echo "  - Venta {$venta['folio']}: {$venta['ticketgraduacion']} - {$existe}\n";
        }
    }
    
    // 5. Diagnóstico del problema
    echo "\n5. DIAGNÓSTICO DEL PROBLEMA:\n";
    
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(DISTINCT v.idventa) as total_ventas,
            COUNT(DISTINCT CASE WHEN svol.idstockventaordenlaboratorio IS NOT NULL THEN v.idventa END) as ventas_con_orden,
            COUNT(DISTINCT CASE WHEN v.ticketgraduacion IS NOT NULL AND v.ticketgraduacion != '' THEN v.idventa END) as ventas_con_ticket
        FROM venta v
        LEFT JOIN stockVenta sv ON v.idventa = sv.venta_idventa AND sv.status = '1'
        LEFT JOIN stockVentaOrdenLaboratorio svol ON sv.idstockventa = svol.stockVenta_idstockVenta
        WHERE v.status = '1' AND v.creacion >= DATE_SUB(NOW(), INTERVAL 7 DAYS)
    ");
    $stmt->execute();
    $diagnostico = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "En los últimos 7 días:\n";
    echo "- Total de ventas: {$diagnostico['total_ventas']}\n";
    echo "- Ventas con órdenes de laboratorio: {$diagnostico['ventas_con_orden']}\n";
    echo "- Ventas con ticket de graduación: {$diagnostico['ventas_con_ticket']}\n";
    
    $diferencia = $diagnostico['ventas_con_orden'] - $diagnostico['ventas_con_ticket'];
    if ($diferencia > 0) {
        echo "\n🚨 PROBLEMA DETECTADO: {$diferencia} ventas tienen órdenes pero NO tienen ticket de graduación\n";
        echo "Esto indica que la lógica de generación de tickets tiene un problema.\n";
    } else {
        echo "\n✅ No se detectaron problemas obvios en la generación de tickets.\n";
    }
    
    // 6. Sugerencias de solución
    echo "\n6. POSIBLES CAUSAS Y SOLUCIONES:\n";
    echo "a) La verificación \$tieneOrdenLab ocurre antes de crear las órdenes\n";
    echo "   → Solución: Mover la verificación después del procesamiento\n\n";
    echo "b) Las graduaciones no se están guardando correctamente\n";
    echo "   → Verificar que el stage se esté guardando en ordenLaboratorio\n\n";
    echo "c) Error en la generación del PDF del ticket\n";
    echo "   → Revisar logs de errores de PHP/Symfony\n\n";
    echo "d) Problema con permisos de archivos\n";
    echo "   → Verificar permisos de escritura en la carpeta de tickets\n\n";
    
} catch (PDOException $e) {
    echo "❌ Error de conexión a la base de datos: " . $e->getMessage() . "\n";
    echo "Por favor, ajusta la configuración de la base de datos en este script.\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== FIN DEL DEBUG ===\n";
?>
