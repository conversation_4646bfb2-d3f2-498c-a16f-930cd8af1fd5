<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Flujoexpediente
 *
 * @ORM\Table(name="flujoExpediente", indexes={@ORM\Index(name="fk_flujoExpediente_cliente1_idx", columns={"cliente_idcliente"}), @ORM\Index(name="fk_flujoExpediente_graduacion1_idx", columns={"graduacion_idgraduacion"}), @ORM\Index(name="fk_flujoExpediente_venta1_idx", columns={"venta_idventa"}), @ORM\Index(name="fk_flujoExpediente_usuario1_idx", columns={"usuario_idusuario"}), @ORM\Index(name="fk_flujoExpediente_sucursal1_idx", columns={"sucursal_idsucursal"})})
 * @ORM\Entity
 */
class Flujoexpediente
{
    /**
     * @var int
     *
     * @ORM\Column(name="idflujoExpediente", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idflujoexpediente;

    /**
     * @var string
     *
     * @ORM\Column(name="etapa", type="string", length=45, nullable=false)
     */
    private $etapa;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=false)
     */
    private $creacion;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="actualizacion", type="datetime", nullable=false)
     */
    private $actualizacion;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="documentoExpedienteClinico", type="string", length=150, nullable=true)
     */
    private $documentoexpedienteclinico;

    /**
     * @var string|null
     *
     * @ORM\Column(name="documentoExpedienteClinicoFirmado1", type="string", length=150, nullable=true)
     */
    private $documentoexpedienteclinicofirmado1;

    /**
     * @var string|null
     *
     * @ORM\Column(name="documentoExpedienteClinicoFirmado2", type="string", length=150, nullable=true)
     */
    private $documentoexpedienteclinicofirmado2;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaTerminoFlujo", type="datetime", nullable=true)
     */
    private $fechaterminoflujo;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaEntrega", type="datetime", nullable=true)
     */
    private $fechaentrega;

    /**
     * @var string|null
     *
     * @ORM\Column(name="observaciones", type="text", length=16777215, nullable=true)
     */
    private $observaciones;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="authorizationDate", type="datetime", nullable=true)
     */
    private $authorizationdate;

    /**
     * @var \Cliente
     *
     * @ORM\ManyToOne(targetEntity="Cliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cliente_idcliente", referencedColumnName="idcliente")
     * })
     */
    private $clienteIdcliente;

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_idventa", referencedColumnName="idventa")
     * })
     */
    private $ventaIdventa;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    /**
     * @var \Graduacion
     *
     * @ORM\ManyToOne(targetEntity="Graduacion")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="graduacion_idgraduacion", referencedColumnName="idgraduacion")
     * })
     */
    private $graduacionIdgraduacion;

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sucursal_idsucursal", referencedColumnName="idsucursal")
     * })
     */
    private $sucursalIdsucursal;

    public function getIdflujoexpediente(): ?int
    {
        return $this->idflujoexpediente;
    }

    public function getEtapa(): ?string
    {
        return $this->etapa;
    }

    public function setEtapa(string $etapa): self
    {
        $this->etapa = $etapa;

        return $this;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getActualizacion(): ?\DateTimeInterface
    {
        return $this->actualizacion;
    }

    public function setActualizacion(\DateTimeInterface $actualizacion): self
    {
        $this->actualizacion = $actualizacion;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDocumentoexpedienteclinico(): ?string
    {
        return $this->documentoexpedienteclinico;
    }

    public function setDocumentoexpedienteclinico(?string $documentoexpedienteclinico): self
    {
        $this->documentoexpedienteclinico = $documentoexpedienteclinico;

        return $this;
    }

    public function getDocumentoexpedienteclinicofirmado1(): ?string
    {
        return $this->documentoexpedienteclinicofirmado1;
    }

    public function setDocumentoexpedienteclinicofirmado1(?string $documentoexpedienteclinicofirmado1): self
    {
        $this->documentoexpedienteclinicofirmado1 = $documentoexpedienteclinicofirmado1;

        return $this;
    }

    public function getDocumentoexpedienteclinicofirmado2(): ?string
    {
        return $this->documentoexpedienteclinicofirmado2;
    }

    public function setDocumentoexpedienteclinicofirmado2(?string $documentoexpedienteclinicofirmado2): self
    {
        $this->documentoexpedienteclinicofirmado2 = $documentoexpedienteclinicofirmado2;

        return $this;
    }

    public function getFechaterminoflujo(): ?\DateTimeInterface
    {
        return $this->fechaterminoflujo;
    }

    public function setFechaterminoflujo(?\DateTimeInterface $fechaterminoflujo): self
    {
        $this->fechaterminoflujo = $fechaterminoflujo;

        return $this;
    }

    public function getFechaentrega(): ?\DateTimeInterface
    {
        return $this->fechaentrega;
    }

    public function setFechaentrega(?\DateTimeInterface $fechaentrega): self
    {
        $this->fechaentrega = $fechaentrega;

        return $this;
    }

    public function getObservaciones(): ?string
    {
        return $this->observaciones;
    }

    public function setObservaciones(?string $observaciones): self
    {
        $this->observaciones = $observaciones;

        return $this;
    }

    public function getAuthorizationdate(): ?\DateTimeInterface
    {
        return $this->authorizationdate;
    }

    public function setAuthorizationdate(?\DateTimeInterface $authorizationdate): self
    {
        $this->authorizationdate = $authorizationdate;

        return $this;
    }

    public function getClienteIdcliente(): ?Cliente
    {
        return $this->clienteIdcliente;
    }

    public function setClienteIdcliente(?Cliente $clienteIdcliente): self
    {
        $this->clienteIdcliente = $clienteIdcliente;

        return $this;
    }

    public function getVentaIdventa(): ?Venta
    {
        return $this->ventaIdventa;
    }

    public function setVentaIdventa(?Venta $ventaIdventa): self
    {
        $this->ventaIdventa = $ventaIdventa;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }

    public function getGraduacionIdgraduacion(): ?Graduacion
    {
        return $this->graduacionIdgraduacion;
    }

    public function setGraduacionIdgraduacion(?Graduacion $graduacionIdgraduacion): self
    {
        $this->graduacionIdgraduacion = $graduacionIdgraduacion;

        return $this;
    }

    public function getSucursalIdsucursal(): ?Sucursal
    {
        return $this->sucursalIdsucursal;
    }

    public function setSucursalIdsucursal(?Sucursal $sucursalIdsucursal): self
    {
        $this->sucursalIdsucursal = $sucursalIdsucursal;

        return $this;
    }

    public function __toString() {
        return $this->etapa;
    }
    public function getNombreCompletoUsuario() {
        
        $usuario = $this->getUsuarioIdusuario();        
        if (!$usuario) {
            return null;
        }
    
        if($usuario->getPuesto() !== 'Optometrista') {
            return null; // o return ''; según lo que prefieras
        }
    
        $nombre = $usuario->getNombre();
        $apellidoPaterno = $usuario->getApellidopaterno();
        $apellidoMaterno = $usuario->getApellidomaterno();
    
        return "$nombre $apellidoPaterno $apellidoMaterno";
    }

    /**
     * Retorna el nombre completo del cliente asociado.
     *
     * @return string
     */
    public function getNombreCompleto(): string
    {
        return $this->clienteIdcliente ? $this->clienteIdcliente->getFullName() : '';
    }

}
