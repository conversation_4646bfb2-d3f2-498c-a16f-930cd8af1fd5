<?php

namespace App\Controller\Api\AppOptimo\SuperAdmin;

use App\DTO\EventsGetRequest;
use App\Entity\Sucursal;
use App\Enum\ErrorCodes\AppOptimo\EventsErrorCodes;
use App\Enum\Status;
use App\Service\ErrorResponseService;
use App\Service\ImagePathService;
use App\Service\RequestValidatorService;
use DateTime;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\ORM\Query;

/**
 * @Route("/cliente-api", name="api_")
 */
class CalendarController extends AbstractController
{
    private EntityManagerInterface $em;
    private ErrorResponseService $errorResponseService;

    public function __construct(
        EntityManagerInterface $em,
        ErrorResponseService $errorResponseService
    ) {
        $this->errorResponseService = $errorResponseService;
        $this->em = $em;
    }

    /**
     * @Route("/get-eventos", name="api-get-eventos", methods={"GET"})
     */
    public function getEvents(Request $request): JsonResponse
    {
        $eventsGetRequest = json_decode($request->getContent(), true);

        $startDate = new DateTime($eventsGetRequest['startDate']);

        $qb = $this->em->createQueryBuilder();
        $qb->select('s')
            ->from(Sucursal::class, 's')
            ->where('s.startdate >= :startdate')
            ->andWhere('s.status = 1')
            ->setParameter('startdate', $startDate)
            ->orderBy('s.startdate', 'ASC');

        $sucursales = $qb->getQuery()->getResult();

        if (!$sucursales) {
            return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_NO_EVENTS_FOUND,
            [
                'startdate' => $startDate,
            ]);
        }

        $response = array_map(fn($sucursal) => $this->mapEventToArray($sucursal), $sucursales);
        $inProgress = $this->inProgressEvents($response);
        $upcoming = $this->upcomingEvents($response);
        $closingSoonEvents = $this->closingSoonEvents($response);
        $ended = $this->endedEvents($response);

        return new JsonResponse([
            'events' => $response,
            'upcoming' => $upcoming,
            'inProgress' => $inProgress,
            'closingSoonEvents' => $closingSoonEvents,
            'ended' => $ended,
            'today' => (new DateTime())->format('Y-m-d'),
            'code' => 200,
        ], 200);
    }


    private function mapEventToArray($sucursal): array
    {
        return [
            'id' => $sucursal->getIdsucursal(),
            'nombre' => $sucursal->getNombre(),
            'start_date' => $sucursal->getStartdate()->format('Y-m-d'),
            'end_date' => $sucursal->getEnddate()->format('Y-m-d'),

        ];
    }

    private function inProgressEvents( $sucursales)
    {
        $today = new DateTime();
        $counter = 0;
        foreach ($sucursales as $sucursal) {
            $startDate = new \DateTime($sucursal['start_date']);
            $endDate   = new \DateTime($sucursal['end_date']);
            $includeEvent = true;

            if ( $startDate > $today || $today > $endDate ) {

                $includeEvent = false;
            }
            if ( $includeEvent ) {
                $counter += 1;
            }
        }

        return $counter;
    }

    private function upcomingEvents( $events)
    {
        $today = new DateTime();

        $counter = 0;
        foreach ($events as $event) {
            $startDate = new \DateTime($event['start_date']);
            if ( $startDate > $today ) {
                $counter += 1;
            }

        }

        return $counter;
    }

    private function closingSoonEvents($events)
    {
        $today = new DateTime();
        $limit = (clone $today)->add(new \DateInterval('P2D'));

        $counter = 0;
        foreach ($events as $event) {

            $endDate   = new \DateTime($event['end_date']);
            if ($today <= $endDate && $endDate <= $limit) {
                $counter++;
            }
        }

        return $counter;
    }

    private function endedEvents($events)
    {
        $today = new DateTime();
        $counter = 0;

        foreach ($events as $event) {
            $endDate   = new \DateTime($event['end_date']);
            if ( $endDate < $today ) {
                $counter += 1;
            }
        }

        return $counter;
    }
}