<?php

namespace App\Controller;

use App\Entity\Stockventa;
use App\Entity\Stockventaordenlaboratorio;
use App\Entity\Cupon;
use App\Entity\Material;
use App\Entity\Pago;
use App\Entity\Stock;
use App\Entity\Ventacupon;
use App\Entity\Tipoventa;
use App\Entity\Cliente;
use App\Entity\Empresa;
use App\Entity\Salelog;
use App\Entity\Empresacliente;
use App\Entity\Sellreference;
use App\Entity\Beneficiario;
use App\Entity\Stockstate;
use App\Entity\Paymenttype;
use App\Entity\Beneficiarioventa;
use Dompdf\Exception;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Mike42\Escpos\PrintConnectors\FilePrintConnector;
use Mike42\Escpos\PrintConnectors\WindowsPrintConnector;
use Mike42\Escpos\Printer;
use Mike42\Escpos\EscposImage;
use App\Entity\Sucursal;
use App\Entity\Venta;
use App\Entity\Productoventa;
use App\Entity\Usuario;
use App\Entity\Documentoventa;
use App\Entity\Tratamiento;
use App\Entity\Tratamientoventa;
use App\Entity\Producto;
use App\Entity\Stockapartado;
use App\Entity\Unidad;
use App\Entity\Authstage;
use App\Entity\Usuarioempresapermiso;
use App\Form\ClienteType;
use App\Form\BeneficiarioType;
use App\Form\ClientSaleType;
use Dompdf\Dompdf;
use Dompdf\Options;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use App\Service\PagoEraser;
use App\Service\MailService;
use App\Service\TwilioService;
use App\Entity\Graduacion;
use App\Entity\Ordenlaboratorio;


/**
 * @Route("/venta", name="")
 */
class VentasController extends AbstractController
{
    private $logger;
    private $mailService;
    private $twilioService;


    public function __construct(MailerInterface $mailer, LoggerInterface $logger, MailService $mailService, TwilioService $twilioService)
    {
        $this->logger = $logger;
        $this->mailService = $mailService;
        $this->twilioService = $twilioService;
    }


    /**
     * @Route("/nueva-venta/{folio}", name="nueva-venta")
     */
    public function nuevaVenta(Request $request, $folio = "")
    {

        $Cliente = new Cliente();

        $Venta = null;
        $pagos = [];
        $Ventacupon = [];
        $productos = [];
        $cupon = null;
        $beneficiaries = null;
        $idempresa = intval($request->get("idempresa"));

        $form = $this->createForm(ClienteType::class, $Cliente);
        $form->handleRequest($request);

        $em = $this->getDoctrine()->getManager();

        $User = $this->getUser();
        $idsucursal = $User->getSucursalIdsucursal()->getIdsucursal();
        //buscamos los campos de la uam
        $unidades = $this->getDoctrine()->getRepository(Unidad::class)->findBy(['status' => "1"]);

        $tiposVenta = [];

        $Usuario = $this->getUser();
        $Sucursal = $Usuario->getSucursalIdsucursal();
        $Empresa = $Sucursal->getEmpresaIdempresa();

        $query = $em->createQuery(
            'SELECT u.nombre, u.apellidopaterno,u.idusuario
               FROM App\Entity\Usuario u
               inner join u.sucursalIdsucursal s
               where u.status =:status and s.idsucursal=:idsucursal or u.usuario =:usr
               '
        )->setParameters(['status' => "1", 'idsucursal' => $idsucursal, 'usr' => 'dios']);
        $usuarios = $query->getResult();

        $empresaId = $Empresa ? $Empresa->getIdempresa() : null;
        $query = $em->createQuery(
            'SELECT tv
               FROM App\Entity\Tipoventa tv
               inner join tv.empresaIdempresa e
               where tv.status =:status and e.idempresa=:idempresa order by tv.nombre
               '
        )->setParameters(['status' => "1", 'idempresa' => $empresaId]);
        $tiposVenta = $query->getResult();

        $query = $em->createQuery(
            'SELECT sr.name, sr.idsellreference
               FROM App\Entity\Sellreference sr
               WHERE sr.status =:status
               ORDER BY sr.name ASC
               '
        )->setParameters(['status' => "1"]);
        $sellReferences = $query->getResult();

        $query = $em->createQuery(
            'SELECT pt.idpaymenttype, pt.name
               FROM App\Entity\Paymenttype pt
               WHERE pt.status =:status
               ORDER BY pt.name ASC
               '
        )->setParameters(['status' => "1"]);
        $paymentTypes = $query->getResult();


        if ($Usuario->getRol() == "ROLE_VENDEDOR" || $idempresa == 0) {
            $idempresa = $Empresa ? $Empresa->getIdempresa() : null;
        }
        //else if($idempresa == -1) throw new \Exception('No se seleccionó una empresa');

        $query = $em->createQuery(
            'SELECT e.nombre as nombre, e.idempresa as id
               FROM App\Entity\Usuarioempresapermiso up
               inner join up.empresaIdempresa e
               where up.status =:status and up.usuarioIdusuario =:idusuario
               '
        )->setParameters(['status' => "1", 'idusuario' => $Usuario->getIdusuario()]);
        $permisos = $query->getResult();


        if ($folio != "" and $idsucursal != "") {
            /*   var_dump($folio);
        var_dump($idsucursal);
        var_dump($idempresa);*/
            if ($folio != "" and $idsucursal != "") {

                $query = $em->createQuery(
                    'SELECT v.idventa, v.fecha, v.total, v.porcentajeiva, v.iva, 
                v.descuento, v.pagado, v.folio, v.ticketpdf, v.cotizacion, v.tipopago,
                v.sedescontodeinventario, v.beneficiario as beneficiarioNombre,
                v.pidiofactura, v.convenio, v.seapartoarmazon, v.fechacreacion, 
                v.fechaventa, v.fechaactualizacion, v.porquesecancelo, v.fechacancelacion,
                v.beneficiario, v.diascredito, c.authorizedcredit, c.debt,
                c.idcliente, c.nombre as nombreCliente, c.apellidopaterno as 
                clienteApellidopaterno,c.apellidomaterno as clienteApellidomaterno,  
                c.telefono, c.email, c.numeroempleado, sr.idsellreference as comonosconocio, s.idsucursal, 
                s.nombre as sucursal, s.porcentajeiva, tv.idtipoventa, tv.nombre as tipoventa, tv.totalventaconiva,
                uni.idunidad, uni.nombre as unidad, u.idusuario, u.nombre as vendedorNombre,
                 u.apellidopaterno as vendedorApellidopaterno, g.idusuario as gerente, 
                 v.folio, v.cotizacion, ec.nombre as nombreEmpresa, ec.idempresacliente,
                v.archivoautorizacion,v.notas, v.credito, vg.folio as ventaGarantiaFolio, v.authorizationnumber, v.authscan
                FROM App\Entity\Venta v
                inner join v.clienteIdcliente c
                left join c.sellreferenceIdsellreference sr
                left join v.ventaGarantia vg
                left join c.empresaclienteIdempresacliente ec 
                inner join v.sucursalIdsucursal s
                INNER JOIN s.empresaIdempresa e
                left join v.tipoventaIdtipoventa tv
                inner join v.gerente g
                inner join v.usuarioIdusuario u
                left join v.unidadIdunidad uni
                where v.folio =:folio and e.idempresa = :idempresa and v.status =:status
                '
                )->setParameters(['folio' => $folio, 'idempresa' => $idempresa, 'status' => '1',]);
                $Venta = $query->getOneOrNullResult();

                /*  var_dump($Venta);*/
                if ($Venta) {

                    $Venta["notas"] = $this->sanitizeInput($Venta["notas"]);

                    $query = $em->createQuery(
                        'SELECT sv.idstockventa, sv.cantidad, s.cantidad as cantidadMax,
                    sv.precio, sv.porcentajedescuento, sv.fixproduct, sv.isomittable,
                    sv.preciofinal, s.idstock, p.descripcion, v.descuento, 
                    p.nombre,p.idproducto, p.modelo, 
                    m.nombre as marca, p.tipoproducto, p.masivounico, cla.nombre AS categoria, 
                    s.codigobarras, uni.idunidad, uni.nombre as unidad, p.codigobarrasuniversal, p.codigo
                    FROM App\Entity\Stockventa sv
                    left join sv.stockIdstock s
                    left join s.productoIdproducto p
                    left join p.categoriaIdcategoria ca
                    left join ca.claseIdclase cla
                    inner join p.marcaIdmarca m
                    inner join sv.ventaIdventa v
                    inner join v.sucursalIdsucursal suc
                    INNER JOIN suc.empresaIdempresa e
                    left join v.unidadIdunidad uni
                    where sv.status=:status and v.folio=:folio and e.idempresa = :idempresa
                    '
                    )->setParameters(['status' => '1', 'folio' => $folio, 'idempresa' => $idempresa]);

                    $productos = $query->getArrayResult();


                    if ($productos) {
                        foreach ($productos as &$producto) {
                            $descuentoRestante = 1;
                            // Aseguramos que la descripción esté limpia
                            $producto["descripcion"] = $this->sanitizeInput($producto["descripcion"]);

                            // Regla 1: Si el precio final ha sido modificado, usar ese valor como precioModificado
                            if ($producto["preciofinal"] && $producto["preciofinal"] != $producto["precio"]) {
                                /*  if($producto['idstockventa'] =="27399"){
                                      echo "entra a 1";
                                  }*/
                                $producto["precioModificado"] = $producto["preciofinal"];
                            } // Regla 2: Si hay un descuento, aplicamos el descuento sobre el precio original
                            elseif ($producto["porcentajedescuento"] > 0) {
                                /* if($producto['idstockventa'] =="27399"){
                                     echo "entra a 2";
                                 }*/
                                $descuentoRestante = (100 - $producto["porcentajedescuento"]) / 100;
                                $producto["precioModificado"] = $producto["precio"] * $descuentoRestante;
                            } // Regla 3: Si no hay modificaciones ni descuentos, el precioModificado es el precio original
                            else {
                                /* if($producto['idstockventa'] =="27399"){
                                     echo "entra a 3";
                                 }*/
                                $producto["precioModificado"] = $producto["precio"];
                            }
                            /* if($producto['idstockventa'] =="27399"){
                                 echo "--".$producto["preciofinal"] ;
                                 echo "--precio ".$producto["precio"];
                                 echo "--descuento ".$producto["porcentajedescuento"];
                                 echo "--descuentoRestante ".$descuentoRestante;
                             }*/
                            // Determinamos el precio final siempre
                            $producto["preciofinal"] = ($producto["porcentajedescuento"] > 0) ?
                                ($producto["precio"] * $descuentoRestante) :
                                $producto["preciofinal"];
                            /*if($producto['idstockventa'] =="27399"){
                                echo "--".$producto["preciofinal"] ;
                            }*/
                        }
                    }


                    $query = $em->createQuery(
                        'SELECT  c.nombre, c.apellidopaterno, c.apellidomaterno, c.idcliente, c.beneficiarytype
                       FROM App\Entity\Beneficiarioventa bv
                       inner join bv.ventaIdventa v
                       inner join bv.clienteIdcliente c
                       where c.status = 1 and v.idventa =:idventa
                       '
                    )->setParameters(['idventa' => $Venta['idventa']]);
                    $beneficiaries = $query->getResult();


                    $query = $em->createQuery(
                        'SELECT p.monto, p.idpago, pt.idpaymenttype, pt.name as tipopago, COALESCE(p.automatic, 0) as isAutomatic
                    FROM App\Entity\Pago p
                    INNER JOIN p.paymenttypeIdpaymenttype pt
                    inner join  p.ventaIdventa v
                    inner join v.sucursalIdsucursal suc
                    INNER JOIN suc.empresaIdempresa e
                    where p.status=:status and v.folio=:folio and e.idempresa = :idempresa
                    '
                    )->setParameters(['status' => '1', 'folio' => $folio, 'idempresa' => $idempresa]);

                    $pagos = $query->getResult();

                    //  var_dump($pagos);
                    //buscaos el cupon si es que hay

                    $query = $em->createQuery(
                        'SELECT c.codigo, vc.porcentajedescuento, vc.idventacupon
                        FROM App\Entity\Ventacupon vc
                        inner join  vc.ventaIdventa v
                        inner join  vc.cuponIdcupon c
                        inner join v.sucursalIdsucursal suc
                        INNER JOIN suc.empresaIdempresa e
                        where vc.status=:status and v.folio=:folio and e.idempresa = :idempresa
                        '
                    )->setParameters(['status' => '1', 'folio' => $folio, 'idempresa' => $idempresa]);


                    $Ventacupon = $query->getResult();

                    if ($Ventacupon) {
                        $code = $Ventacupon[0]['codigo'];

                        $query = $em->createQuery(
                            'SELECT  cm.idcuponmarca,c.codigo,m.idmarca,
                            m.nombre,c.porcentajedescuento 
                           FROM App\Entity\Cuponmarca cm
                           INNER JOIN cm.cuponIdcupon c
                           LEFT JOIN  cm.marcaIdmarca m

                           where cm.status=:status and c.codigo=:codigo
                           '
                        )->setParameters(['status' => '1', 'codigo' => $code]);
                        $cupon = $query->getResult();

                        if (count($cupon) > 0) {
                            $cupon[0]['porcentajedescuento'] = $Ventacupon[0]['porcentajedescuento'];
                            $cupon[0]['idventacupon'] = $Ventacupon[0]['idventacupon'];
                            $cuponJson = json_encode($cupon[0]);
                        }
                    }
                }
            }
        } else
            $this->eraseSaleDocuments($Usuario->getIdusuario());

        $ventaLiquidada = false;
        if ($Venta && $Venta['total'] <= $Venta['pagado']) {
            $ventaLiquidada = true;
        }



        // --- MODIFICACIÓN: Siempre obtener graduaciones después de cualquier cambio de estado de venta/cotización ---
        // --- MODIFICACIÓN: Siempre obtener graduaciones, incluso si el estado no cambia ---
        $graduacionesData = [];
        if ($Venta) {
            $ventaEntity = $this->getDoctrine()->getRepository(Venta::class)->find($Venta['idventa']);
            if ($ventaEntity) {
                // Siempre obtener las graduaciones más recientes de la base de datos
                $graduacionesData = $this->getGraduacionData($ventaEntity);
                // Asociar graduación a cada producto en el array $productos
                if (!empty($productos)) {
                    foreach ($productos as &$producto) {
                        $producto['graduacion'] = null;
                        if (!empty($graduacionesData['graduaciones'])) {
                            foreach ($graduacionesData['graduaciones'] as $grad) {
                                if (
                                    (isset($grad['producto_id']) && (
                                        (isset($producto['idstockventa']) && $grad['producto_id'] == $producto['idstockventa']) ||
                                        (isset($producto['idproducto']) && $grad['producto_id'] == $producto['idproducto'])
                                    ))
                                ) {
                                    $producto['graduacion'] = $grad;
                                    break;
                                }
                            }
                        }
                    }
                    unset($producto);
                }
            }
        }

        return $this->render('ventas/nueva-venta.html.twig', [
            'unidades' => $unidades,
            'usuarios' => $usuarios,
            'tiposVenta' => $tiposVenta,
            'form' => $form->createView(),
            'venta' => $Venta,
            'pagos' => $pagos,
            'productos' => $productos,
            'cupon' => $cupon[0] ?? null,
            'cuponJson' => $cuponJson ?? null,
            'permisos' => $permisos,
            'idempresa' => $idempresa,
            'rol' => $Usuario->getRol(),
            'beneficiaries' => $beneficiaries,
            'sellReferences' => $sellReferences,
            'paymentTypes' => $paymentTypes,
            'ventaLiquidada' => $ventaLiquidada,
            'graduacionesData' => $graduacionesData
        ]);
    }

    function sanitizeInput($input)
    {
        // Eliminar espacios al principio y al final
        $input = trim($input);

        // Convertir caracteres especiales en entidades HTML para prevenir XSS
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');

        // Eliminar saltos de línea innecesarios y convertir CRLF a LF
        $input = str_replace(array("\r\n", "\r", "\n"), ' ', $input);

        // Eliminar múltiples espacios consecutivos
        $input = preg_replace('/\s+/', ' ', $input);

        return $input;
    }


    /**
     * @Route("/buscar-cliente", name="buscar-cliente")
     */
    public function buscarCliente(Request $request)
    {

        $em = $this->getDoctrine()->getManager();
        $parameters = array(
            'status' => '1'
        );

        $nombre = $request->get("nombre");
        $apellidop = $request->get("apellidop");
        $apellidom = $request->get("apellidom");
        $email = $request->get("email");
        $telefono = $request->get("telefono");
        $numeroempleado = $request->get("numeroempleado");


        $whereNombre = "";
        $whereEmail = "";
        $whereTelefono = "";
        $whereNumeroEmpleado = "";

        if ($nombre != '')
            $whereNombre .= " and c.nombre like '%" . $nombre . "%' ";
        if ($apellidop != '')
            $whereNombre .= " and c.apellidopaterno like '%" . $apellidop . "%' ";
        if ($apellidom != '')
            $whereNombre .= " and c.apellidomaterno like '%" . $apellidom . "%' ";
        if ($email != '')
            $whereEmail .= " and c.email like '%" . $email . "%' ";
        if ($telefono != '')
            $whereTelefono .= " and c.telefono like '%" . $telefono . "%' ";
        if ($numeroempleado != '')
            $whereNumeroEmpleado .= " and c.numeroempleado = '" . $numeroempleado . "' ";


        $query = $em->createQuery(
            'SELECT  c.idcliente as data,c.nombre as value, c.apellidopaterno as apellidopaterno, c.apellidomaterno as apellidomaterno, c.telefono as telefono, c.email as email, c.numeroempleado as numeroEmpleado, c.diascredito,
               ec.idempresacliente, ec.nombre as nombreempresa, c.authorizedcredit, c.debt, sr.idsellreference
               FROM App\Entity\Cliente c
               LEFT JOIN c.sellreferenceIdsellreference sr
               LEFT JOIN c.empresaclienteIdempresacliente ec

               where c.status =:status' . $whereTelefono . $whereEmail . $whereNombre . $whereNumeroEmpleado . ' order by c.apellidopaterno asc
               '
        )->setParameters($parameters);
        $query->setMaxResults(20);
        $Descripciones = $query->getResult();

        return $this->json(['suggestions' => $Descripciones]);
    }

    /**
     * @Route("/admin/app/ventafactura/list", name="ventafactura")
     */
    public function ventaFactura(Request $request)
    {
    }

    //Paso 4

    /**
     * @Route("/seleccionar-clientes", name="seleccionar-clientes")
     */
    public function seleccionarClientes(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $parameters = array('nombre' => "%" . $request->get('query') . "%");
        $query = $em->createQuery(
            'SELECT  c.idcliente as data,c.telefono, c.nombre ,c.email, c.nombre as value
            FROM App\Entity\Cliente c
            where c.nombre like :nombre
            '
        )->setParameters($parameters);
        $Clientes = $query->getResult();

        foreach ($Clientes as $key => &$cliente) {
            if ($cliente['telefono'] == null) {
                $cliente['telefono'] = "";
            }

            if ($cliente['email'] == null) {
                $cliente['email'] = "";
            }

            $cliente['value'] = $cliente['value'] . " / " . $cliente['email'] . " / " . $cliente['telefono'];
        }

        return $this->json(['suggestions' => $Clientes]);
    }

    /**
     * @Route("/seleccionar-email", name="seleccionar-email")
     */
    public function seleccionarEmail(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $parameters = array('email' => "%" . $request->get('query') . "%");
        $query = $em->createQuery(
            'SELECT  c.idcliente as data,c.telefono,  c.email ,c.nombre, c.email as value
            FROM App\Entity\Cliente c
            where c.email like :email
            '
        )->setParameters($parameters);
        $Clientes = $query->getResult();

        foreach ($Clientes as $key => &$cliente) {
            if ($cliente['telefono'] == null) {
                $cliente['telefono'] = "";
            }

            if ($cliente['value'] == null) {
                $cliente['value'] = "";
            }

            $cliente['value'] = $cliente['value'] . " / " . $cliente['nombre'] . " / " . $cliente['telefono'];
        }

        return $this->json(['suggestions' => $Clientes]);
    }

    /**
     * @Route("/seleccionar-telefono", name="seleccionar-telefono")
     */
    public function seleccionarTelefono(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $parameters = array('telefono' => "%" . $request->get('query') . "%");
        $query = $em->createQuery(
            'SELECT  c.idcliente as data,c.email, c.telefono  ,c.nombre, c.telefono as value
            FROM App\Entity\Cliente c
            where c.telefono like :telefono
            '
        )->setParameters($parameters);
        $Clientes = $query->getResult();

        foreach ($Clientes as $key => &$cliente) {
            if ($cliente['value'] == null) {
                $cliente['value'] = "";
            }

            if ($cliente['email'] == null) {
                $cliente['email'] = "";
            }

            $cliente['value'] = $cliente['value'] . " / " . $cliente['nombre'] . " / " . $cliente['email'];
        }

        return $this->json(['suggestions' => $Clientes]);
    }


    /**
     * @Route("/buscar-numero-cliente", name="buscar-numero-cliente")
     */
    public function buscarNumeroCliente(Request $request)
    {

        $em = $this->getDoctrine()->getManager();
        $parameters = array('numeroEmpleado' => "%" . $request->get('query') . "%");
        $query = $em->createQuery(
            'SELECT  c.idcliente as data,c.nombre,c.apellidopaterno, c.apellidomaterno ,c.numeroempleado as value,
               c.telefono, c.email, ec.idempresacliente, ec.nombre as enterpriseName
               FROM App\Entity\Cliente c
               LEFT JOIN c.empresaclienteIdempresacliente ec
               where c.numeroempleado like :numeroEmpleado
               '
        )->setParameters($parameters);
        $Clientes = $query->getResult();

        return $this->json(['suggestions' => $Clientes]);
    }

    /**
     * @Route("/buscar-empresa-cliente", name="buscar-empresa-cliente")
     */
    public function buscarEmpresaCliente(Request $request)
    {

        $em = $this->getDoctrine()->getManager();
        $parameters = array('empresaEmpleado' => "%" . $request->get('query') . "%");
        $query = $em->createQuery(
            'SELECT  ec.idempresacliente as data, ec.nombre as value
               FROM App\Entity\Empresacliente ec
               where ec.nombre like :empresaEmpleado
               '
        )->setParameters($parameters)->setMaxResults(10);
        $empresasClientes = $query->getResult();

        return $this->json(['suggestions' => $empresasClientes]);
    }

    /**
     * @Route("/search-client-email", name="venta-search-client-email")
     */
    public function searchClientEmail(Request $request)
    {

        $em = $this->getDoctrine()->getManager();
        $parameters = array('email' => "%" . $request->get('query') . "%");
        $query = $em->createQuery(
            'SELECT  c.idcliente as data, c.email as value, c.nombre, c.apellidopaterno,
            c.apellidomaterno, ec.idempresacliente, ec.nombre as enterpriseName, c.numeroempleado,
            c.telefono

            FROM App\Entity\Cliente c
            LEFT JOIN c.empresaclienteIdempresacliente ec
            where c.email like :email and c.status = 1
            '
        )->setParameters($parameters)->setMaxResults(10);
        $Emails = $query->getResult();

        return $this->json(['suggestions' => $Emails]);
    }

    /**
     * @Route("/search-client-phone", name="venta-search-client-phone")
     */
    public function searchClientPhone(Request $request)
    {

        $em = $this->getDoctrine()->getManager();
        $parameters = array('phone' => "%" . $request->get('query') . "%");
        $query = $em->createQuery(
            'SELECT  c.idcliente as data, c.telefono as value, c.nombre, c.apellidopaterno,
            c.apellidomaterno, ec.idempresacliente, ec.nombre as enterpriseName, c.numeroempleado,
            c.email

            FROM App\Entity\Cliente c
            LEFT JOIN c.empresaclienteIdempresacliente ec
            where c.telefono like :phone and c.status = 1
            '
        )->setParameters($parameters)->setMaxResults(10);
        $Phones = $query->getResult();

        return $this->json(['suggestions' => $Phones]);
    }

    /**
     * @Route("/buscar-beneficiarios", name="buscar-beneficiarios")
     */
    public function buscarBeneficiarios(Request $request)
    {

        $em = $this->getDoctrine()->getManager();
        $parameters = array('idcliente' => $request->get('idcliente'));

        $query = $em->createQuery(
            'SELECT  c.nombre, c.apellidopaterno, c.apellidomaterno, c.idcliente
               FROM App\Entity\Cliente c
               inner join c.holder h
               where c.status = 1 and h.idcliente =:idcliente
               '
        )->setParameters($parameters);
        $beneficiarios = $query->getResult();

        return $this->json($beneficiarios);
    }

    /**
     * @Route("/beneficiary-table", name="ventas-beneficiary-table")
     */
    public function beneficiaryTable(Request $request)
    {

        $em = $this->getDoctrine()->getManager();
        $parameters = array('idcliente' => $request->get('idclient'));

        $query = $em->createQuery(
            'SELECT  c.nombre, c.apellidopaterno, c.apellidomaterno, c.idcliente, c.beneficiarytype
               FROM App\Entity\Cliente c
               INNER JOIN c.holder h
               WHERE c.status = 1 and h.idcliente =:idcliente
               ORDER BY c.apellidopaterno DESC
               '
        )->setParameters($parameters);
        $beneficiaries = $query->getResult();

        return $this->render('ventas/ventas-beneficiary-table.html.twig', [
            'beneficiaries' => $beneficiaries,
        ]);
    }

    /**
     * @Route("/buscar-producto-codigo", name="buscar-producto-codigo")
     */
    public function buscarProductoCodigo(Request $request)
    {
        $User = $this->getUser();
        $exito = false;
        $msj = "";
        // Sacamos la sucursal a la que pertenece el usuario conectado
        $Sucursal = $User->getSucursalIdsucursal();
        //Creamos la variable em para tener entityManager
        $em = $this->getDoctrine()->getManager();

        $producto = null;
        try {
            //$parameters = array('codigobarrasuniversal' => "%" . $request->get('query') . "%", 'status' => "1", 'idsucursal' => $Sucursal->getIdSucursal());

            /*****************************/

            $parameters = array('barcode' => $request->get('query'), 'status' => "1", 'idsucursal' => $Sucursal->getIdSucursal());


            //Query donde sacamos los productos
            $query = $em->createQuery(
                'SELECT p.idproducto, s.codigobarras as value, p.nombre, p.precio, p.tipoproducto,
                p.idproducto, p.descripcion, m.nombre as marca, 
                c.nombre as categoria, cc.nombre as clase, s.cantidad, p.modelo, s.idstock as data,
                p.codigobarrasuniversal, p.masivounico, p.precioespecial, p.preciosubdistribuidor, 
                p.preciodistribuidor, s.precio as stockPrice, p.graduable
                FROM App\Entity\Stock s
                inner join s.productoIdproducto p
                inner join p.marcaIdmarca m
                inner join p.categoriaIdcategoria c
                inner join c.claseIdclase cc
                inner join s.sucursalIdsucursal sucursal
                where s.status=:status and sucursal.idsucursal=:idsucursal and (s.codigobarras = :barcode or p.codigobarrasuniversal = :barcode ) and s.cantidad>0 
            '
            )->setParameters($parameters);
            $productos = $query->getResult();


            $iva = floatval($Sucursal->getPorcentajeiva());

            if ($iva <= 0) {
                $iva = 16;
            }

            if ($productos) {
                //hay produyctos
                $producto = $productos[0];
                if (count($productos) == 1 && $producto['masivounico'] == "1" || $producto['masivounico'] == "2" && count($productos) > 0) {

                    // Revisamos que el producto tenga codigo si no no se muestra

                    $producto['precio'] = $producto['precio'] + ($producto['precio'] * (.01 * $iva));
                    $producto['precioespecial'] = $producto['precioespecial'] ? number_format((float)$producto['precioespecial'] + ($producto['precioespecial'] * (.01 * $iva)), 2, '.', '') : null;
                    $producto['preciosubdistribuidor'] = $producto['preciosubdistribuidor'] ? number_format((float)$producto['preciosubdistribuidor'] + ($producto['preciosubdistribuidor'] * (.01 * $iva)), 2, '.', '') : null;
                    $producto['preciodistribuidor'] = $producto['preciodistribuidor'] ? number_format((float)$producto['preciodistribuidor'] + ($producto['preciodistribuidor'] * (.01 * $iva)), 2, '.', '') : null;
                    //$producto['value'] = $producto['value'] . "-" . $producto['codigobarrasuniversal'];
                    // echo "<br>precio ".$producto['precio'];
                    // $total+=(float)$producto['total'];

                    if ($producto['marca'] == "Sin marca") {
                        $producto['marca'] = "";
                    }
                    $producto['nombre'] = $producto['marca'] . " / " . $producto['modelo'] . " / " . $producto['descripcion'] . " / " . $producto['value'] . " / " . $producto['codigobarrasuniversal'];
                    // $producto['value']=$producto['marca']." / ".$producto['modelo']." / ".$producto['descripcion']." / ".$producto['nombre'];

                    $exito = true;
                } else {
                    $msj = "Si es masivo revise existencias, si es único intente por SKU";
                }
            } else {
                $msj = "No hay productos";
            }
        } catch (\Exception $e) {
            $msj = $e->getMessage() . " linea " . $e->getLine() . " archivo " . $e->getFile();
        }


        return $this->json(['producto' => $producto, 'exito' => $exito, "msj" => $msj]);
    }

    /**
     * @Route("/buscar-producto-nombre", name="buscar-producto-nombre")
     */
    public function buscarProductoNombre(Request $request)
    {
        $User = $this->getUser();
        $productosFinal = [];
        ///sacamos la sucursal a la que pertenece el usuario conectado
        $Sucursal = $User->getSucursalIdsucursal();
        $Enterprise = $User->getSucursalIdsucursal()->getEmpresaIdempresa();
        $em = $this->getDoctrine()->getManager();

        $enterpriseId = $Enterprise ? $Enterprise->getIdempresa() : null;
        $query = $em->createQuery(
            'SELECT p.idproducto as data,p.codigo ,p.nombre as value,p.precio,p.descripcion, m.nombre as marca, c.nombre as categoria,p.modelo
                ,p.tipoproducto, p.precioespecial, p.preciosubdistribuidor, p.preciodistribuidor, p.codigobarrasuniversal
               FROM App\Entity\Producto p

                inner join p.marcaIdmarca m
                inner join p.categoriaIdcategoria c
                INNER JOIN c.claseIdclase cl
                INNER JOIN cl.empresaIdempresa e
               where p.status=:status and p.tipoproducto=:tipoproducto and (p.descripcion like :nombre  or p.modelo like :nombre or c.nombre  like :nombre) AND e.idempresa =:enterpriseId
                ORDER BY p.idproducto DESC
               '
        )->setParameters(['status' => '1', 'nombre' => "%" . $request->get('query') . "%", 'tipoproducto' => '2', 'enterpriseId' => $enterpriseId])->setMaxResults(10);
        $productos = $query->getResult();

        if ($productos) {
            foreach ($productos as $keyProducto => $producto) {
                $productosFinal[] = $producto;
            }
        }

        $iva = floatval($Sucursal->getPorcentajeiva());

        if ($iva <= 0) {
            $iva = 16;
        }

        foreach ($productosFinal as $key => $producto) {

            $productosFinal[$key]['precio'] = $productosFinal[$key]['precio'] * (1 + (.01 * $iva));
            $productosFinal[$key]['precioespecial'] = $productosFinal[$key]['precioespecial'] ? number_format((float)$productosFinal[$key]['precioespecial'] * (1 + (.01 * $iva)), 2, '.', '') : null;
            $productosFinal[$key]['preciosubdistribuidor'] = $productosFinal[$key]['preciosubdistribuidor'] ? number_format((float)$productosFinal[$key]['preciosubdistribuidor'] * (1 + (.01 * $iva)), 2, '.', '') : null;
            $productosFinal[$key]['preciodistribuidor'] = $productosFinal[$key]['preciodistribuidor'] ? number_format((float)$productosFinal[$key]['preciodistribuidor'] * (1 + (.01 * $iva)), 2, '.', '') : null;
            //$total+=(float)$producto['total'];

            if ($producto['marca'] == "Sin marca") {
                $producto['marca'] = "";
            }
            if ($producto['tipoproducto'] == "2") {
                $productosFinal[$key]['value'] = $producto['modelo'];
            } else {
                $productosFinal[$key]['value'] = $producto['marca'] . " / " . $producto['modelo'] . " / " . $producto['descripcion'];
            }
        }

        return $this->json(['suggestions' => $productosFinal]);
    }

    /**
     * @Route("/get-fixed-products", name="ventas-get-fixed-products")
     */
    public function getFixedProducts(Request $request)
    {

        $em = $this->getDoctrine()->getManager();
        $fixedProducts = $request->get('fixedProducts');
        $fixedProductsArray = explode(",", $fixedProducts);
        $User = $this->getUser();
        $Sucursal = $User->getSucursalIdsucursal();

        if (count($fixedProductsArray) > 0) {
            $whereProducts = "";
            for ($i = 0; $i < count($fixedProductsArray); $i++) {
                $productCode = trim($fixedProductsArray[$i]);
                if ($i == 0)
                    $whereProducts = " and ";
                $whereProducts .= "(p.codigo = '" . $productCode . "' or p.codigobarrasuniversal ='" . $productCode . "')";
                if ($i != count($fixedProductsArray) - 1)
                    $whereProducts .= " or ";
            }

            $query = $em->createQuery(
                'SELECT p.idproducto as data, p.codigo, p.codigobarrasuniversal, p.nombre as value, p.precio, p.descripcion, m.nombre as marca,
                c.nombre as categoria,p.modelo, p.tipoproducto, p.precioespecial, p.preciosubdistribuidor, p.preciodistribuidor
                FROM App\Entity\Producto p
                INNER JOIN p.marcaIdmarca m
                INNER JOIN p.categoriaIdcategoria c
                WHERE p.status=:status AND p.tipoproducto=:productType ' . $whereProducts
            )->setParameters(['status' => 1, 'productType' => 2]);
            $fixedProducsResults = $query->getResult();

            $iva = floatval($Sucursal->getPorcentajeiva());

            if ($iva <= 0)
                $iva = 16;

            foreach ($fixedProducsResults as $key => $tmpProduct) {
                $fixedProducsResults[$key]['precio'] = $tmpProduct['precio'] * (1 + (.01 * $iva));
                $fixedProducsResults[$key]['precioespecial'] = $tmpProduct['precioespecial'] ? number_format((float)$tmpProduct['precioespecial'] * (1 + (.01 * $iva)), 2, '.', '') : null;
                $fixedProducsResults[$key]['preciosubdistribuidor'] = $tmpProduct['preciosubdistribuidor'] ? number_format((float)$tmpProduct['preciosubdistribuidor'] * (1 + (.01 * $iva)), 2, '.', '') : null;
                $fixedProducsResults[$key]['preciodistribuidor'] = $tmpProduct['preciodistribuidor'] ? number_format((float)$tmpProduct['preciodistribuidor'] * (1 + (.01 * $iva)), 2, '.', '') : null;
            }
        }
        return $this->json(['fixedProducsResults' => $fixedProducsResults ?? []]);
    }

    /**
     * @Route("/check-poli", name="ventas-check-poli")
     */
    public function checkPoli(Request $request)
    {

        $em = $this->getDoctrine()->getManager();
        $barcode = $request->get('barcode');
        $idSaleType = $request->get('idSaleType');
        $User = $this->getUser();
        $Sucursal = $User->getSucursalIdsucursal();

        $success = false;
        $hasPoli = false;
        $target = 4;

        $query = $em->createQuery(
            'SELECT v.idventa
            FROM App\Entity\Venta v
            INNER JOIN v.tipoventaIdtipoventa tv
            WHERE v.status=:status AND tv.idtipoventa =:idSaleType
            ORDER BY v.idventa DESC
        '
        )->setParameters(['status' => 1, 'idSaleType' => $idSaleType])->setMaxResults($target);
        $lastSaleIds = $query->getResult();

        $whereSaleIds = " AND v.idventa IN (";
        for ($i = 0; $i < count($lastSaleIds); $i++) {
            $whereSaleIds .= $lastSaleIds[$i]["idventa"];
            if ($i < count($lastSaleIds) - 1)
                $whereSaleIds .= ',';
        }
        $whereSaleIds .= ")";

        if (count($lastSaleIds) == $target) {
            $query = $em->createQuery(
                'SELECT p.codigo, p.codigobarrasuniversal
                FROM App\Entity\Stockventa sv
                INNER JOIN sv.ventaIdventa v
                INNER JOIN sv.stockIdstock s
                INNER JOIN s.productoIdproducto p
                WHERE sv.status = :status ' . $whereSaleIds
            )->setParameters([
                "status" => 1,
            ]);
            $Stockventas = $query->getResult();

            $curTarget = $target;
            foreach ($Stockventas as $Stockventa) {
                if ($Stockventa["codigo"] == $barcode || $Stockventa["codigobarrasuniversal"] == $barcode)
                    $curTarget--;
            }

            if ($curTarget == 0)
                $hasPoli = true;
        }

        if (!$hasPoli) {

            $query = $em->createQuery(
                'SELECT p.idproducto as data, p.codigo, p.codigobarrasuniversal, p.nombre as value, p.precio, p.descripcion, m.nombre as marca,
                c.nombre as categoria,p.modelo, p.tipoproducto, p.precioespecial, p.preciosubdistribuidor, p.preciodistribuidor
                FROM App\Entity\Producto p
                INNER JOIN p.marcaIdmarca m
                INNER JOIN p.categoriaIdcategoria c
                WHERE p.status=:status AND p.tipoproducto=:productType AND (p.codigo =:barcode OR p.codigobarrasuniversal =:barcode)'
            )->setParameters(['status' => 1, 'productType' => 2, 'barcode' => $barcode]);
            $product = $query->getOneOrNullResult();

            $iva = floatval($Sucursal->getPorcentajeiva());

            if ($iva <= 0)
                $iva = 16;

            if ($product) {

                $success = true;

                $product['precio'] = $product['precio'] * (1 + (.01 * $iva));
                $product['precioespecial'] = $product['precioespecial'] ? number_format((float)$product['precioespecial'] * (1 + (.01 * $iva)), 2, '.', '') : null;
                $product['preciosubdistribuidor'] = $product['preciosubdistribuidor'] ? number_format((float)$product['preciosubdistribuidor'] * (1 + (.01 * $iva)), 2, '.', '') : null;
                $product['preciodistribuidor'] = $product['preciodistribuidor'] ? number_format((float)$product['preciodistribuidor'] * (1 + (.01 * $iva)), 2, '.', '') : null;
            }
        }

        return $this->json(['success' => $success, 'product' => $product ?? []]);
    }

    /**
     * @Route("/buscar-tratamiento", name="buscar-tratamiento")
     */
    public function buscarTratamiento(Request $request)
    {

        $em = $this->getDoctrine()->getManager();

        $parameters = array('nombre' => "%" . $request->get('query') . "%", 'status' => "1");
        $productosFinal = [];

        $query = $em->createQuery(
            'SELECT p.idproducto as data,p.codigo ,p.nombre as value,p.precio,p.descripcion, m.nombre as marca, c.nombre as categoria,p.modelo
                ,p.tipoproducto
               FROM App\Entity\Producto p

                inner join p.marcaIdmarca m
               inner join p.categoriaIdcategoria c

               where p.status=:status and (p.descripcion like :nombre  or p.modelo like :nombre) and   p.tipoproducto=2

               '
        )->setParameters($parameters)->setMaxResults(10);


        $productos = $query->getResult();

        if ($productos) {
            foreach ($productos as $keyProducto => $producto) {
                if (trim($producto['codigo']) != "") {
                    $productosFinal[] = $producto;
                }
            }
        }

        $query = $em->createQuery(
            'SELECT p.idproducto as data,p.codigo ,p.nombre as value,p.precio,p.descripcion, m.nombre as marca, c.nombre as categoria,p.modelo,
               p.tipoproducto
               FROM App\Entity\Producto p

                inner join p.marcaIdmarca m
               inner join p.categoriaIdcategoria c 

               where p.status=:status and (p.codigo like :nombre) and p.tipoproducto=2

               '
        )->setParameters($parameters)->setMaxResults(10);


        $productos = $query->getResult();
        if ($productos) {
            foreach ($productos as $keyProducto => $producto) {
                if (trim($producto['codigo']) != "") {
                    $productosFinal[] = $producto;
                }
            }
        }

        foreach ($productosFinal as $key => $producto) {


            //$total+=(float)$producto['total'];

            if ($producto['marca'] == "Sin marca") {
                $producto['marca'] = "";
            }
            if ($producto['tipoproducto'] == "2") {
                $productosFinal[$key]['value'] = $producto['modelo'];
            } else {
                $productosFinal[$key]['value'] = $producto['modelo'] . " / " . $producto['descripcion'];
            }
        }


        return $this->json(['suggestions' => $productosFinal]);
    }

    //TICKET


    /**
     * @Route("/ticket/{type}", name="ticket")
     */
    public function ticket(Request $request, string $type = null): JsonResponse
    {
        /* ---------- 0. Dependencias ---------- */
        $em       = $this->getDoctrine()->getManager();
        $domPdfOp = ['isHtml5ParserEnabled' => true, 'isPhpEnabled' => true];
        $User = $this->getUser();

        /* ---------- 1. Parámetros de entrada ---------- */
        $folio        = $request->get('folio');
        $idEmpresa    = (int) $request->get('idempresa');
        $usuarioVenta = $request->get('usuarioVenta');
        $beneficiaryIds = $request->get('beneficiaryIds', []);
        $productosReq   = $request->get('productos', []);   // array desde el frontend
        $porcIva        = (float) $request->get('porcentajeiva', 0.16);

        // Variables que la versión original esperaba
        $convenio                 = $request->get('convenio');
        $numeroEmpleado           = $request->get('numeroEmpleado');
        $beneficiario             = $request->get('beneficiario');
        $beneficiarioNombre       = $request->get('beneficiarioNombre');
        $clienteTelefono          = $request->get('clienteTelefono');
        $sinConvenioNombreCliente = $request->get('sinConvenioNombreCliente');
        $sinConvenioEmailCliente  = $request->get('sinConvenioEmailCliente');
        $unidadNombre             = $request->get('unidadNombre');
        $esCotizacion             = $request->get('esCotizacion');
        $cuponDescuento           = $request->get('cuponDescuento');
        $precioFijoTipoVenta      = $request->get('preciofijotipoventa');
        $empresa = $User->getSucursalIdsucursal()->getEmpresaIdempresa();
        $prefijo = $empresa->getPrefijotickets();
        $logo = $empresa->getLogo64();
        $pieTicket = $empresa->getPieticket();
        $razonsocial = $empresa->getRazonsocial();

        $msj   = '';
        $exito = false;

        try {
            /* ---------- 2. Recuperar venta (nuevo QueryBuilder) ---------- */
            $qb = $em->createQueryBuilder()
                ->select('sv', 'v', 'svol', 'ol', 'tv', 'suc', 'e', 'c', 'ec', 'u', 'sr', 's', 'p')
                ->from('\\App\\Entity\\Stockventa', 'sv')
                ->innerJoin('sv.ventaIdventa', 'v')
                ->innerJoin('v.tipoventaIdtipoventa', 'tv')
                ->innerJoin('v.sucursalIdsucursal', 'suc')
                ->innerJoin('suc.empresaIdempresa', 'e')
                ->innerJoin('v.clienteIdcliente', 'c')
                ->innerJoin('c.empresaclienteIdempresacliente', 'ec')
                ->leftJoin('c.unidadIdunidad', 'u')
                ->leftJoin('c.sellreferenceIdsellreference', 'sr')
                ->leftJoin('sv.stockIdstock', 's')
                ->innerJoin('s.productoIdproducto', 'p')
                ->leftJoin(
                    '\App\Entity\Stockventaordenlaboratorio', 'svol',
                    'WITH',
                    'svol.stockventaIdstockventa = sv'
                )
                ->leftJoin('svol.ordenlaboratorioIdordenlaboratorio', 'ol')
                ->where('v.folio = :folio')
                ->andWhere('v.status = 1')
                ->andWhere('e.idempresa = :empresa')
                ->setParameter('folio', $folio)
                ->setParameter('empresa', $idEmpresa);

            /** @var App\Entity\Stockventa[] $svList */
            $svList = $qb->getQuery()->getResult();

            if (!$svList) {
                throw new \RuntimeException("No se encontró venta con folio {$folio}");
            }

            /** @var App\Entity\Venta $venta */
            $venta = $svList[0]->getVentaIdventa();
            $empresa  = $venta->getSucursalIdsucursal()->getEmpresaIdempresa();
            $usuario  = $em->getRepository('\App\Entity\Usuario')
                ->findOneBy(['idusuario' => $usuarioVenta]);

            /* ---------- 3. ¿Tiene orden de laboratorio? ---------- */
            // Inicializar como false, se actualizará después del procesamiento
            $tieneOrdenLab = false;

            /* ---------- 4. Beneficiarios (opcional) ---------- */
            $beneficiaries = null;

            $fecha = date("d-m-Y H:i:s ") . " Hrs";

            if (is_array($beneficiaryIds) && $beneficiaryIds) {
                $beneficiaries = $em->createQuery(
                    'SELECT CONCAT(c.nombre, \' \', c.apellidopaterno, \' \', c.apellidomaterno) AS fullName,
                        c.email, c.telefono
                 FROM App\Entity\Cliente c
                 WHERE c.status = 1 AND c.idcliente IN (:ids)
                 ORDER BY c.nombre ASC'
                )->setParameter('ids', $beneficiaryIds)
                    ->getResult();
            }

            /* ---------- 5. Tipo de venta ---------- */
            $tipoVenta = null;
            $nombreConvenio = '';
            if ($convenio) {
                $tipoVenta = $em->getRepository('\App\Entity\Tipoventa')
                    ->findOneBy(['idtipoventa' => $convenio]);
                $nombreConvenio = $tipoVenta ? $tipoVenta->getNombre() : '';
            }

            /* ---------- 6. Productos & subtotales ---------- */
            // Consulta de productos (mantengo tu DQL original porque usas campos calculados)
            $dql = '
            SELECT sv.preciofinal, sv.cantidad, m.nombre AS Marca, 
                   p.descripcion, p.tipoproducto, p.masivounico,
                   c.nombre AS claseNombre, sv.isomittable, p.idproducto
            FROM App\Entity\Stockventa sv
            INNER JOIN sv.stockIdstock s
            INNER JOIN sv.ventaIdventa v2
            INNER JOIN s.productoIdproducto p
            INNER JOIN p.marcaIdmarca m
            INNER JOIN p.categoriaIdcategoria sc
            INNER JOIN sc.claseIdclase c
            WHERE sv.status = 1 AND v2.idventa = :idVenta';
            if ($type === null) {
                $dql .= " AND sv.isomittable = '0'";
            } elseif ($type === '1') {
                $dql .= " AND sv.isomittable = '1'";
            }

            $stockVentas = $em->createQuery($dql)
                ->setParameter('idVenta', $venta->getIdventa())
                ->getResult();

            // Lookup para filtrar productos que vienen del frontend
            $lookup = [];
            foreach ($stockVentas as $svRow) {
                $lookup[$svRow['idproducto']] = $svRow;
            }

            $tieneEspecial = count($lookup) !== count($productosReq);

            $productosFinal = [];
            $newSubtotal    = 0.0;

            foreach ($productosReq as $prod) {

                if (!isset($lookup[$prod['idproducto']])) {
                    continue; // omite productos que no estén en stockventas
                }

                $objProd = $em->getRepository('\App\Entity\Producto')->find($prod['idproducto']);

                if ($objProd) {
                    $descripcion = trim($objProd->getDescripcion()) !== ''
                        ? $objProd->getCategoriaIdcategoria()->getNombre() . ' ' . $objProd->getDescripcion()
                        : $objProd->getCategoriaIdcategoria()->getNombre() . ' ' . $objProd->getModelo();

                    $prod['descripcion'] = $descripcion;
                    $prod['codigobarrasuniversal'] = $objProd->getCodigobarrasuniversal();
                    $prod['modelo'] = $lookup[$prod['idproducto']]['modelo'] ?? '-';
                    $prod['marca'] = $lookup[$prod['idproducto']]['Marca'] ?? '-';
                    $prod['sku'] = $lookup[$prod['idproducto']]['sku'] ?? '-';
                    $prod['diagnostico'] = $lookup[$prod['idproducto']]['diagnostico'] ?? '-';

                    $newSubtotal += $prod['productoCantidad'] * $prod['productoPrecioFinal'];
                    $productosFinal[] = $prod;
                }
            }


            $subtotal = $newSubtotal / (1 + $porcIva);
            $iva      = $subtotal * $porcIva;
            $total    = $subtotal + $iva;

            /* ---------- 7. Pagos ---------- */
            $pagos = $em->createQuery(
                'SELECT p.monto, pt.name AS tipopago, p.fecha, p.mesesintereses
             FROM App\Entity\Pago p
             INNER JOIN p.paymenttypeIdpaymenttype pt
             INNER JOIN p.ventaIdventa v3
             INNER JOIN v3.sucursalIdsucursal s3
             INNER JOIN s3.empresaIdempresa e3
             WHERE p.status = 1
               AND v3.folio = :folio
               AND e3.idempresa = :empresa
               ' . ($type !== null ? "AND pt.name NOT LIKE 'Convenio'" : '') . '
             ORDER BY p.mesesintereses DESC'
            )->setParameters(['folio' => $folio, 'empresa' => $idEmpresa])
                ->getResult();





            $totalPagado = array_sum(array_column($pagos, 'monto'));

            /* ---------- 8. Orden Laboratorio  ---------- */
            $graduacionData = $this->getGraduacionData($venta);

            /* → NUEVO: arma el array que tu Twig realmente recorre */
            $stockventaordenlaboratorio = [];

            foreach ($graduacionData['productos'] as $prod) {
                // Busca la graduación que corresponde al id de producto
                $grad = array_filter(
                    $graduacionData['graduaciones'],
                    fn ($g) => $g['producto_id'] === $prod['id']
                );
                $grad = array_values($grad)[0] ?? null;   // primera coincidencia o null

                $stockventaordenlaboratorio[] = [
                    'stockventa' => $prod,   // ya trae cantidad, precio, etc.
                    'graduacion' => $grad    // puede ser null (Twig lo verifica)
                ];
            }

            /* ---------- 9. Preparar datos para la vista ---------- */
            $twigData = [
                'ommitableOverride' => $type == '1',
                'stockVentas' => $stockVentas,
                'logo' => $logo,
                'prefijo' => $prefijo,
                'razonsocial' => $razonsocial,
                'nombreSucursal' => $venta->getSucursalIdsucursal()->getNombre(),
                'direccionSucursal' => $venta->getSucursalIdsucursal()->getDireccion(),
                'telefonoSucursal' => $venta->getSucursalIdsucursal()->getTelefono(),
                'nombreVendedor' => $usuario ? $usuario->getNombre() . ' ' . $usuario->getApellidopaterno() : '',
                'authorizationNumber' => $venta->getAuthscan(),
                'folio' => $venta->getFolio(),
                'fecha' => $fecha,
                'Tipoventa' => $tipoVenta,
                'empresa' => $empresa,
                'venta' => $venta,
                'usuario' => $usuario,
                'nombreConvenio' => $nombreConvenio,
                'beneficiaries' => $beneficiaries,
                'productos' => $productosFinal,
                'subtotal' => $subtotal,
                'iva' => $iva,
                'total' => $total,
                'pagos' => $pagos,
                'totalPagado' => $totalPagado,
                'tieneOrdenLab' => $tieneOrdenLab,
                'tieneEspecial' => $tieneEspecial,
                'type' => $type,
                'convenio' => $convenio,
                'numeroEmpleado' => $numeroEmpleado,
                'beneficiario' => $beneficiario,
                'beneficiarioNombre' => $beneficiarioNombre,
                'clienteTelefono' => $clienteTelefono,
                'cliente' => $venta->getClienteIdcliente()
                    ? $venta->getClienteIdcliente()->getNombreCompleto()
                    : ($sinConvenioNombreCliente ?: ''),
                'sinConvenioEmailCliente' => $sinConvenioEmailCliente,
                'unidadNombre' => $unidadNombre,
                'esCotizacion' => $esCotizacion,
                'cuponDescuento' => $cuponDescuento,
                'precioFijoTipoVenta' => $precioFijoTipoVenta,
                'stockventaordenlaboratorio' => $stockventaordenlaboratorio ?? [],
                'pieTicket' => $pieTicket ?: 'Muchas gracias por su compra'
            ];


            /* ---------- 9. Ruta y nombres de archivos ---------- */
            $ticketsDir = $this->getParameter('carpetaTickets');
            $rfc        = preg_replace('/\s+/', '', $empresa->getRfc());
            $dir        = "{$ticketsDir}/{$rfc}/{$folio}";
            if (!is_dir($dir)) {
                mkdir($dir, 0775, true);
            }

            /* ---------- 10. Generar ticket de venta ---------- */
            $ticketName = $this->sanitize("{$folio}-" . time()) . '.pdf';
            $dompdf = new Dompdf($domPdfOp);
            $dompdf->set_paper([0, 0, 226.772, 1000]);
            $dompdf->loadHtml($this->renderView('ventas/ticket-new.html.twig', $twigData));
            $dompdf->render();
            file_put_contents("{$dir}/{$ticketName}", $dompdf->output());

            // -- Diferenciar ticket normal y ticket especial
            if ($type === null) {
                $venta->setTicketpdf($ticketName);
            } elseif ($type === '1') {
                $venta->setTickerpdfespecial($ticketName);
            }

            /* ---------- 11. Generar ticket de graduación ---------- */
            // Actualizar verificación de órdenes de laboratorio DESPUÉS de procesar la venta
            foreach ($svList as $sv) {
                $stockventaol = $em->getRepository('\App\Entity\Stockventaordenlaboratorio')
                    ->findOneBy(['stockventaIdstockventa' => $sv]);
                if ($stockventaol) {
                    $tieneOrdenLab = true;
                    break;
                }
            }

            if ($tieneOrdenLab) {
                $gradName = $this->sanitize("{$folio}-graduacion-" . time()) . '.pdf';

                // Siempre generar nuevo PDF para asegurar datos actualizados
                $dompdf = new Dompdf($domPdfOp);

                // Usar el método buildGraduacionData que ya está corregido para múltiples graduaciones
                $gradData = $this->buildGraduacionData($venta, $empresa);

                // Agregar datos adicionales específicos de esta generación
                // NOTA: No incluimos 'fecha' aquí para mantener la fecha actual de buildGraduacionData()
                $gradData = array_merge($gradData, [
                    'authorizationNumber' => $venta->getAuthscan(),
                    'Tipoventa' => $tipoVenta,
                    'empresa' => $empresa,
                    'venta' => $venta,
                    'usuario' => $usuario,
                    'tipoVenta' => $tipoVenta,
                    'nombreConvenio' => $nombreConvenio,
                    'beneficiaries' => $beneficiaries,
                    'productos' => $productosFinal,
                    'subtotal' => $subtotal,
                    'iva' => $iva,
                    'total' => $total,
                    'pagos' => $pagos,
                    'totalPagado' => $totalPagado,
                    'tieneEspecial' => $tieneEspecial,
                    'type' => $type,
                    'convenio' => $convenio,
                    'numeroEmpleado' => $numeroEmpleado,
                    'beneficiario' => $beneficiario,
                    'beneficiarioNombre' => $beneficiarioNombre,
                    'unidadNombre' => $unidadNombre,
                    'esCotizacion' => $esCotizacion,
                    'cuponDescuento' => $cuponDescuento,
                    'precioFijoTipoVenta' => $precioFijoTipoVenta
                ]);

                $dompdf->set_paper([0, 0, 226.772, 1000]);
                $dompdf->loadHtml(
                    $this->renderView('ventas/ticket-graduacion.html.twig', $gradData)
                );
                $dompdf->render();
                file_put_contents("{$dir}/{$gradName}", $dompdf->output());
                $venta->setTicketgraduacion($gradName);
            }

            // ---------- 12. Generar ticket especial si corresponde ----------
            if ($tieneEspecial && $type === null) {
                // Filtrar solo productos omitibles
                $productosEspeciales = array_filter($productosReq, function ($prod) {
                    return isset($prod['isOmittable']) && $prod['isOmittable'] == '1';
                });
                $reqEspecial = clone $request;
                $reqEspecial->request->set('productos', array_values($productosEspeciales));
                $this->ticket($reqEspecial, '1');
            }

            /* ---------- 13. Persistir y finalizar ---------- */
            $em->flush();
            $exito = true;

        } catch (\Throwable $e) {
            $msj = $e->getMessage() . ' linea ' . $e->getLine() . ' archivo ' . $e->getFile();
        }

        return $this->json(['msj' => $msj, 'exito' => $exito]);
    }


    /**
     * Prepara los datos necesarios para generar el ticket de graduación.
     */
    private function getGraduacionData(Venta $venta): array
    {
        $em = $this->getDoctrine()->getManager();
        $graduaciones = [];
        $productos = [];

        // Obtener todos los StockVenta activos para esta venta
        $stockVentas = $em->createQueryBuilder()
            ->select('sv')
            ->from('App\\Entity\\Stockventa', 'sv')
            ->where('sv.ventaIdventa = :venta')
            ->andWhere('sv.status = 1')
            ->setParameter('venta', $venta)
            ->getQuery()
            ->getResult();

        foreach ($stockVentas as $sv) {

            // Obtener Stock con consulta directa (patrón ticket-new)
            $stockId = $sv->getStockIdstock() ? $sv->getStockIdstock()->getIdstock() : null;
            if (!$stockId) continue;

            $stock = $em->find('App\Entity\Stock', $stockId);
            if (!$stock) continue;

            // Obtener Producto con consulta directa (patrón ticket-new)
            $productoId = $stock->getProductoIdproducto() ? $stock->getProductoIdproducto()->getIdproducto() : null;
            if (!$productoId) continue;

            $producto = $em->find('App\Entity\Producto', $productoId);
            if (!$producto) continue;

            // Obtener la graduación más reciente para este StockVenta
            $svol = $em->createQueryBuilder()
                ->select('svol', 'ol')
                ->from('App\\Entity\\Stockventaordenlaboratorio', 'svol')
                ->innerJoin('svol.ordenlaboratorioIdordenlaboratorio', 'ol')
                ->where('svol.stockventaIdstockventa = :stockventa')
                ->setParameter('stockventa', $sv)
                ->orderBy('ol.actualizacion', 'DESC')
                ->setMaxResults(1)
                ->getQuery()
                ->getOneOrNullResult();



            // Preparar datos del producto (patrón ticket-new con consultas frescas)
            $descripcion = trim($producto->getDescripcion()) !== ''
                ? $producto->getCategoriaIdcategoria()->getNombre() . ' ' . $producto->getDescripcion()
                : $producto->getCategoriaIdcategoria()->getNombre() . ' ' . $producto->getModelo();

            $productoData = [
                'id' => $sv->getIdstockventa(),
                'descripcion' => $descripcion,
                'marca' => $producto->getMarcaIdmarca() ? $producto->getMarcaIdmarca()->getNombre() : 'NA',
                'tipoproducto' => $producto->getTipoproducto(),
                'cantidad' => $sv->getCantidad(),
                'preciofinal' => $sv->getPreciofinal(),
                'stock' => [
                    'codigobarras' => $stock->getCodigobarras(),
                    'producto' => [
                        'modelo' => $producto->getModelo()
                    ]
                ]
            ];
            $productos[] = $productoData;

            // Si hay graduación, agregarla al array
            if ($svol && $ol = $svol->getOrdenlaboratorioIdordenlaboratorio()) {
                $graduaciones[] = [
                    'producto_id' => $sv->getIdstockventa(),
                    'odEsfera' => $ol->getEsferaod(),
                    'odCilindro' => $ol->getCilindrood(),
                    'odEje' => $ol->getEjeod(),
                    'odAdicion' => $ol->getAvcercacaddod(),
                    'oiEsfera' => $ol->getEsferaoi(),
                    'oiCilindro' => $ol->getCilindrooi(),
                    'oiEje' => $ol->getEjeoi(),
                    'oiAdicion' => $ol->getAvcercacaddoi(),
                    'distanciaPupilar' => $ol->getDip(),
                    'altura' => $ol->getAo(),
                    '_aco' => $ol->getAco(),
                    'diagnostico' => $ol->getDiagnosis(),
                    'notas' => $ol->getNotes(),
                    'stage' => $ol->getStage() // Agregar el stage desde la orden de laboratorio
                ];
            }
        }

        return [
            'graduaciones' => $graduaciones,
            'productos' => $productos
        ];
    }

    private function sanitize($string)
    {
        // Remove spaces and special characters
        $string = str_replace(array(' ', "\t", "\n", "\r"), '', $string);
        $string = preg_replace('/[^A-Za-z0-9\-]/', '', $string);
        return trim($string);
    }

    /**
     * @Route("/imprimir-ticket", name="imprimir-ticket")
     */
    public function imprimirTicket(Request $request)
    {
        $exito = false;
        $msj = "";
        try {
            $baseurl = $request->getScheme() . '://' . $request->getHttpHost() . $request->getBasePath();
            $cliente = $request->get('cliente');
            $convenio = $request->get('convenio');
            $numeroEmpleado = $request->get('numeroEmpleado');
            $beneficiario = $request->get('beneficiario');
            $beneficiarioNombre = $request->get('beneficiarioNombre');
            $productos = $request->get('productos');


            // for ewindows --->
            //$nombre_impresora = "Spill-Proof";
            // $connector = new WindowsPrintConnector($nombre_impresora);

            $connector = new FilePrintConnector("/dev/usb/lp2");
            $printer = new Printer($connector);

            $total = 0;


            # Vamos a alinear al centro lo próximo que imprimamos
            $printer->setJustification(Printer::JUSTIFY_CENTER);

            /*
                Intentaremos cargar e imprimir
                el logo
            */
            /*try{
                 $logo = EscposImage::load("img/xml_negro.png", false);
               $printer->bitImage($logo);
            }catch(Exception $e){}
     */
            /*
                Ahora vamos a imprimir un encabezado
            */

            $printer->text("Grupo Optimo" . "\n");
            //$printer->text("Direccion: Orquídeas #151" . "\n");
            $printer->text("Tel: 5666 6666" . "\n");
            $printer->text("----------------------------" . "\n");
            $printer->text("Cliente: " . $cliente . "\n");
            $printer->text("Convenio: " . $convenio . "\n");
            $printer->text("Empleado: " . $numeroEmpleado . "\n");
            $printer->text("Beneficiario: " . $beneficiarioNombre . "\n");
            #La fecha también

            $printer->text(date("Y-m-d H:i:s") . "\n");
            $printer->text("----------------------------" . "\n");
            $printer->setJustification(Printer::JUSTIFY_LEFT);
            $printer->text("ARTICULO .\n");
            $printer->text("CANT     P.U      IMP.\n");
            $printer->text("----------------------------" . "\n");
            /*
            Ahora vamos a imprimir los
            productos
        */
            /*Alinear a la izquierda para la cantidad y el nombre*/
            $printer->setJustification(Printer::JUSTIFY_LEFT);

            foreach ($productos as $key => $producto) {

                $printer->text($producto['nombre'] . "\n");
                $printer->text($producto['cantidad'] . "      " . $producto['precio'] . "     " . $producto['total'] . "   \n");
                $total += (float)$this->eliminar_simbolos($producto['total']);
                // code...
            }
            /*$iva
                Terminamos de imprimir
                los productos, ahora va el total
            */
            $User = $this->getUser();
            $Sucursal = $User->getSucursalIdsucursal();
            $iva = floatval($Sucursal->getPorcentajeiva());

            if ($iva <= 0) {
                $iva = 16;
            }

            $iva = $iva / 100;

            $printer->text("----------------------------" . "\n");
            $printer->setJustification(Printer::JUSTIFY_RIGHT);
            $printer->text("SUBTOTAL: $" . number_format($total, 2, '.', ',') . "\n");
            $printer->text("IVA: $" . number_format(($total * $iva), 2, '.', ',') . "\n");
            $printer->text("TOTAL: $" . number_format(($total * (1 + $iva)), 2, '.', ',') . "\n");


            /*
                Podemos poner también un pie de página
            */
            $printer->setJustification(Printer::JUSTIFY_CENTER);
            $printer->text("Muchas gracias por su compra\n");

            $printer->text("\n\n\n\n\n");

            /*Alimentamos el papel 3 veces*/
            $printer->feed(1);

            /*
                Cortamos el papel. Si nuestra impresora
                no tiene soporte para ello, no generará
                ningún error
            */
            $printer->cut("Printer::CUT_FULL", 6);


            /*
                Por medio de la impresora mandamos un pulso.
                Esto es útil cuando la tenemos conectada
                por ejemplo a un cajón
            */
            $printer->pulse();
            $exito = true;
            /*
                Para imprimir realmente, tenemos que "cerrar"
                la conexión con la impresora. Recuerda incluir esto al final de todos los archivos
            */
            $printer->close();
        } catch (\Exception $e) {
            $msj = $e->getMessage() . " linea " . $e->getLine() . " archivo " . $e->getFile();
        }
        return $this->json(["msj" => $msj, "exito" => $exito]);
    }

    function eliminar_simbolos($string)
    {

        $string = trim($string);

        $string = str_replace(
            array('á', 'à', 'ä', 'â', 'ª', 'Á', 'À', 'Â', 'Ä'),
            array('a', 'a', 'a', 'a', 'a', 'A', 'A', 'A', 'A'),
            $string
        );

        $string = str_replace(
            array('é', 'è', 'ë', 'ê', 'É', 'È', 'Ê', 'Ë'),
            array('e', 'e', 'e', 'e', 'E', 'E', 'E', 'E'),
            $string
        );

        $string = str_replace(
            array('í', 'ì', 'ï', 'î', 'Í', 'Ì', 'Ï', 'Î'),
            array('i', 'i', 'i', 'i', 'I', 'I', 'I', 'I'),
            $string
        );

        $string = str_replace(
            array('ó', 'ò', 'ö', 'ô', 'Ó', 'Ò', 'Ö', 'Ô'),
            array('o', 'o', 'o', 'o', 'O', 'O', 'O', 'O'),
            $string
        );

        $string = str_replace(
            array('ú', 'ù', 'ü', 'û', 'Ú', 'Ù', 'Û', 'Ü'),
            array('u', 'u', 'u', 'u', 'U', 'U', 'U', 'U'),
            $string
        );

        $string = str_replace(
            array('ñ', 'Ñ', 'ç', 'Ç'),
            array('n', 'N', 'c', 'C',),
            $string
        );

        $string = str_replace(
            array(
                "\\",
                "¨",
                "º",
                "-",
                "~",
                "#",
                "@",
                "|",
                "!",
                "\"",
                "·",
                "$",
                "%",
                "&",
                "/",
                "(",
                ")",
                "?",
                "'",
                "¡",
                "¿",
                "[",
                "^",
                "<code>",
                "]",
                "+",
                "}",
                "{",
                "¨",
                "´",
                ">",
                "< ",
                ";",
                ",",
                ":",
                ".",
                " "
            ),
            ' ',
            $string
        );
        return $string;
    }

    /**
     * @Route("/guardar-venta", name="guardar-venta")
     */
    public function guardarVenta(Request $request)
    {

        $exito = false;
        $msj = "";
        $esNuevaventa = false;
        $ultimoFolio = "";
        $cantidadesProductosCorrectas = true;

        $User = $this->getUser();
        $idsucursal = $User->getSucursalIdsucursal()->getIdSucursal();

        $em = $this->getDoctrine()->getManager();
        //  $total=$request->get('total');
        //  $porcentajeiva=$request->get('porcentajeiva');
        //  $iva=$request->get('iva');

        //El total es el precio, no el precio final

        $total = $request->get('total');
        $subtotal = $request->get('subtotal');

        $porcentajeiva = $request->get('porcentajeiva');
        $iva = $request->get('iva');
        $idventa = $request->get('idventa');
        $esCotizacion = $request->get('esCotizacion');
        $savedVentaCupon = $request->get('savedVentaCupon');

        //$nombreDocumento=$request->get('nombreDocumento');
        $descuento = 0;
        $pagado = 0;

        $beneficiarioNombre = $request->get('beneficiarioNombre');
        $beneficiario = $request->get('beneficiario');

        $idcliente = $request->get('idcliente');
        $convenio = $request->get('convenio');
        $numeroEmpleado = $request->get('numeroEmpleado');
        $productos = $request->get('productos');


        $sinConvenioNombreCliente = $request->get('sinConvenioNombreCliente');


        //EmailCliente
        $sinConvenioEmailCliente = $request->get('sinConvenioEmailCliente');

        $sinConvenioTelefonoCliente = $request->get('clienteTelefono');
        $unidadNombre = $request->get('unidadNombre');
        $idunidad = $request->get('idunidad');
        $tipoPago = $request->get('tipoPago');
        $pidioFactura = $request->get('pidioFactura');
        $idUsuarioVenta = $request->get('idUsuarioVenta');


        $dondeNosConocio = $request->get('dondeNosConocio');
        $graduaciones = $request->get('graduaciones');


        if (is_string($graduaciones)) {
            $graduaciones = json_decode($graduaciones, true);
        }
        //LENON

        $pagos = $request->get('pagos');
        $clienteTelefono = $request->get('clienteTelefono');

        $cuponDescuento = $request->get('cuponDescuento');
        //$nombreCliente=$request->get('cliente');

        $nombreCliente = $request->get('nombre');
        $apellidoP = $request->get('apellidoP');
        $apellidoM = $request->get('apellidoM');

        $meses = $request->get('meses');
        $notas = $request->get('notas');
        $ventaGarantiaFolio = $request->get('foliogarantia');
        $empresagarantia = $request->get('empresagarantia');
        $ventaCredito = $request->get('ventaCredito');

        $empresaclienteid = $request->get('empresaclienteid');
        $diascredito = $request->get('diascredito');

        $beneficiaryIds = $request->get('beneficiaryIds');
        $authorizationNumber = $request->get('authorizationNumber');
        $authorizationScan = $request->get('authorizationScan', '');


        $Usuario = $user = $this->getUser();
        $Sucursal = $Usuario->getSucursalIdsucursal();
        $Empresa = $Sucursal->getEmpresaIdempresa();
        $Unidad = null;

        $ultimoFolio = 1;
        $ultimaVenta = null;

        $Pago = null;
        $documentoEncontrado = $request->get('tieneDocumento');

        $apartararmazon = "0";
        try {

            //buscamos el usuario que vendió
            $UsuarioVenta = $this->getDoctrine()->getRepository(Usuario::class)->findOneBy(['idusuario' => $idUsuarioVenta]);

            if (!$UsuarioVenta) {
                throw new \Exception('El usuario que vendió no existe');
            }

            //si  no es nueva venta
            if ($idventa != "") {
                $Venta = $this->getDoctrine()->getRepository(Venta::class)->findOneBy(['idventa' => $idventa]);
                $ultimoFolio = $Venta->getFolio();
                $Venta->setFechaactualizacion(new \DateTime("now"));
                if (!$Venta) {
                    throw new \Exception('No se encontró la venta por id');
                }
            } else {
                $esNuevaventa = true;
                $Venta = new Venta();
                $Venta->setFolio(0);
                $fechaCreacion = new \DateTime("now");

                $Venta->setFecha($fechaCreacion);
                $Venta->setFechacreacion($fechaCreacion);
                $Venta->setFechaactualizacion($fechaCreacion);
                $Venta->setSedescontodeinventario('0');
                $Venta->setGerente($Usuario);
                $Venta->setSucursalIdsucursal($Sucursal);

                //$Venta->setFechaventa($fechaVenta);
            }

            if ($ventaGarantiaFolio != "") {
                //$VentaGarantia=$this->getDoctrine()->getRepository(Venta::class)->findOneBy(['idventa'=>$ventaGarantiaFolio]);

                $query = $em->createQuery(
                    'SELECT  v
                FROM App\Entity\Venta v
                INNER JOIN v.sucursalIdsucursal s
                INNER JOIN s.empresaIdempresa e

               where v.status=:status and v.folio=:folio and e.idempresa = :idempresa
               '
                )->setParameters(['status' => '1', 'folio' => $ventaGarantiaFolio, 'idempresa' => $empresagarantia]);

                $VentaGarantia = $query->getResult();

                if (!$VentaGarantia) {
                    throw new \Exception('No se encontró la venta de garantia');
                } else
                    $Venta->setVentaGarantia($VentaGarantia);
            }

            //Es nueva venta con la validación de idventa de arriba
            if ($esNuevaventa) {
                //primero validamos que los productos esten en stock
                $idsProductosVendidos = [];  // Inicializar el array aquí
                foreach ($productos as $key => $productoElegido) {


                    $idstockventa = $productoElegido['idstockventa'];
                    $idstock = $productoElegido['idstock'];
                    $Stockventa = $this->getDoctrine()->getRepository(Stockventa::class)->findOneBy(['idstockventa' => $idstockventa]);
                    $nombre = $productoElegido['nombre'];
                    $Stock = $this->getDoctrine()->getRepository(Stock::class)->findOneBy(['idstock' => $idstock]);
                    $productoCantidad = $productoElegido['productoCantidad'];
                    $idstockventa = $productoElegido['idstockventa'];
                    $tipoproducto = $productoElegido['tipoproducto'];
                    $productoStock = $Stock->getCantidad();

                    if ($idstockventa != "" && $Stockventa->getStatus() == "1" && $Stockventa->getEstaapartado() == "1") {

                        $productoStock += $Stockventa->getCantidad();
                    }


                    if ($tipoproducto != "2") {
                        if ($productoCantidad > $productoStock) {
                            $cantidadesProductosCorrectas = false;
                            $msj .= 'El producto ' . $nombre . ' no tiene ' . $productoCantidad . ' unidades en stock, solo hay ' . $Stock->getCantidad() . '<br>';
                        }
                    }


                    if ($productoElegido['tipoproducto'] == "1") {
                        if ($productoCantidad > $productoStock) {
                            $cantidadesProductosCorrectas = false;
                            $msj .= 'El producto ' . $nombre . ' no tiene ' . $productoCantidad . ' unidades en stock solo hay ' . $Stock->getCantidad() . '<br>';
                        }
                    }

                    $idsProductosVendidos[] = $productoElegido['idproducto'];
                }
                // Llama a emailStock después de procesar TODOS los productos de la venta
                $wasEmailSent = $this->emailStock($idsProductosVendidos);
            }


            if ($cantidadesProductosCorrectas) {

                $Tipoventa = $this->getDoctrine()->getRepository(Tipoventa::class)->findOneBy(['idtipoventa' => $convenio]);


                if ($Tipoventa) {

                    $apartararmazon = $Tipoventa->getApartararmazon();


                    // Verificar si se debe agregar el pago del total al cerrar la venta si es venta entra
                    if ($Tipoventa->getPagoalfinal() == '1' && $esCotizacion !== '1') {

                        $newSubtotal = 0;
                        $oldSubtotal = 0;


                        foreach ($productos as $producto) {
                            if ($producto['isOmittable'] != "1") {
                                $newSubtotal += (floatval($producto['productoPrecioFinal']) * floatval($producto['productoCantidad']));
                            }
                        }

                        $query = $em->createQuery(
                            'SELECT pago.idpago, pago.monto 
                            FROM App\Entity\Pago pago
                            INNER JOIN pago.ventaIdventa venta
                            WHERE pago.status = :status
                              AND venta.folio = :folio'
                        )->setParameters(['status' => '1', 'folio' => $Venta->getFolio()]);

                        $Pagos = $query->getResult() ?? new Pago();


                        if ($Pagos) {
                            foreach ($Pagos as $Pago) {
                                $oldSubtotal += floatval($Pago['monto']);
                            }
                        }


                        if ($oldSubtotal < $newSubtotal) {


                            $restante = $newSubtotal - $oldSubtotal;


                            $tempPaymenttype = $em->getRepository(Paymenttype::class)->findOneBy(array('name' => 'Convenio'));

                            $pagosIds = [];
                            foreach ($Pagos as $Pago) {
                                $pagosIds[] = $Pago['idpago'];
                            }


                            $pagoConvenio = ['monto' => $restante, 'tipopagoanticipo' => $tempPaymenttype->getIdpaymenttype(), 'idpago' => $pagosIds, 'automatic' => '1'];
                            if ($pagos == NULL) {
                                $pagos = [];
                            }
                            array_push($pagos, $pagoConvenio);
                        }

                    }
                } else {
                    throw new \Exception('No se encontró el tipo de venta');
                }

                $Venta->setTipoventaIdtipoventa($Tipoventa);

                $updateToSale = false;

                //Si es cotización y verifica doble vez si no es cotización
                if ($Venta->getCotizacion() == '1' && $esCotizacion != '1') {
                    /** @var TYPE_NAME $updateToSale */
                    $updateToSale = true;
                }

                $Venta->setCotizacion($esCotizacion);

                if ($esCotizacion != "1" && $Venta->getFechaventa() == NULL) {

                    //  var_dump($Venta->getFechaventa());
                    $Venta->setFechaventa($Venta->getFechaventa() ?? new \DateTime("now"));
                }

                $ivaPercentage = floatval($Sucursal->getPorcentajeiva());

                if ($ivaPercentage <= 0) {
                    $ivaPercentage = 16;
                }

                $Venta->setPagado($total);
                $Venta->setDeuda($total);
                $Venta->setTotal($subtotal);
                $Venta->setPorcentajeiva($ivaPercentage);
                $Venta->setIva($iva);
                $Venta->setDescuento($descuento);
                $Venta->setBeneficiario($beneficiarioNombre);
                $Venta->setStatus("1");
                $Venta->setUsuarioIdusuario($UsuarioVenta);
                $Venta->setTipopago($tipoPago);
                $Venta->setPidiofactura($pidioFactura);
                $Venta->setConvenio($convenio);
                /*if($nombreDocumento!=""){
                  $Venta->setArchivoautorizacion($nombreDocumento);
              }*/


                /*********************Guardar Cliente******************/
                $Cliente = new Cliente();


                if ($idcliente != "") {
                    $Cliente = $this->getDoctrine()->getRepository(Cliente::class)->findOneBy(['idcliente' => $idcliente]);
                }

                if ($idunidad != "") {
                    $Unidad = $this->getDoctrine()->getRepository(Unidad::class)->findOneBy(['idunidad' => $idunidad]);
                    $Cliente->setUnidadIdunidad($Unidad);
                    $Venta->setUnidadIdunidad($Unidad);


                    $Beneficiario = $em->getRepository(Cliente::class)->findOneBy(array('idcliente' => $beneficiario));
                    if ($Beneficiario) {
                        $Beneficiario->setUnidadIdunidad($Unidad);
                        $em->persist($Beneficiario);
                    }
                }


                //$Cliente->setComonosconocio($dondeNosConocio);
                $Cliente->setTelefono($clienteTelefono);
                $Cliente->setNombre($nombreCliente);
                $Cliente->setApellidopaterno($apellidoP);
                $Cliente->setApellidomaterno($apellidoM);
                $Cliente->setEmail($sinConvenioEmailCliente);
                $Cliente->setNumeroempleado($numeroEmpleado);

                //$Beneficiario = $em->getRepository(Cliente::class)->findOneBy(array('idcliente' => $beneficiario));
                $Empresacliente = $em->getRepository(Empresacliente::class)->findOneBy(array('idempresacliente' => $empresaclienteid));

                if (!$Empresacliente)
                    $Empresacliente = $em->getRepository(Empresacliente::class)->findOneBy(array('idempresacliente' => 1));

                $Cliente->setEmpresaclienteIdempresacliente($Empresacliente);


                $sellReferenceRepository = $this->getDoctrine()->getRepository(Sellreference::class);
                // Verifica si $dondeNosConocio es numérico, lo cual indica una referencia existente
                if (is_numeric($dondeNosConocio)) {
                    $sellReference = $sellReferenceRepository->find($dondeNosConocio);
                } else {
                    $sellReference = null;
                }

                if ($sellReference !== null) {
                    $Cliente->setSellreferenceIdsellreference($sellReference);
                } elseif ($dondeNosConocio !== null && $dondeNosConocio !== '') {

                    $newSellReference = new Sellreference();
                    $newSellReference->setName($dondeNosConocio);
                    $newSellReference->setCommissionstorablepercentage('0.00');
                    $newSellReference->setCommissionservicepercentage('0.00');
                    $em->persist($newSellReference);
                    $em->flush();

                    $Cliente->setSellreferenceIdsellreference($newSellReference);
                } else {
                    $Cliente->setSellreferenceIdsellreference(null);
                }


                //$Cliente->setSellreferenceIdsellreference($dondeNosConocio);
                $Cliente->setTelefono($clienteTelefono);
                $Cliente->setNombre($nombreCliente);
                $Cliente->setApellidopaterno($apellidoP);
                $Cliente->setApellidomaterno($apellidoM);
                $Cliente->setEmail($sinConvenioEmailCliente);

                /*$SellReference = $this->getDoctrine()->getRepository(Sellreference::class)->findOneBy(['idsellreference'=>$dondeNosConocio]);

                if ($SellReference) $Cliente->setSellreferenceIdsellreference($SellReference);*/

                $em->persist($Cliente);
                $em->flush();

                /*************************************************/

                $Venta->setClienteIdcliente($Cliente);
                $Venta->setNotas($notas);

                //$em->persist($Venta);
                //$em->flush();

                if ($ventaCredito == "true")
                    $Venta->setCredito('1');
                else
                    $Venta->setCredito('0');

                //$em->persist($Venta);
                //$em->flush();

                /******************************ESTO SIEMPRE SE HACE***********************************/

                if ($cuponDescuento != "") {
                    //pruimero buscamos la venta cupon

                    $Ventacupon = new Ventacupon();
                    $query = $em->createQuery(
                        'SELECT vc
                        FROM App\Entity\Ventacupon vc
                        inner join  vc.ventaIdventa v
                        INNER JOIN v.sucursalIdsucursal s
                        INNER JOIN s.empresaIdempresa e
                        inner join  vc.cuponIdcupon c
                        where vc.status=:status and c.codigo=:codigo and v.idventa=:idventa
                        '
                    )->setParameters(['status' => '1', 'codigo' => $cuponDescuento, 'idventa' => $Venta->getIdventa()]);
                    $Ventacupones = $query->getResult();

                    $newCupon = true;
                    if ($Ventacupones) {
                        $Ventacupon = $Ventacupones[0];
                        $newCupon = false;
                    } else {
                        $Ventacupon->setFechacreacion(new \DateTime("now"));
                    }
                    //tomaos uns
                    $Cupon = $this->getDoctrine()->getRepository(Cupon::class)->findOneBy(['codigo' => $cuponDescuento]);

                    if ($Cupon) {
                        //creamos una venta cupon

                        $Ventacupon->setCuponIdcupon($Cupon);
                        $Ventacupon->setFechaactualizacion(new \DateTime("now"));

                        if (!$savedVentaCupon)
                            $Ventacupon->setPorcentajedescuento($Cupon->getPorcentajedescuento());
                        $Ventacupon->setVentaIdventa($Venta);

                        if ($newCupon) {
                            $usedCupons = ($Cupon->getCuponesusados()) ? $Cupon->getCuponesusados() : 0;
                            $Cupon->setCuponesusados($usedCupons + 1);
                            $em->persist($Cupon);
                        }
                        $em->persist($Ventacupon);
                        //$em->flush();
                        //$Venta->set
                    }
                } else {
                    //reseteamos los cupones que haya
                    $query = $em->createQuery(
                        'SELECT vc
                        FROM App\Entity\Ventacupon vc
                        inner join  vc.ventaIdventa v
                        inner join  vc.cuponIdcupon c
                        where vc.status=:status  and v.idventa=:idventa
                        '
                    )->setParameters(['status' => '1', 'idventa' => $Venta->getIdventa()]);
                    $VentacuponesReset = $query->getResult();
                    if ($VentacuponesReset) {
                        foreach ($VentacuponesReset as $keyVentacupones => $Ventacupon) {

                            $Ventacupon->setStatus("0");
                            $Cupon = $Ventacupon->getCuponIdcupon();

                            $usedCupons = ($Cupon->getCuponesusados()) ? $Cupon->getCuponesusados() : 0;

                            if ($usedCupons > 0) {
                                $Cupon->setCuponesusados($usedCupons - 1);
                                $em->persist($Cupon);
                            }
                            $em->persist($Ventacupon);
                            //$em->flush();
                        }
                    }
                }


                $Venta->setLiquidada('0');

                if ($esNuevaventa) {
                    $empresaId = $Empresa ? $Empresa->getIdempresa() : null;
                    $query = $em->createQuery(
                        'SELECT v
                    FROM App\Entity\Venta v
                    INNER JOIN v.sucursalIdsucursal s
                    INNER JOIN s.empresaIdempresa e
                    WHERE  e.idempresa = :idempresa
                    ORDER BY v.idventa DESC'
                    )->setParameters(['idempresa' => $empresaId])->setMaxResults(1);

                    $Ventas = $query->getResult();


                    if (!empty($Ventas)) {
                        $ultimaVenta = $Ventas[0];
                    }

                    //cho "ultima venta ".count($ultimaVenta);
                    if ($ultimaVenta) {
                        $ultimoFolio = $ultimaVenta->getFolio();

                        $ultimoFolio = (((int)$ultimoFolio) + 1);
                        unset($ultimaVenta);
                    }
                }

                $Venta->setFolio($ultimoFolio);
                if (intval($diascredito) > 0)
                    $Venta->setDiascredito($diascredito);
                else
                    $Venta->setDiascredito(0);

                $em->persist($Venta);


                $em->flush();

                /*if ($Pago) {
                    $Pago->setVentaIdventa($Venta);
                    $em->persist($Pago);
                    $em->flush();
                }*/


                //procesamos los pagos
                $responseGuardarPagos = $this->guardarPagos($pagos, $Venta, $esNuevaventa, $meses, $Cliente, $updateToSale);


                if ($responseGuardarPagos['exito']) {
                } else {
                    throw new \Exception($responseGuardarPagos['msj']);
                }


                $query = $em->createQuery(

                    'SELECT p
                    FROM App\Entity\Pago p

                    inner join  p.ventaIdventa v

                    where p.status=:status and v.idventa=:idventa
                    '

                )->setParameters(['status' => '1', 'idventa' => $Venta->getIdventa()]);

                $pagos = $query->getResult();

                $totalPagado = 0;

                foreach ($pagos as $key => $Pago) {
                    $totalPagado += $Pago->getMonto();
                }

                // Set pagado as the sum of total (subtotal) + iva
                $Venta->setPagado($Venta->getTotal() + $Venta->getIva());

                $totalVenta = $Venta->getTotal() + $Venta->getIva();

                if (round($totalPagado, 2) >= round($totalVenta, 2)) {
                    $Venta->setLiquidada('1');
                } else {
                    $Venta->setLiquidada('0');
                }

                $em->persist($Venta);
                $em->flush();

                //$em->persist($Venta);

                /*****************************ESTO SIEMPRE SE HACE***********************************/

                if ($esNuevaventa || $esCotizacion == '1' || $updateToSale) {
                    error_log("Graduaciones recibidas: " . print_r($graduaciones, true));

                    $responseGuardarProductosVenta = $this->guardarProductosVenta($productos, $Sucursal, $Venta, $esNuevaventa, $esCotizacion, $apartararmazon, $updateToSale, $graduaciones);

                    /*var_dump($responseGuardarProductosVenta);
                    echo "<br>";
                    var_dump($apartararmazon);*/
                    if ($responseGuardarProductosVenta['exito']) {
                        if ($esCotizacion != "1") {
                            //si es venta
                            $Venta->setSedescontodeinventario("1");
                        } else {
                            $Venta->setSeapartoarmazon($apartararmazon);
                        }
                        //$em->persist($Venta);
                        //$em->flush();
                    } else {
                        throw new \Exception($responseGuardarProductosVenta['msj']);
                    }
                }

                //$em=$this->getDoctrine()->getManager();

                $query = $em->createQuery(
                    'SELECT sv.idstockventa
                    FROM App\Entity\Stockventa sv
                    INNER JOIN sv.ventaIdventa v
                    WHERE sv.status = :status and v.idventa=:idventa AND (sv.preciofinal < sv.costo) '
                )->setParameters(['status' => '1', 'idventa' => $Venta->getIdventa()]);

                $underCostStockVentas = $query->getResult();

                if (count($underCostStockVentas) > 0) {
                    $Venta->setVentaproductodebajodelcosto('1');

                    //$em->flush();
                }


                if ($authorizationNumber != '')
                    $Venta->setAuthorizationnumber($authorizationNumber);
                else
                    $Venta->setAuthorizationnumber(null);

                if ($authorizationScan != '') {

                    $query = $em->createQuery(
                        'SELECT DISTINCT v.authscan, v.idventa
                     FROM App\Entity\Venta v 
                     WHERE v.authscan = :scan AND v.status = 1'
                    )->setParameter('scan', $authorizationScan);

                    $value = $query->getOneOrNullResult();

                    if ($value && $value['idventa'] != $Venta->getIdventa())
                        throw new \Exception('Autorización previamente utilizada.');

                    $AuthStage = $em->getRepository(Authstage::class)->findOneBy(array('stageorder' => 1)) ?? new Authstage();
                    $AuthStage->setStageorder(1);
                    $AuthStage->setName("En Sucursal");
                    $em->persist($AuthStage);
                    $em->flush();

                    if ($Venta->getAuthscan() != $authorizationScan) {
                        $tempSaleLog = new Salelog();
                        $tempSaleLog->setName("En Sucursal");
                        $tempSaleLog->setDate(new \DateTime("now"));
                        $tempSaleLog->setAuthstageIdauthstage($AuthStage);
                        $tempSaleLog->setVentaIdventa($Venta);
                        $tempSaleLog->setUsuarioIdusuario($this->getUser());

                        $em->persist($tempSaleLog);
                    }

                    $Venta->setAuthscan($authorizationScan);
                    $Venta->setAutorizacionstate('1');
                    $Venta->setAuthstageIdauthstage($AuthStage);
                }

                $em->persist($Venta);
                $em->flush();

                $exito = true;

                $saveBeneficiariesResponse = $this->saveBeneficiaries($Venta, $beneficiaryIds);


                $msj .= $saveBeneficiariesResponse['msg'];

                /*if ($Venta->getCotizacion() == '0') {
                    $this->twilioService->sendThanksPurchase($Cliente->getTelefono(), $Sucursal->getNombre(), $Cliente->getNombreCompleto());
                }*/

                $idventa = -1;

                if ($Venta->getIdventa())
                    $idventa = $Venta->getIdventa();
            } else {
                throw new \Exception("Las cabtidades de los productos no son correctas ");
            }
        } catch (\Exception $e) {
            $exito = false;
            //$msj=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();
            $msj = $e->getMessage();
        }

        return $this->json(['exito' => $exito, 'msj' => $msj, 'folio' => $ultimoFolio, 'idventa' => $idventa]);
    }

    function saveBeneficiaries($Sale, $beneficiaries)
    {

        $em = $this->getDoctrine()->getManager();
        $success = false;
        $msg = "";

        try {

            $query = $em->createQuery(

                'SELECT bv
                FROM App\Entity\Beneficiarioventa bv

                inner join  bv.ventaIdventa v

                where v.idventa=:idventa
                '

            )->setParameters(['idventa' => $Sale->getIdventa()]);

            $beneficiariesSales = $query->getResult();

            foreach ($beneficiariesSales as $beneficiarySale) {

                $em->remove($beneficiarySale);
            }

            $em->flush();

            if (is_array($beneficiaries)) {

                $beneficiariosString = "";
                foreach ($beneficiaries as $beneficiaryId) {

                    $Beneficiary = $this->getDoctrine()->getRepository(Cliente::class)->findOneBy(['idcliente' => $beneficiaryId]);

                    $BeneficiatySale = new Beneficiarioventa();
                    $BeneficiatySale->setVentaIdventa($Sale);
                    $BeneficiatySale->setClienteIdcliente($Beneficiary);

                    $beneficiariosString .= $Beneficiary->getNombre() . ' ' . $Beneficiary->getapellidoPaterno() . ' ' . $Beneficiary->getapellidoMaterno() . ', ';

                    $em->persist($BeneficiatySale);
                }

                $Sale->setBeneficiario($beneficiariosString);

                $em->flush();
            }
            $success = true;
        } catch (\Exception $e) {
            $msg = $e->getMessage() . " linea " . $e->getLine() . " archivo " . $e->getFile();
        }

        return ['success' => $success, 'msg' => $msg];
    }

    function guardarProductosVenta($productos, $Sucursal, $Venta, $esNuevaventa, $esCotizacion, $apartararmazon, $updateToSale, $graduaciones): array
    {
        $em = $this->getDoctrine()->getManager();
        $exito = false;
        $msj = "";

        try {

            //si la venta ya existe hacemos un reset de losproductos por si cambiaron
            if (!$esNuevaventa) {
                //reseteamos los prpductos por si se elimino uno

                $query = $em->createQuery(
                    'SELECT sv
               FROM App\Entity\Stockventa sv
               inner join  sv.ventaIdventa v

               where sv.status=:status and v.idventa=:idventa
               '
                )->setParameters(['status' => '1', 'idventa' => $Venta->getIdventa()]);


                $ProductosReset = $query->getResult();

                foreach ($ProductosReset as $keyPago => $StockventaAux) {
                    $stock = $StockventaAux->getStockIdstock();
                    $StockventaAux->setStatus("0");

                    if ($StockventaAux->getEstaapartado() == "1") {
                        //si estaba apartado se pasa al stock
                        $stockActual = $stock->getCantidad();
                        $stockRegresar = $StockventaAux->getCantidad();
                        $stockCantidad = $stockActual + $stockRegresar;

                        $stockApartado = $stock->getApartados();

                        $stock->setApartados($stockApartado - $stockRegresar);

                        /*  if($stockCantidad < 0){
                            $stockCantidad=0;
                        }*/
                        $stock->setCantidad($stockCantidad);
                        $StockventaAux->setCantidad(0);
                    }

                    $em->persist($StockventaAux);
                    $em->persist($stock);
                }
            }

            foreach ($productos as $key => $productoElegido) {


                $idstockventa = $productoElegido['idstockventa'];
                $idRow = $productoElegido['id'];

                $preciostockventa = $productoElegido['precioOriginal'];
                $tipoproducto = $productoElegido['tipoproducto'];
                $idproducto = $productoElegido['idproducto'];
                $productoCantidad = $productoElegido['productoCantidad'];
                $fixproduct = $productoElegido['fixproduct'];

                $Producto = null;
                $Stock = null;

                if ($tipoproducto == "2") { //es servicio
                    $Producto = $this->getDoctrine()->getRepository(Producto::class)->findOneBy(['idproducto' => $idproducto]);
                    //buscamos el stock si no lo creamos

                    $query = $em->createQuery(
                        'SELECT s
                        FROM App\Entity\Stock s
                        inner join s.sucursalIdsucursal suc
                        inner join s.productoIdproducto p
                        where s.status=:status and p.idproducto=:idproducto and suc.idsucursal=:idsucursal
                        '
                    )->setParameters(['status' => '1', 'idproducto' => $idproducto, 'idsucursal' => $Sucursal->getIdsucursal()]);
                    $Stock = $query->getResult();
                    if ($Stock) {
                        $Stock = $Stock[0];
                    }
                    if (!$Stock) {
                        $Stock = new Stock();
                        $Stock->setProductoIdproducto($Producto);
                        $Stock->setSucursalIdsucursal($Sucursal);
                        $Stock->setCantidad("1");
                        $Stock->setCreacion(new \DateTime("now"));
                        $Stock->setModificacion(new \DateTime("now"));

                        $tmpStockState = $em->getRepository(Stockstate::class)->findOneBy(array('name' => "DISPONIBLE"));
                        $Stock->setStockstateIdstockstate($tmpStockState);
                    }
                } else {
                    $idstock = $productoElegido['idstock'];

                    $Stock = $this->getDoctrine()->getRepository(Stock::class)->findOneBy(['idstock' => $idstock]);
                    $Producto = $Stock->getProductoIdproducto();
                }

                if ($idstockventa != "") {
                    // echo "1";
                    $Stockventa = $this->getDoctrine()->getRepository(Stockventa::class)->findOneBy(['idstockventa' => $idstockventa]);
                    $Stockventa->setModificacion(new \DateTime("now"));
                } else {
                    // echo "2";
                    $Stockventa = new Stockventa();
                    $Stockventa->setCreacion(new \DateTime("now"));
                    $Stockventa->setModificacion(new \DateTime("now"));
                }

                $Stockventa->setCantidad($productoCantidad);
                $Stockventa->setFixproduct($fixproduct);
                //solo se descuenta si es venta o es cotizacion y apartado
                if ($tipoproducto != "2" && ($esCotizacion == "0" || ($esCotizacion == "1" && $apartararmazon == "1"))) {

                    $cantidadRestante = $Stock->getCantidad();
                    $cantidadRestante = $cantidadRestante - $productoCantidad;
                    $Stock->setCantidad($cantidadRestante);
                }


                $tmpCost = ($Stock->getCosto() > 0) ? $Stock->getCosto() : $Producto->getCosto();

                $Stockventa->setCosto($tmpCost);

                // if($tipoproducto=="2"){
                //aqui va lo nuevo de la base de adtos
                // $Stockventa->setProductoIdproducto($Producto);
                // }else{
                $Stockventa->setStockIdstock($Stock);
                // }


                if ($esNuevaventa) {
                    $Stockventa->setPreciobase($productoElegido['precioOriginal']);
                }

                $Stockventa->setPrecio($productoElegido['productoPrecio']);


                $Stockventa->setStatus("1");


                $Stockventa->setVentaIdventa($Venta);
                $Stockventa->setPorcentajedescuento($productoElegido['productoDescuento']);
                $Stockventa->setPreciofinal($productoElegido['productoPrecioFinal']);
                $Stockventa->setStatus("1");

                //if ($updateToSale && $apartararmazon == "1") $Stock->setApartados($Stock->getApartados() - $productoCantidad);

                if ($apartararmazon == "1" && $esCotizacion) {
                    $Stock->setApartados($Stock->getApartados() + $productoCantidad);
                    $Stockventa->setEstaapartado("1");
                    //$Stock->setApartados($productoCantidad);
                }

                $Stockventa->setIsomittable($Stockventa->getIsomittable() == '0' ? $productoElegido['isOmittable'] : $Stockventa->getIsomittable());

                $em->persist($Stock);
                $em->persist($Stockventa);

                if(is_array($graduaciones) && isset($graduaciones[$idRow])){
                    $grad = $graduaciones[$idRow];
                    $orden = null;
                    $stockvol = null;

                    // Verificar si el producto cumple con los criterios para tener graduación
                    $isValidForGraduation = false;

                    // Obtener información del producto
                    $producto = $Stock->getProductoIdproducto();
                    $tipoproducto = $producto->getTipoproducto();
                    $categoria = $producto->getCategoriaIdcategoria();
                    $barcode = $Stock->getCodigobarras();

                    // Validar según los criterios:
                    // 1. Es un armazón (tipoproducto = '1') Y su clase es 3 (armazón)
                    // 2. O tiene un código de barras específico ('111389607921')
                    if ($tipoproducto === '1' && $categoria) {
                        $clase = $categoria->getClaseIdclase();
                        if ($clase && $clase->getIdclase() == 3) {
                            $isValidForGraduation = true;
                        }
                    }

                    if ($barcode === '111389607921') {
                        $isValidForGraduation = true;
                    }

                    // Si no cumple con los criterios, no crear orden de laboratorio
                    if (!$isValidForGraduation) {
                        continue;
                    }

                    $existingStockvol = $em->getRepository(Stockventaordenlaboratorio::class)
                        ->findOneBy(['stockventaIdstockventa' => $Stockventa]);

                    if($existingStockvol){
                        $orden = $existingStockvol->getOrdenlaboratorioIdordenlaboratorio();
                        $stockvol = $existingStockvol;
                    }

                    if (
                        (isset($grad['odEsfera']) && !empty($grad['odEsfera'])) ||
                        (isset($grad['oiEsfera']) && !empty($grad['oiEsfera'])) ||
                        (isset($grad['odCilindro']) && !empty($grad['odCilindro'])) ||
                        (isset($grad['oiCilindro']) && !empty($grad['oiCilindro'])) ||
                        (isset($grad['odEje']) && !empty($grad['odEje'])) ||
                        (isset($grad['oiEje']) && !empty($grad['oiEje'])) ||
                        (isset($grad['distanciaPupilar']) && !empty($grad['distanciaPupilar'])) ||
                        (isset($grad['altura']) && !empty($grad['altura'])) ||
                        (isset($grad['_aco']) && !empty($grad['_aco']))
                    ) {
                        // Si no existe la orden, creamos una nueva
                        if($orden == null){
                            $orden = new Ordenlaboratorio();
                            $orden->setCreacion(new \DateTime());
                            $orden->setStatus('1');
                            $orden->setRefusedclient('0');

                            // Establecer cliente y beneficiario
                            $orden->setClienteIdcliente($Cliente);
                            if ($beneficiario && $beneficiario != "") {
                                $Beneficiario = $em->getRepository(Cliente::class)->findOneBy(array('idcliente' => $beneficiario));
                                if ($Beneficiario) {
                                    $orden->setBeneficiarioIdbeneficiario($Beneficiario);
                                }
                            }
                        }

                        // Actualizamos los datos de la orden
                        $orden->setActualizacion(new \DateTime());
                        $orden->setEsferaod($grad['odEsfera'] ?? null);
                        $orden->setEsferaoi($grad['oiEsfera'] ?? null);
                        $orden->setCilindrood($grad['odCilindro'] ?? null);
                        $orden->setCilindrooi($grad['oiCilindro'] ?? null);
                        $orden->setEjeod($grad['odEje'] ?? null);
                        $orden->setEjeoi($grad['oiEje'] ?? null);
                        $orden->setDip($grad['distanciaPupilar'] ?? null);
                        $orden->setAo($grad['altura'] ?? null);
                        $orden->setAco($grad['_aco'] ?? null);
                        $orden->setObservaciones($grad['observaciones'] ?? null);

                        // Establecer el stage desde los datos de graduación (por defecto 9 = Pendiente)
                        $stageValue = $grad['stage'] ?? '9';
                        error_log("GUARDAR VENTA - Stage recibido: " . $stageValue);
                        $orden->setStage($stageValue);
                        error_log("GUARDAR VENTA - Stage establecido: " . $orden->getStage());

                        $em->persist($orden);
                        $em->flush();

                        // Si no existe el stockventaordenlaboratorio, creamos uno nuevo
                        if($stockvol == null){
                            $stockvol = new Stockventaordenlaboratorio();
                            $stockvol->setCreacion(new \DateTime());
                            $stockvol->setAlreadyset('1');
                            $stockvol->setMainproduct('1');
                            $stockvol->setStockventaordenlaboratorioIdstockventaordenlaboratorio(null);
                        }

                        // Vinculamos el stockventaordenlaboratorio con la orden y el stockventa
                        $stockvol->setStockventaIdstockventa($Stockventa);
                        $stockvol->setOrdenlaboratorioIdordenlaboratorio($orden);

                        $em->persist($stockvol);
                        $em->flush();

                        // Actualizar el status del stockventa para indicar que ya tiene orden de laboratorio
                        $Stockventa->setStatus('1');
                        $em->persist($Stockventa);
                        $em->flush();
                    }
                }

            }



            // Procesamiento de graduaciones sin idRow (método anterior)
            // Este código se mantiene para compatibilidad con versiones anteriores
         /*   if (is_array($graduaciones) && count($graduaciones) > 0) {
                foreach ($graduaciones as $key => $grad) {
                    // Saltamos las graduaciones que ya fueron procesadas con el nuevo enfoque (idRow)
                    if (is_numeric($key)) {
                        continue; // Si la clave es numérica, asumimos que es un idRow y ya fue procesado
                    }

                    // Buscar el Stockventa correspondiente
                    if (!isset($grad['idstockventa']) || empty($grad['idstockventa'])) {
                        continue;
                    }

                    $stockventaObj = $em->getRepository(Stockventa::class)->find($grad['idstockventa']);
                    if (!$stockventaObj) {
                        continue;
                    }

                    // Verificar si ya existe un Stockventaordenlaboratorio para este Stockventa
                    $existingStockvolObj = $em->getRepository(Stockventaordenlaboratorio::class)
                        ->findOneBy(['stockventaIdstockventa' => $stockventaObj]);

                    if ($existingStockvolObj) {
                        // Si ya existe, continuamos con el siguiente
                        continue;
                    }

                    // Verificar si el producto cumple con los criterios para tener graduación
                    $isValidForGraduation = false;

                    // Obtener información del producto
                    $stock = $stockventaObj->getStockIdstock();
                    if (!$stock) {
                        continue;
                    }

                    $producto = $stock->getProductoIdproducto();
                    if (!$producto) {
                        continue;
                    }

                    $tipoproducto = $producto->getTipoproducto();
                    $categoria = $producto->getCategoriaIdcategoria();
                    $barcode = $stock->getCodigobarras();

                    // Validar según los criterios:
                    // 1. Es un armazón (tipoproducto = '1') Y su clase es 3 (armazón)
                    // 2. O tiene un código de barras específico ('111389607921')
                    if ($tipoproducto === '1' && $categoria) {
                        $clase = $categoria->getClaseIdclase();
                        if ($clase && $clase->getIdclase() == 3) {
                            $isValidForGraduation = true;
                        }
                    }

                    if ($barcode === '111389607921') {
                        $isValidForGraduation = true;
                    }

                    // Si no cumple con los criterios, no crear orden de laboratorio
                    if (!$isValidForGraduation) {
                        continue;
                    }

                    // Verificar si hay datos de graduación válidos
                    if (
                        (isset($grad['odEsfera']) && !empty($grad['odEsfera'])) ||
                        (isset($grad['oiEsfera']) && !empty($grad['oiEsfera'])) ||
                        (isset($grad['odCilindro']) && !empty($grad['odCilindro'])) ||
                        (isset($grad['oiCilindro']) && !empty($grad['oiCilindro'])) ||
                        (isset($grad['odEje']) && !empty($grad['odEje'])) ||
                        (isset($grad['oiEje']) && !empty($grad['oiEje'])) ||
                        (isset($grad['distanciaPupilar']) && !empty($grad['distanciaPupilar'])) ||
                        (isset($grad['altura']) && !empty($grad['altura'])) ||
                        (isset($grad['_aco']) && !empty($grad['_aco']))
                    ) {
                        // Crear nueva orden de laboratorio
                        $ordenObj = new Ordenlaboratorio();
                        $ordenObj->setCreacion(new \DateTime());
                        $ordenObj->setActualizacion(new \DateTime());
                        $ordenObj->setEsferaod($grad['odEsfera'] ?? null);
                        $ordenObj->setEsferaoi($grad['oiEsfera'] ?? null);
                        $ordenObj->setCilindrood($grad['odCilindro'] ?? null);
                        $ordenObj->setCilindrooi($grad['oiCilindro'] ?? null);
                        $ordenObj->setEjeod($grad['odEje'] ?? null);
                        $ordenObj->setEjeoi($grad['oiEje'] ?? null);
                        $ordenObj->setDip($grad['distanciaPupilar'] ?? null);
                        $ordenObj->setAo($grad['altura'] ?? null);
                        $ordenObj->setAco($grad['_aco'] ?? null);
                        $ordenObj->setObservaciones($grad['observaciones'] ?? null);
                        $ordenObj->setStatus('1');
                        $ordenObj->setRefusedclient('0');

                        $em->persist($ordenObj);
                        $em->flush();

                        // Crear Stockventaordenlaboratorio y vincularlo con la orden
                        $stockvolObj = new Stockventaordenlaboratorio();
                        $stockvolObj->setStockventaIdstockventa($stockventaObj);
                        $stockvolObj->setCreacion(new \DateTime());
                        $stockvolObj->setOrdenlaboratorioIdordenlaboratorio($ordenObj);
                        $stockvolObj->setAlreadyset('1');
                        $stockvolObj->setMainproduct('1');
                        $stockvolObj->setStockventaordenlaboratorioIdstockventaordenlaboratorio(null);

                        $em->persist($stockvolObj);
                        $em->flush();

                        // Actualizar el status del stockventa
                        $stockventaObj->setStatus('1');
                        $em->persist($stockventaObj);
                        $em->flush();
                    }
                }
            }
*/

            $em->flush();
            $exito = true;
        } catch (\Exception $e) {
            $exito = false;
            $msj = $e->getMessage() . " linea " . $e->getLine() . " archivo " . $e->getFile();
        }
        return ['exito' => $exito, 'msj' => $msj];
    }

    function guardarPagos($pagos, $Venta, $esNuevaventa, $meses, $Client, $updateToSale)
    {
        $em = $this->getDoctrine()->getManager();
        $exito = false;
        $msj = "";
        try {
            if ($esNuevaventa != "1") {
                $query = $em->createQuery('SELECT p
               FROM App\Entity\Pago p              
               inner join  p.ventaIdventa v        
               where p.status=:status and v.idventa=:idventa 
               ')->setParameters(['status' => '1', 'idventa' => $Venta->getIdventa()]);

                $PagosReset = $query->getResult();

                $totalPagadoTemp = 0;

                $pagoEraser = new PagoEraser($em);

                //ve si es cotización, sino es entra
                if (!$updateToSale) {
                    foreach ($PagosReset as $keyPago => $Pago) {

                        $pagoEraser->erasePago($Pago);
                        $totalPagadoTemp += $Pago->getMonto();

                    }
                }

                if ($Venta->getCredito() == '1' && $updateToSale) {


                    $Client->setDebt($Client->getDebt() + $Venta->getPagado());

                    $em->persist($Client);
                }
            } else {
                if ($Venta->getCredito() == '1' && $Venta->getCotizacion() != '1' && $esNuevaventa == "1") {

                    $Client->setDebt($Client->getDebt() + $Venta->getPagado());

                    $em->persist($Client);
                }
            }

            //if( $Venta->getConvenio() !="UAM" && $Venta->getConvenio() !="UAM Auditivos"){
            $totalPagado = 0;

            if (is_array($pagos)) {
                foreach ($pagos as $keyPago => $pago) {
                    $Pago = new Pago();
                    if ($pago['idpago'] != "" && $pago['idpago'] != NULL) {
                        $Pago = $this->getDoctrine()->getRepository(Pago::class)->findOneBy(['idpago' => $pago['idpago']]);
                    } else {
                        $Pago->setFecha($Pago->getFecha() ?? new \DateTime("now"));
                    }


                    $totalPagado = $totalPagado + $Pago->getMonto() ?? $pago['monto'];

                    $tempPaymenttype = $em->getRepository(Paymenttype::class)->findOneBy(array('idpaymenttype' => $pago['tipopagoanticipo']));

                    if ($tempPaymenttype)
                        $Pago->setPaymenttypeIdpaymenttype($tempPaymenttype);
                    else
                        throw new \Exception('No se encontró el tipo de pago');

                    $Pago->setPaymenttypeIdpaymenttype($tempPaymenttype);

                    try {
                        $Pago->setMonto($Pago->getMonto() ?? $pago['monto']);
                        if ($meses)
                            $Pago->setMesesintereses($meses);
                        $Pago->setVentaIdventa($Venta);
                        $Pago->setStatus("1");
                        if (isset($pago['automatic'])) {
                            $Pago->setAutomatic($pago['automatic']);
                        }
                        $Pago->setUsuarioCobro($Venta->getGerente());
                        $em->persist($Pago);


                    } catch (\Exception $e) {
                        $exito = false;
                        //$msj = $e->getMessage() . " linea " . $e->getLine() . " archivo " . $e->getFile();
                        $msj = $e->getMessage();
                    }

                    //$em->flush();
                }
                $em->flush();
            }

            if ($Venta->getCredito() == '1' && $Venta->getCotizacion() != '1') {


                $Client->setDebt($Client->getDebt() - $totalPagado);

                if ($Client->getDebt() > $Client->getAuthorizedcredit())
                    throw new \Exception("No tienes suficiente crédito para esta venta ");

                $em->persist($Client);
            }


            $em->flush();

            $exito = true;
        } catch (\Exception $e) {
            $exito = false;
            //$msj = $e->getMessage() . " linea " . $e->getLine() . " archivo " . $e->getFile();
            $msj = $e->getMessage();
        }

        return ['exito' => $exito, 'msj' => $msj];
    }

    function guardarProductos($productos)
    {
        foreach ($productos as $key => $productoElegido) {
            $idstockventa = $productoElegido['idstockventa'];
            $tipoproducto = $productoElegido['tipoproducto'];
            $idproducto = $productoElegido['idproducto'];
            $productoCantidad = $productoElegido['productoCantidad'];
        }
    }

    /**
     * @Route("/buscar-venta", name="buscar-venta")
     */
    public function buscarVenta(Request $request)
    {
        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $numeroFolio = $request->get("numeroFolio");
        $idempresa = $request->get("idempresa");

        $Venta = -1;
        $ventaLiquidada = false;

        try {
            $Usuario = $this->getUser();
            $Sucursal = $Usuario->getSucursalIdsucursal();
            $Empresa = $Sucursal ? $Sucursal->getEmpresaIdempresa() : null;

            $idempresa = $Empresa ? $Empresa->getIdempresa() : null;

            $query = $em->createQuery(
                'SELECT v.idventa, v.liquidada
            FROM App\Entity\Venta v
            INNER JOIN v.sucursalIdsucursal s
            INNER JOIN s.empresaIdempresa e
            WHERE v.status=:status AND v.folio=:folio AND e.idempresa = :idempresa'
            )->setParameters(['status' => '1', 'folio' => $numeroFolio, 'idempresa' => $idempresa]);

            $Venta = $query->getOneOrNullResult();


            if ($Venta) {
                $exito = true;

                // Determinar si la venta está liquidada usando el campo `liquidada`
                if ($Venta['liquidada'] == 1) {
                    $ventaLiquidada = true;
                }

                // Aquí iría el código adicional para obtener productos, pagos, cupones, etc.

            } else {
                $msj = "No se encontró ningún resultado";
            }
        } catch (\Exception $e) {
            $exito = false;
            $msj = $e->getMessage();
        }

        return $this->json([
            'venta' => $Venta,
            'empresa' => $idempresa,
            'exito' => $exito,
            'msj' => $msj,
            'ventaLiquidada' => $ventaLiquidada
        ]);
    }


    /**
     * @Route("/buscar-cupon", name="buscar-cupon")
     */
    public function buscarCupon(Request $request)
    {
        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $codigoCupon = $request->get("cuponDescuento");
        $idcliente = "";
        $beneficiarioNombre = "";
        $convenio = "";
        $numeroEmpleado = "";
        $marcas = [];
        $emailCliente = "";
        $marcas = [];

        try {
            $query = $em->createQuery(
                'SELECT  cm.idcuponmarca,c.codigo,m.idmarca, c.numerocupones, c.cuponesusados,
                m.nombre,c.porcentajedescuento 
               FROM App\Entity\Cuponmarca cm
               INNER JOIN cm.cuponIdcupon c
               LEFT JOIN  cm.marcaIdmarca m

               where cm.status=:status and c.codigo=:codigo
               '
            )->setParameters(['status' => '1', 'codigo' => $codigoCupon]);
            $marcas = $query->getResult();
            //obtenemos los productos
            if ($marcas) {
                $availableCupons = $marcas[0]["numerocupones"];
                $usedCupons = $marcas[0]["cuponesusados"];

                if ($availableCupons <= $usedCupons)
                    throw new \Exception("Este cupón ya no es válido");

                $exito = true;
            } else {
                $msj = "No se encontró nigún resultado";
            }
        } catch (\Exception $e) {
            $exito = false;
            //$msj = $e->getMessage() . " linea " . $e->getLine() . " archivo " . $e->getFile();
            $msj = $e->getMessage();
        }


        return $this->json(['exito' => $exito, 'msj' => $msj, 'marcas' => $marcas]);
    }

    /**
     * @Route("/obtener-informacion-tipo-venta", name="obtener-informacion-tipo-venta")
     */
    public function obtenerInformacionTipoVenta(Request $request)
    {
        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $idtipoventa = $request->get("idtipoventa");  // <-- mantiene tu forma original
        $Tipoventa = null;

        try {
            // 🔍 Mantienes la consulta tal cual tú la haces
            $query = $em->createQuery(
                'SELECT tv
             FROM App\Entity\Tipoventa tv
             WHERE tv.status = :status AND tv.idtipoventa = :idtipoventa'
            )->setParameters([
                'status' => '1',
                'idtipoventa' => $idtipoventa
            ]);

            $Tipoventa = $query->getOneOrNullResult();

            if ($idtipoventa == "1748") {
                $query = $em->createQuery(
                    'SELECT MAX(v.idventa) as maxId
                 FROM App\Entity\Venta v'
                );
                $result = $query->getSingleScalarResult();
                $lastId = $result ? (int) $result : 0;

                $random = mt_rand(1, 100);

                if ($random <= 40) {
                    // Blue (40%)
                    $fixProducts = "121377611613,121962990190";
                } elseif ($random <= 70) {
                    // Policarbonato (40%)
                    $fixProducts = "121377612614,121962990190";
                } else {
                    // Ambos (20%)
                    $fixProducts = "121377612614,121377611613,121962990190";
                }

                $Tipoventa->setFixproducts($fixProducts);
            }

            $exito = true;

        } catch (\Exception $e) {
            $exito = false;
            $msj = $e->getMessage() . " línea " . $e->getLine() . " archivo " . $e->getFile();
        }

        return $this->json([
            'exito' => $exito,
            'msj' => $msj,
            'Tipoventa' => $Tipoventa,
            'pagoalfinal' => $Tipoventa ? $Tipoventa->getPagoalfinal() : null
        ]);
    }



    /**
     * @Route("/quitar-cupon", name="quitar-cupon")
     */
    public function quitarCupon(Request $request)
    {
        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $codigoCupon = $request->get("cuponDescuento");
        $idventa = $request->get("idventa");


        try {
            $query = $em->createQuery(
                'SELECT  vc 
               FROM App\Entity\Ventacupon vc
               inner join vc.cuponIdcupon c
               inner join  vc.ventaIdventa v


               where vc.status=:status and c.codigo=:codigo and v.idventa=:idventa
               '
            )->setParameters(['status' => '1', 'idventa' => $idventa, 'codigo' => $codigoCupon]);


            $Ventacupones = $query->getResult();
            //obtenemos los productos
            if ($Ventacupones) {

                foreach ($Ventacupones as $Ventacupon) {
                    $Ventacupon->setStatus("0");
                    $Cupon = $Ventacupon->getCuponIdcupon();

                    $usedCupons = ($Cupon->getCuponesusados()) ? $Cupon->getCuponesusados() : 0;

                    if ($usedCupons > 0) {
                        $Cupon->setCuponesusados($usedCupons - 1);
                        $em->persist($Cupon);
                    }

                    $em->persist($Ventacupon);

                    $em->flush();
                }
                $exito = true;
            } else {
                $msj = "No se encontró nigún resultado";
            }
        } catch (\Exception $e) {
            $exito = false;
            $msj = $e->getMessage() . " linea " . $e->getLine() . " archivo " . $e->getFile();
        }


        return $this->json(['exito' => $exito, 'msj' => $msj]);
    }

    /**
     * @Route("/detalle-visor", name="visor-documentos")
     */
    public function detalleDocumentosVisor(Request $request): Response
    {
        // Obtener parámetros enviados en la petición: opción, folio y id de empresa
        $opcion = $request->get('opcion');
        $folio = $request->get('folio');
        $idEmpresa = (int)$request->get('idempresa');

        // Obtener el EntityManager para consultas Doctrine
        $em = $this->getDoctrine()->getManager();
        // Buscar la empresa en la base de datos según el id recibido
        $empresa = $em->getRepository(Empresa::class)->find($idEmpresa);

        $carpetaPub = '';  // Ruta pública base donde está el archivo
        $archivo = '';     // Nombre del archivo PDF que se mostrará
        $msj = '';         // Mensaje de error si ocurre algo
        $exito = false;    // Indica si el proceso fue exitoso

        try {
            /* ========== 1. Obtener los datos de la venta según folio y empresa ========== */
            $venta = $em->createQuery(
                'SELECT v
             FROM App\Entity\Venta v
             JOIN v.sucursalIdsucursal s
             JOIN s.empresaIdempresa e
             WHERE v.folio = :folio
               AND e.idempresa = :emp'
            )->setParameters(['folio' => $folio, 'emp' => $idEmpresa])
                ->getOneOrNullResult();

            if (!$venta) {
                // Si no encuentra la venta, lanza excepción para informar
                throw new \RuntimeException("Venta/archivo no encontrado");
            }

            /* ========== 2. Determinar archivo y carpeta pública según la opción solicitada ========== */
            // Se limpia el RFC para usarlo en la ruta
            $rfcSan = preg_replace('/\s+/', '', $empresa->getRfc());
            // Carpeta base absoluta donde están los tickets
            $baseAbs = $this->getParameter('carpetaTickets');
            // Carpeta base pública (ruta relativa web)
            $basePub = str_replace($this->getParameter('kernel.project_dir') . '/public', '', $baseAbs);
            // Carpeta pública para documentos de venta
            $baseDocPub = str_replace(
                $this->getParameter('kernel.project_dir') . '/public',
                '',
                $this->getParameter('carpetaDocumentosVenta')
            );

            // Según la opción, se define qué archivo y carpeta mostrar
            switch ($opcion) {
                case 'ticket':
                    $archivo = basename(trim($venta->getTicketpdf())); // archivo ticket normal
                    $carpetaPub = "{$basePub}/{$rfcSan}/{$folio}";     // ruta carpeta tickets
                    break;

                case 'especial':
                    $archivo = basename(trim($venta->getTickerpdfespecial())); // ticket especial
                    $carpetaPub = "{$basePub}/{$rfcSan}/{$folio}";
                    break;

                case 'archivoautorizacion':
                    $archivo = basename(trim($venta->getArchivoautorizacion())); // autorización
                    $carpetaPub = $baseDocPub;
                    break;

                case 'graduacion':
                    // Ticket graduación puede generarse si no existe
                    $archivo = trim($venta->getTicketgraduacion());
                    if (!$archivo) {
                        $archivo = sprintf('%s-graduacion.pdf', $venta->getFolio());
                        $venta->setTicketgraduacion($archivo);
                        $em->flush();
                    }
                    $carpetaPub = "{$basePub}/{$rfcSan}/{$folio}";
                    break;

                default:
                    // Si la opción no coincide, lanza error
                    throw new \InvalidArgumentException('Opción no reconocida');
            }

            // Validar que el archivo sea PDF
            if (strtolower(pathinfo($archivo, PATHINFO_EXTENSION)) !== 'pdf') {
                throw new \RuntimeException('El archivo debe ser un PDF válido.');
            }

            // Construir la ruta completa absoluta al archivo en el sistema de archivos
            $pathFs = $this->getParameter('kernel.project_dir') . '/public/' . ltrim($carpetaPub, '/') . '/' . $archivo;

            // Validar que la ruta esté dentro del directorio público para evitar accesos no autorizados
            $projectDir = realpath($this->getParameter('kernel.project_dir') . '/public');
            $filePath = realpath($pathFs);
            if (!$filePath || strpos($filePath, $projectDir) !== 0) {
                throw new \RuntimeException('Acceso denegado al archivo solicitado.');
            }

            // Crear la carpeta si no existe para guardar archivos
            $dirPath = dirname($pathFs);
            if (!is_dir($dirPath)) {
                mkdir($dirPath, 0777, true);
            }


            if (!is_file($pathFs)) {
                if ($opcion === 'graduacion') {
                    $domPdfOp = new Options();
                    $domPdfOp->set('isRemoteEnabled', false); // Deshabilita carga remota en PDF
                    $domPdfOp->set('defaultFont', 'Arial');

                    $dompdf = new Dompdf($domPdfOp);
                    // Tamaño personalizado del papel
                    $dompdf->set_paper([0, 0, 226.772, 1000]);

                    // Preparar datos para la plantilla
                    $twigData = $this->buildGraduacionData($venta, $empresa);
                    // Renderizar HTML del ticket graduación
                    $dompdf->loadHtml($this->renderView('ventas/ticket-graduacion.html.twig', $twigData));
                    $dompdf->render();
                    // Guardar PDF generado en disco
                    file_put_contents($pathFs, $dompdf->output());
                } else {
                    throw new \RuntimeException('El archivo solicitado no existe en disco');
                }
            }

            $exito = true;

        } catch (\Throwable $e) {
            // Captura errores, guarda mensaje y loguea la excepción
            $msj = $e->getMessage();
            $this->logger->error($e->getMessage(), ['exception' => $e]);
        }

        // Renderizar vista con los datos: archivo, carpeta, mensaje y éxito
        return $this->render('ventas/visor.html.twig', [
            'archivo' => $archivo,
            'carpeta' => $carpetaPub,
            'msj' => $msj,
            'exito' => $exito,
        ]);
    }

    /**
     * Prepara los datos necesarios para generar el ticket de graduación.
     */
    private function buildGraduacionData(Venta $venta, Empresa $empresa): array
    {
        $graduacionData = $this->getGraduacionData($venta);

        /* → CORREGIDO: Solo incluir productos que realmente tienen graduación */
        $stockventaordenlaboratorio = [];

        foreach ($graduacionData['productos'] as $prod) {

            // Busca la graduación que corresponde al id de producto
            $grad = array_filter(
                $graduacionData['graduaciones'],
                fn ($g) => $g['producto_id'] === $prod['id']
            );
            $grad = array_values($grad)[0] ?? null;   // primera coincidencia o null

            // SOLO agregar al array si el producto tiene graduación
            if ($grad !== null) {
                $stockventaordenlaboratorio[] = [
                    'stockventa' => $prod,   // Ya tiene la estructura correcta desde getGraduacionData
                    'graduacion' => $grad    // Los datos de graduación sin cambios
                ];
            }
        }

        // Obtener información del cliente
        $cliente = $venta->getClienteIdcliente();
        $clienteNombre = $cliente ? $cliente->getNombreCompleto() : '-';
        $clienteTelefono = $cliente ? $cliente->getTelefono() : '-';
        $clienteEmail = $cliente ? $cliente->getEmail() : '-';

        // Preparar datos para el ticket
        $gradData = [
            'logo' => $empresa->getLogo64(),
            'razonsocial' => $empresa->getRazonsocial(),
            'nombreSucursal' => $venta->getSucursalIdsucursal()->getNombre(),
            'nombreVendedor' => $venta->getUsuarioIdusuario()->getNombreVendedor(),
            'prefijo' => $empresa->getPrefijotickets(),
            'folio' => $venta->getFolio(),
            'fecha' => (new \DateTime())->format('d/m/Y H:i:s'),
            'cliente' => $clienteNombre,
            'clienteTelefono' => $clienteTelefono,
            'sinConvenioEmailCliente' => $clienteEmail,
            'stockventaordenlaboratorio' => $stockventaordenlaboratorio,
            'pieTicket' => $empresa->getPieticket() ?: 'Muchas gracias por su compra'
        ];

        return $gradData;
    }


    /**
     * @Route("/cancelar-venta/{idventa}", name="cancelar-venta")
     */
    public function cancelarVenta($idventa = "", Request $request)
    {
        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $cantidadproductos = 0;
        $porquesecancelo = $request->get('porquesecancelo');

        try {

            $query = $em->createQuery(
                '   SELECT  v
                    FROM App\Entity\Venta v
                    WHERE v.idventa = :idventa and v.status=:status
                '
            )->setParameters(["idventa" => $idventa, 'status' => "1"]);
            $Venta = $query->getOneOrNullResult();

            if ($Venta) {

                if ($Venta->getCotizacion() == '0') {
                    $currenUserRol = $this->getUser()->getRol();
                    if ($currenUserRol != "ROLE_SUPER_ADMIN" && $currenUserRol != "ROLE_VENDEDOR")
                        throw new \Exception("No tienes permisos para cancelar esta venta");
                }

                $query = $em->createQuery(
                    'SELECT sv.idstockventa
                    FROM App\Entity\Merma m
                    INNER JOIN m.stockventaIdstockventa sv
                    INNER JOIN sv.ventaIdventa v
                    WHERE v.idventa = :idventa
                '
                )->setParameters(["idventa" => $idventa]);
                $checkMerma = $query->getResult();

                if (count($checkMerma) > 0)
                    throw new \Exception("Hay una merma que está asociada con esta venta");

                $Venta->setStatus('0');
                $Venta->setFechacancelacion(new \DateTime('now'));
                $Venta->setPorquesecancelo($porquesecancelo);
                $Venta->setUsuarioResponsablecancelacion($this->getUser());

                $em->persist($Venta);


                $cotizacion = $Venta->getCotizacion();
                $seapartoarmazon = $Venta->getSeapartoarmazon();
                $sedescontodeinventario = $Venta->getSedescontodeinventario();


                $query = $em->createQuery(
                    '   SELECT sv
                    FROM App\Entity\Stockventa sv
                    inner join sv.ventaIdventa v
                    WHERE v.idventa = :idventa
                '
                )->setParameters(["idventa" => $idventa]);
                $StockVentas = $query->getResult();


                foreach ($StockVentas as $keystockventa => $Stockventa) {
                    $em->persist($Stockventa);
                    $cantidadproductos = $Stockventa->getCantidad();
                    if ($Stockventa->getStockIdstock()) {
                        $Stock = $Stockventa->getStockIdstock();
                        $Producto = $Stock->getProductoIdproducto();
                        $tipoproducto = $Producto->getTipoproducto();

                        /*echo "<br>tipoproducto ";var_dump($tipoproducto);
                        echo "<br>sedescontodeinventario ";var_dump($sedescontodeinventario);
                        echo "<br>cotizacion ";var_dump($cotizacion);
                        echo "<br>seapartoarmazon ";var_dump($seapartoarmazon);*/
                        //si no es servicio lo ponemos en stock
                        if ($tipoproducto != "2" && ($sedescontodeinventario == "1" || ($cotizacion == "1" && $seapartoarmazon == "1"))) {
                            $cantidadstock = $Stock->getCantidad();
                            $Stock->setCantidad($cantidadstock + $cantidadproductos);
                            if ($cotizacion == "1" && $seapartoarmazon == "1")
                                $Stock->setApartados($Stock->getApartados() - $cantidadproductos);
                            $em->persist($Stock);
                        }
                    }
                }

                $query = $em->createQuery(
                    '   SELECT p
                    FROM App\Entity\Pago p
                    inner join p.ventaIdventa v
                    WHERE v.idventa = :idventa AND p.status =:status
                '
                )->setParameters(["idventa" => $idventa, "status" => '1']);
                $Pagos = $query->getResult();

                $pagoEraser = new PagoEraser($em);

                if ($Pagos) {
                    foreach ($Pagos as $keypago => $Pago) {
                        //$Pago->setStatus("0");

                        $pagoEraser->erasePago($Pago);

                        $em->persist($Pago);
                    }
                }

                if ($Venta->getCredito() == '1') {
                    $Client = $Venta->getClienteIdcliente();
                    $debtTemp = $Venta->getPagado();
                    if ($Client->getDebt() > 0)
                        $Client->setDebt($Client->getDebt() - $debtTemp);
                    $em->persist($Client);
                }


                $query = $em->createQuery(
                    '   SELECT vc
                    FROM App\Entity\Ventacupon vc
                    inner join vc.cuponIdcupon c
                    inner join vc.ventaIdventa v
                    WHERE v.idventa = :idventa and vc.status =:status
                '
                )->setParameters(["idventa" => $idventa, "status" => '1']);
                $VentaCupones = $query->getResult();

                if ($VentaCupones) {
                    foreach ($VentaCupones as $keycupon => $VentaCupon) {
                        $VentaCupon->setStatus("0");
                        $Cupon = $VentaCupon->getCuponIdcupon();

                        $usedCupons = ($Cupon->getCuponesusados()) ? $Cupon->getCuponesusados() : 0;

                        if ($usedCupons > 0) {
                            $Cupon->setCuponesusados($usedCupons - 1);
                            $em->persist($Cupon);
                        }

                        $em->persist($VentaCupon);
                    }
                }


                $em->flush();
                $exito = true;
            }
        } catch (\Exception $e) {

            //$msj = $e->getMessage() . " linea " . $e->getLine() . " archivo " . $e->getFile();
            $msj = $e->getMessage();
        }

        return $this->json(['exito' => $exito, 'msj' => $msj]);
    }

    /**
     * @Route("/regresar-apartado/{idstockventa}", name="regresar-apartado")
     */
    public function regresarApartado($idstockventa = "")
    {
        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $cantidadproductos = 0;

        try {

            $query = $em->createQuery(
                '   SELECT  sv
                    FROM App\Entity\Stockventa sv
                    WHERE sv.idstockventa = :idstockventa 
                '
            )->setParameters(["idstockventa" => $idstockventa]);
            $Stockventa = $query->getOneOrNullResult();

            if ($Stockventa) {
                // echo "entra 1";

                $Venta = $Stockventa->getVentaIdventa();
                $Stock = $Stockventa->getStockIdstock();
                $escotizacion = $Venta->getCotizacion();
                $estaAparatado = $Stockventa->getEstaapartado();
                $numeroEnStock = $Stock->getCantidad();
                $numeroApartados = $Stockventa->getCantidad();
                if ($escotizacion == "1" && $estaAparatado == "1") {
                    //  echo "entra   2";
                    $totalEnStock = $numeroEnStock + $numeroApartados;
                    $Stock->setCantidad($totalEnStock);
                    $Stockventa->setEstaapartado("0");
                    $Stockventa->setModificacion(new \DateTime("now"));
                    $Stock->setModificacion(new \DateTime("now"));


                    $em->persist($Stockventa);
                    $em->persist($Stock);
                    $em->flush();
                }

                $exito = true;
            }
        } catch (\Exception $e) {

            $msj = $e->getMessage() . " linea " . $e->getLine() . " archivo " . $e->getFile();
        }

        return $this->json(['exito' => $exito, 'msj' => $msj]);
    }

    /**
     * @Route("/obtener-meses", name="obtener-meses")
     */
    public function obtenerMeses(Request $request): Response
    {

        $meses = [];

        $max = 24;
        $min = 3;
        $salto = 3;

        for ($i = $min; $i <= $max; $i += $salto) {
            array_push($meses, $i);
            if ($i >= 6)
                $salto = 6;
        }

        return $this->render('ventas/meses.html.twig', [
            'meses' => $meses,
        ]);
    }

    /**
     * @Route("/subir-documento-venta", name="venta-subir-documento-venta")
     */
    public function subirDocumentoVenta(Request $request): Response
    {

        ini_set('memory_limit', '2024M'); // or you could use 1G
        $responseProcesarDocumento = null;

        $valid = false;
        $msj = "";
        $exito = false;

        //para ver en que carpeta se guardarán
        $documentName = "";

        $carpeta = $this->getParameter('carpetaDocumentosVenta');
        //este id es el que viene de la vista y sirve para mostar el mensaje de error en el documento que sea necesario
        $idVista = "";

        $tipoDocumento = "";
        $html = "";

        try {

            if (isset($_FILES['file']['name'])) {

                $name = $_FILES['file']['name'];
                $tmpName = $_FILES['file']['tmp_name'];
                $error = $_FILES['file']['error'];
                $size = $_FILES['file']['size'];
                $ext = strtolower(pathinfo($name, PATHINFO_EXTENSION));

                $documentName = $name; // Mantenemos el nombre original del archivo

                $targetPath = $carpeta . DIRECTORY_SEPARATOR . $documentName;
                switch ($error) {
                    case UPLOAD_ERR_OK:

                        if (move_uploaded_file($tmpName, $targetPath)) {
                            $exito = true;
                            $valid = true;
                        }

                        break;
                    case UPLOAD_ERR_INI_SIZE:
                        $msj = 'El archivo cargado excede la directiva upload_max_filesize en php.ini.';
                        break;
                    case UPLOAD_ERR_FORM_SIZE:
                        $msj = 'El archivo cargado excede la directiva MAX_FILE_SIZE que se especificó en el formulario HTML.';
                        break;
                    case UPLOAD_ERR_PARTIAL:
                        $msj = 'El archivo cargado solo se cargó parcialmente.';
                        break;
                    case UPLOAD_ERR_NO_FILE:
                        $msj = 'No se cargó ningún archivo.';
                        break;
                    case UPLOAD_ERR_NO_TMP_DIR:
                        $msj = 'Falta una carpeta temporal. Introducido en PHP 4.3.10 y PHP 5.0.3.';
                        break;
                    case UPLOAD_ERR_CANT_WRITE:
                        $msj = 'Error al escribir el archivo en el disco. Introducido en PHP 5.1.0.';
                        break;
                    case UPLOAD_ERR_EXTENSION:
                        $msj = 'Carga de archivo detenida por extensión. Introducido en PHP 5.2.0.';
                        break;
                    default:
                        $msj = 'Error desconocido';
                        break;
                }
            } else {
                $msj = "Debe seleccionar un documento";
            }
        } catch (\Exception $e) {
            $msj = $e->getMessage() . " linea " . $e->getLine() . " archivo " . $e->getFile();
        }

        return $this->json(
            array(
                'msj' => $msj,
                'valid' => $valid,
                'nombreDocumento' => $documentName,
                'tipoDocumento' => $tipoDocumento,
                'exito' => $exito,
                'html' => $html,
            )
        );
    }

    /**
     * @Route("/buscar-stock-debajo-del-costo", name="buscar-stock-debajo-del-costo")
     */
    public function buscarStockDebajoCosto(Request $request)
    {

        $em = $this->getDoctrine()->getManager();


        $query = $em->createQuery(
            'SELECT v.idventa
        FROM App\Entity\Stockventa sv
        INNER JOIN sv.ventaIdventa v
        WHERE sv.status = :status AND v.status = :status AND (sv.preciofinal < sv.costo) order by v.idventa ASC'
        )->setParameters(['status' => '1']);

        $ventas = $query->getResult();

        foreach ($ventas as $idventa => $id) {
            $venta = $this->getDoctrine()->getRepository(Venta::class)->findOneBy(['idventa' => $id]);

            $venta->setVentaproductodebajodelcosto("1");

            $em->persist($venta);
            $em->flush();
        }


        return $this->json(['ventas' => $ventas]);
    }

    /**
     * @Route("/agregar-formulario-beneficiario", name="agregar-formulario-beneficiario")
     */
    public function agregarFormularioBeneficiario(Request $request): Response
    {
        $exito = false;
        $msj = "";
        $idcliente = $request->get("idcliente");
        $isClient = $request->get("isClient");

        $em = $this->getDoctrine()->getManager();

        // Obtener el cliente existente si se proporciona un ID
        $Cliente = null;
        if ($idcliente) {
            $Cliente = $em->getRepository(Cliente::class)->findOneBy(array('idcliente' => $idcliente));

            // Asegurar que el cliente existente tenga empresa asociada para evitar errores en template
            if ($Cliente && !$Cliente->getEmpresaclienteIdempresacliente()) {
                $empresaDefault = $em->getRepository(Empresacliente::class)->findOneBy(array('idempresacliente' => 1));
                if ($empresaDefault) {
                    $Cliente->setEmpresaclienteIdempresacliente($empresaDefault);
                    $em->persist($Cliente);
                    $em->flush();
                }
            }
        }

        $Beneficiario = new Cliente();

        $form = $this->createForm(ClientSaleType::class, $Beneficiario);

        $form->handleRequest($request);

        try {
            if ($form->isSubmitted() && $form->isvalid()) {

                if ($Cliente or $isClient == '1') {

                    $Beneficiario = $form->getData();

                    $dateString = $form->get('fechanacimiento')->getData();

                    $currentDate = new \DateTime();

                    $interval = $currentDate->diff($dateString);

                    $Beneficiario->setEdad($interval->y);

                    if ($isClient != '1')
                        $Beneficiario->setHolder($Cliente);

                    // Asignar empresa por defecto si no tiene una
                    if (!$Beneficiario->getEmpresaclienteIdempresacliente()) {
                        $empresaDefault = $em->getRepository(Empresacliente::class)->findOneBy(array('idempresacliente' => 1));
                        if ($empresaDefault) {
                            $Beneficiario->setEmpresaclienteIdempresacliente($empresaDefault);
                        }
                    }

                    $em->persist($Beneficiario);

                    $em->flush();


                    $exito = true;
                } else
                    throw new \Exception("No hay un cliente");
            } elseif ($form->isSubmitted()) {
                // Si el formulario fue enviado pero no es válido, capturar los errores
                foreach ($form->getErrors(true) as $error) {
                    $msj .= $error->getMessage() . " ";
                }
            }
        } catch (\Exception $e) {

            $msj .= $e->getMessage();
        }

        // Determinar qué cliente pasar al template
        $clienteParaTemplate = $Beneficiario;
        if ($isClient == '1' && isset($Cliente)) {
            $clienteParaTemplate = $Cliente;
        }

        // SOLUCIÓN DEFINITIVA: Asegurar que NUNCA se envíe null al template
        if ($clienteParaTemplate && !$clienteParaTemplate->getEmpresaclienteIdempresacliente()) {
            // Asignar empresa por defecto (ID 1) en lugar de crear objeto vacío
            $empresaDefault = $em->getRepository(Empresacliente::class)->findOneBy(array('idempresacliente' => 1));
            if ($empresaDefault) {
                $clienteParaTemplate->setEmpresaclienteIdempresacliente($empresaDefault);
                // Persistir el cambio para que quede guardado
                $em->persist($clienteParaTemplate);
                $em->flush();
            }
        }

        return $this->render('ventas/formularioBeneficiario.html.twig', [
            'form' => $form->createView(),
            'exito' => $exito,
            'msj' => $msj,
            'isClient' => $isClient,
            'Cliente' => $clienteParaTemplate,
        ]);
    }

    /**
     * @Route("/upload-sale-document", name="ventas-upload-sale-document")
     */
    public function uploadSaleDocument(Request $request)
    {
        $success = false;
        $msg = "";

        $file = $request->files->get('file');

        $userid = $request->get('userid');

        $saleid = $request->get('saleid');

        $saveSale = $request->get('saveSale');

        $em = $this->getDoctrine()->getManager();

        $User = $em->getRepository(Usuario::class)->findOneBy(array('idusuario' => $userid));

        if (!$User)
            $User = $this->getUser();

        $Sale = $em->getRepository(Venta::class)->findOneBy(array('idventa' => $saleid));

        if ($Sale)
            $User = $Sale->getGerente();

        try {

            if (!$file and intval($saveSale) != 1) {
                throw new \Exception('Error al cargar el archivo');
            } else {
                $path = $this->getParameter('carpetaDocumentosVenta');
                $path .= "/" . $User->getIdusuario() . '/';
                if (!file_exists($path))
                    mkdir($path, 0777, true);

                if ($Sale) {

                    $salePath = $path . $Sale->getIdventa() . "/";

                    if (!file_exists($salePath)) {


                        $files = array_filter(scandir($path), function ($file) use ($path) {
                            return is_file($path . $file);
                        });

                        if (!file_exists($salePath))
                            mkdir($salePath, 0777, true);

                        if (intval($saveSale) != 1) {

                            $ext = $file->getClientOriginalExtension();

                            $fileName = $this->sanitize($User->getIdusuario() . time()) . '.' . $ext;

                            $file->move(
                                $salePath,
                                $fileName
                            );

                            $SaleDocument = new Documentoventa();

                            $documentCategory = $request->get('document-categories');

                            $SaleDocument->setUsuarioIdusuario($User);
                            $SaleDocument->setVentaIdventa($Sale);
                            $SaleDocument->setCreacion(new \DateTime("now"));
                            $SaleDocument->setNombredocumento($fileName);
                            $SaleDocument->setTipodocumento($documentCategory);

                            $em->persist($SaleDocument);
                            $em->flush();
                        } else {

                            foreach ($files as $file) {

                                rename($path . $file, $salePath . $file);
                            }
                        }

                        $query = $em->createQuery(
                            'SELECT dv
                            FROM App\Entity\Documentoventa dv
                            inner join dv.usuarioIdusuario u
                            left join dv.ventaIdventa v
                            where u.idusuario =:idusuario AND dv.status =:status AND v.idventa IS NULL
                            '
                        )->setParameters(['idusuario' => $User->getIdusuario(), 'status' => '1']);
                        $saledocuments = $query->getResult();

                        foreach ($saledocuments as $saledocument) {

                            $saledocument->setVentaIdventa($Sale);

                            $em->persist($saledocument);
                        }

                        $em->flush();
                    } else if (intval($saveSale) != 1) {
                        $ext = $file->getClientOriginalExtension();

                        $fileName = $this->sanitize($User->getIdusuario() . time()) . '.' . $ext;

                        $file->move(
                            $salePath,
                            $fileName
                        );

                        $SaleDocument = new Documentoventa();

                        $documentCategory = $request->get('document-categories');

                        $SaleDocument->setUsuarioIdusuario($User);
                        $SaleDocument->setVentaIdventa($Sale);
                        $SaleDocument->setCreacion(new \DateTime("now"));
                        $SaleDocument->setNombredocumento($fileName);
                        $SaleDocument->setTipodocumento($documentCategory);

                        $em->persist($SaleDocument);
                        $em->flush();
                        $msg .= "test 1";
                    }

                    $msg .= "Se agregó con venta";
                    $success = true;
                } else if ($User and $file) {

                    $ext = $file->getClientOriginalExtension();
                    $fileName = $this->sanitize($User->getIdusuario() . time()) . '.' . $ext;

                    $file->move(
                        $path,
                        $fileName
                    );

                    $SaleDocument = new Documentoventa();

                    $documentCategory = $request->get('document-categories');

                    $SaleDocument->setUsuarioIdusuario($User);
                    $SaleDocument->setCreacion(new \DateTime("now"));
                    $SaleDocument->setNombredocumento($fileName);
                    $SaleDocument->setTipodocumento($documentCategory);

                    $em->persist($SaleDocument);
                    $em->flush();

                    $msg .= "Se agregó con cliente";
                    $success = true;
                } else
                    throw new \Exception('Error al subir archivo');
            }
        } catch (\Exception $e) {
            $msg = $e->getMessage();
        }

        return new JsonResponse(["msg" => $msg, "success" => $success, "request" => $request]);
    }

    /**
     * @Route("/sale-document-table", name="ventas-sale-document-table")
     */
    public function saleDocumentTable(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();

        $saleid = $request->get('saleid');

        $userid = $request->get('userid');

        $saledeail = $request->get('saledeail');

        $currenUser = $this->getUser();

        $User = $em->getRepository(Usuario::class)->findOneBy(array('idusuario' => $userid));

        if (!$User)
            $User = $currenUser;

        $Sale = $em->getRepository(Venta::class)->findOneBy(array('idventa' => $saleid));

        $saledocuments = null;

        if ($Sale) {


            $query = $em->createQuery(
                'SELECT dv
                   FROM App\Entity\Documentoventa dv
                   inner join dv.usuarioIdusuario u
                   left join dv.ventaIdventa v
                   where v.idventa =:idventa AND dv.status =:status
                   '
            )->setParameters(['idventa' => $Sale->getIdventa(), 'status' => '1']);
            $saledocuments = $query->getResult();
        } else {

            $query = $em->createQuery(
                'SELECT dv
                   FROM App\Entity\Documentoventa dv
                   inner join dv.usuarioIdusuario u
                   left join dv.ventaIdventa v
                   where u.idusuario =:idusuario AND dv.status =:status AND v.idventa IS NULL
                   '
            )->setParameters(['idusuario' => $User->getIdusuario(), 'status' => '1']);
            $saledocuments = $query->getResult();
        }

        return $this->render('ventas/ventas-sale-document-table.html.twig', [
            'saledocuments' => $saledocuments,
            'userid' => $userid,
            'saledeail' => $saledeail,
            'saleid' => $saleid,
            'role' => $currenUser->getRol(),

        ]);
    }

    /**
     * @Route("/sale-document-form", name="ventas-sale-document-form")
     */
    public function saleDocumentForm(Request $request): Response
    {

        $userid = $request->get('userid');

        return $this->render('ventas/ventas-add-sale-document.html.twig', [
            'userid' => $userid,
        ]);
    }

    /**
     * @Route("/open-sale-document-visor", name="ventas-open-sale-document-visor")
     */
    public function openSaleDocumentVisor(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        $fileDirectory = $this->getParameter('carpetaDocumentosVenta');

        $filename = $request->get('filename');

        $saleid = $request->get('saleid');

        $userid = $request->get('userid');

        $User = $em->getRepository(Usuario::class)->findOneBy(array('idusuario' => $userid));

        $Sale = $em->getRepository(Venta::class)->findOneBy(array('idventa' => $saleid));

        // If we have a sale, use the manager's ID instead of the passed userid
        if ($Sale && $Sale->getGerente()) {
            $User = $Sale->getGerente();
        } else if (!$User) {
            $User = $this->getUser();
        }

        // Construct the file path, ensuring no double slashes
        $physicalPath = rtrim($fileDirectory, '/');
        if ($Sale) {
            $physicalPath .= '/' . $User->getIdusuario() . '/' . $Sale->getIdventa();
        } else {
            $physicalPath .= '/' . $User->getIdusuario();
        }

        // Check if the file exists
        $fileExist = file_exists($physicalPath . '/' . $filename);

        // Create a web-accessible path relative to the public directory
        // The physical path is %kernel.project_dir%/public/uploads/carpetaDocumentosVenta/...
        // The web path should be /uploads/carpetaDocumentosVenta/...
        $webPath = 'uploads/carpetaDocumentosVenta';
        if ($Sale) {
            $webPath .= '/' . $User->getIdusuario() . '/' . $Sale->getIdventa();
        } else {
            $webPath .= '/' . $User->getIdusuario();
        }

        return $this->render('ventas/ventas-open-sale-document-visor.html.twig', [
            'filename' => $filename,
            'fileDirectory' => $webPath,
            'fileExist' => $fileExist,
        ]);
    }

    /**
     * @Route("/delete-sale-document", name="ventas-delete-sale-document")
     */
    public function deleteSaleDocument(Request $request): Response
    {

        $success = false;
        $msg = "";
        $em = $this->getDoctrine()->getManager();

        $saledocumentid = $request->get("saledocumentid");

        try {

            $Saledocument = $em->getRepository(Documentoventa::class)->findOneBy(array('iddocumentoventa' => $saledocumentid));

            if ($Saledocument) {
                if ($Saledocument->getStatus() == 1) {

                    $Saledocument->setStatus(0);

                    $em->persist($Saledocument);

                    $em->flush();

                    $success = true;
                } else
                    throw new \Exception('El documento ya se había eliminado');
            } else
                throw new \Exception('No se encontró el documento');
        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }

        return $this->json(['success' => $success, 'msg' => $msg]);
    }

    private function eraseSaleDocuments($userid)
    {

        $path = $this->getParameter('carpetaDocumentosVenta');
        $path .= "/" . $userid . '/';


        if (file_exists($path)) {
            $files = array_filter(scandir($path), function ($file) use ($path) {
                return is_file($path . $file);
            });
            foreach ($files as $file) {
                unlink($path . "/" . $file);
            }

            $em = $this->getDoctrine()->getManager();

            $query = $em->createQuery(
                'SELECT dv
            FROM App\Entity\Documentoventa dv
            inner join dv.usuarioIdusuario u
            left join dv.ventaIdventa v
            where u.idusuario =:idusuario AND dv.status =:status AND v.idventa IS NULL
            '
            )->setParameters(['idusuario' => $userid, 'status' => '1']);
            $saledocuments = $query->getResult();

            foreach ($saledocuments as $saledocument) {

                $saledocument->setStatus('0');
                $em->persist($saledocument);
            }

            $em->flush();
        }
    }


    /**
     * @Route("/email-stock", name="email-stock")
     */

    public function emailStock($productosVendidos = []): bool
    {
        $em = $this->getDoctrine()->getManager();

        // Base de la consulta
        $dql = 'SELECT sv, ra.cantidadminima AS Cantidadminima, ra.cantidadmaxima AS Cantidadmaxima, p.modelo AS ProductoNombre, st.cantidad AS StockCantidad,
         st.codigobarras AS CodigodeBarras
         FROM App\Entity\Stockventa sv
         INNER JOIN sv.stockIdstock st
         INNER JOIN st.productoIdproducto p
         INNER JOIN App\Entity\Reglaabastecimiento ra WITH ra.productoIdproducto = p.idproducto
         WHERE st.cantidad <= ra.cantidadminima';

        // Si hay productos vendidos, añadimos una condición adicional
        if (!empty($productosVendidos)) {
            $dql .= ' AND p.idproducto IN (:productosVendidos)';
        }

        $query = $em->createQuery($dql);

        // Si hay productos vendidos, establecemos el parámetro en la consulta
        if (!empty($productosVendidos)) {
            $query->setParameter('productosVendidos', $productosVendidos);
        }

        $productosBajoStock = $query->getResult();

        // Construir un mensaje para todos los productos bajo stock
        $messageBody = "";
        foreach ($productosBajoStock as $producto) {
            $messageBody .= 'El producto ' . $producto['ProductoNombre'] . ' tiene un stock actual de ' . $producto['StockCantidad'] . ', que está por debajo del mínimo requerido de ' . $producto['Cantidadminima'] . ".\n";
        }

        // Enviar un solo correo con todos los productos
        if ($messageBody) {
            $email = (new Email())
                ->from(new Address('<EMAIL>', 'sistemas'))
                ->to('<EMAIL>')
                ->subject('Productos con stock bajo')
                ->text($messageBody);
            try {
                $this->mailer->send($email);
                return true;  // Se envió el correo
            } catch (Exception $e) {
                // Handle error here, for example logging the error
                error_log('Error al enviar el correo: ' . $e->getMessage());
            }
        }
        return false;
    }


    /**
     * @Route("/delete-beneficiary", name="ventas-delete-beneficiary")
     */
    public function deleteBeneficiary(Request $request): Response
    {

        $success = false;
        $msg = "";
        $em = $this->getDoctrine()->getManager();

        $beneficiaryid = $request->get("beneficiaryid");

        try {

            $Beneficiary = $em->getRepository(Cliente::class)->findOneBy(array('idcliente' => $beneficiaryid));

            if ($Beneficiary) {

                $Beneficiary->setStatus(0);

                $em->persist($Beneficiary);

                $em->flush();

                $success = true;
            } else
                throw new \Exception('No se encontró la orden');
        } catch (\Exception $e) {
            //$msj.=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();
            $msg .= $e->getMessage();
        }

        return $this->json(['success' => $success, 'msg' => $msg]);
    }

    /**
     * @param Pago $Pagos
     * @return Pago
     */
    public function getPagos(Pago $Pagos): Pago
    {
        return $Pagos;
    }

    /**
     * @Route("/obtener-graduacion/{idstockventa}", name="obtener_graduacion", methods={"GET"})
     */
    public function obtenerGraduacion(int $idstockventa): JsonResponse
    {
        $em = $this->getDoctrine()->getManager();

        try {
            // Obtener el StockVenta
            $stockVenta = $em->getRepository(Stockventa::class)->find($idstockventa);

            if (!$stockVenta) {
                throw new \Exception('StockVenta no encontrado');
            }

            // Verificar que el producto sea un armazón (idclase=3)
            $stock = $stockVenta->getStockIdstock();
            if (!$stock) {
                throw new \Exception('No se encontró el stock asociado al producto');
            }

            $producto = $stock->getProductoIdproducto();
            if (!$producto) {
                throw new \Exception('No se encontró el producto asociado al stock');
            }

            $categoria = $producto->getCategoriaIdcategoria();
            if (!$categoria) {
                throw new \Exception('No se encontró la categoría asociada al producto');
            }

          /*  $clase = $categoria->getClaseIdclase();
            if (!$clase || $clase->getIdclase() != 3) {
                throw new \Exception('La graduación solo puede aplicarse a productos tipo armazón (idclase=3)');
            }*/

            // Inicializar datos de graduación por defecto
            $graduacion = [
                'odEsfera' => '',
                'odCilindro' => '',
                'odEje' => '',
                'odAdicion' => '',
                'oiEsfera' => '',
                'oiCilindro' => '',
                'oiEje' => '',
                'oiAdicion' => '',
                'distanciaPupilar' => '',
                'altura' => '',
                '_aco' => '',
                'diagnostico' => '',
                'notas' => ''
            ];

            // Buscar Stockventaordenlaboratorio relacionado con este StockVenta
            $svol = $em->getRepository(Stockventaordenlaboratorio::class)->findOneBy([
                'stockventaIdstockventa' => $stockVenta
            ]);

            if ($svol && $ol = $svol->getOrdenlaboratorioIdordenlaboratorio()) {
                // Si existe, extraer los datos de graduación
                $graduacion = [
                    'odEsfera' => $ol->getEsferaod(),
                    'odCilindro' => $ol->getCilindrood(),
                    'odEje' => $ol->getEjeod(),
                    'odAdicion' => $ol->getAvcercacaddod(),
                    'oiEsfera' => $ol->getEsferaoi(),
                    'oiCilindro' => $ol->getCilindrooi(),
                    'oiEje' => $ol->getEjeoi(),
                    'oiAdicion' => $ol->getAvcercacaddoi(),
                    'distanciaPupilar' => $ol->getDip(),
                    'altura' => $ol->getAo(),
                    '_aco' => $ol->getAco(),
                    'diagnostico' => $ol->getDiagnosis(),
                    'notas' => $ol->getNotes(),
                    'stage' => $ol->getStage() // Agregar el stage desde la orden de laboratorio
                ];
            }

            return $this->json([
                'exito' => true,
                'graduacion' => $graduacion
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'exito' => false,
                'msj' => 'Error al obtener graduación: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * @Route("/agregar-graduacion", name="agregar_graduacion", methods={"POST"})
     */
    public function agregarGraduacion(Request $request): JsonResponse
    {
        $em = $this->getDoctrine()->getManager();

        $content = $request->getContent();
        $data = json_decode($content, true);

        if (JSON_ERROR_NONE !== json_last_error()) {
            throw new BadRequestHttpException('JSON inválido: ' . json_last_error_msg());
        }


        if (empty($data)) {
            $data = json_decode($request->getContent(), true);
        }


        error_log("=== AGREGAR GRADUACION DEBUG ===");
        error_log("Datos recibidos: " . print_r($data, true));
        error_log("Stage recibido: " . ($data['stage'] ?? 'NO DEFINIDO'));

        try {
            $requiredFields = ['idVenta'];

            foreach ($requiredFields as $field) {
                if (!isset($data[$field])) {
                    throw new \InvalidArgumentException("El campo $field es obligatorio");
                }
            }

            $venta = $em->getRepository(Venta::class)->find($data['idVenta']);
            if (!$venta) {
                throw new \Exception('Venta no encontrada');
            }

/*            // Obtener Stock
            $stock = $em->getRepository(Stock::class)->find($data['idStock']); // Usa idStock
            if (!$stock) {
                throw new \Exception('Stock no encontrado');
            }*/

            $stockVenta = $em->createQuery(
                '
                SELECT sv
                FROM App\Entity\Stockventa sv
                JOIN sv.ventaIdventa v
                WHERE v.idventa = :venta
                AND sv.idstockventa = :stockventa
                AND sv.status = :status
                '
            )
                ->setParameter('venta', $data['idVenta'])
                ->setParameter('stockventa', $data['idProducto'])
                ->setParameter('status', 1)
                ->getOneOrNullResult();

            if (!$stockVenta) {
                $stockVenta = $em->createQuery(
                    '
                SELECT sv
                FROM App\Entity\Stockventa sv
                JOIN sv.ventaIdventa v
                JOIN sv.stockIdstock st
                JOIN st.productoIdproducto p
                WHERE v.idventa = :venta
                AND p.idproducto = :producto
                AND sv.status = :status
                '
                )
                    ->setParameter('venta', $data['idVenta'])
                    ->setParameter('producto', $data['idProducto'])
                    ->setParameter('status', 1)
                    ->getOneOrNullResult();
            }

/*            // Obtener StockVenta
            $stockVenta = $em->getRepository(Stockventa::class)->findOneBy([
                'ventaIdventa' => $venta,
                'stockIdstock' => $stock
            ]);*/

            // Si no se encontró por venta y stock, obtener el último StockVenta creado para esta venta (fallback)
            if (!$stockVenta) {
                $query = $em->createQuery(
                    'SELECT sv FROM App\Entity\Stockventa sv
                    WHERE sv.ventaIdventa = :venta
                    ORDER BY sv.idstockventa DESC'
                )->setParameter('venta', $venta)
                    ->setMaxResults(1);

                $stockVenta = $query->getOneOrNullResult();
            }

            if (!$stockVenta) {
                throw new \Exception('No se encontró ningún StockVenta para esta venta');
            }


            $stock = $em->getRepository(Stock::class)->findOneBy(['idstock' => $stockVenta->getStockIdstock()]);
            if (!$stock) {
                throw new \Exception('Stock no encontrado');
            }

            $producto = $stock->getProductoIdproducto();
            if (!$producto) {
                throw new \Exception('No se encontró el producto asociado al stock');
            }

            $categoria = $producto->getCategoriaIdcategoria();

            if (!$categoria) {
                throw new \Exception('No se encontró la categoría asociada al producto');
            }

            $clase = $categoria->getClaseIdclase();

            // Verificar si el producto cumple con los criterios para tener graduación
            $isValidForGraduation = false;

            // Obtener información del producto
            $tipoproducto = $producto->getTipoproducto();
            $barcode = $stock->getProductoIdproducto()->getCodigobarrasUniversal();

            // Validar según los criterios:
            // 1. Es un armazón (tipoproducto = '1') Y su clase es 3 (armazón)
            // 2. O tiene un código de barras específico ('111389607921')
            if ($tipoproducto === '1' && $clase) {
                if ($clase->getIdclase() === 3) {
                    $isValidForGraduation = true;
                }
            }

            if ($barcode === '111389607921') {
                $isValidForGraduation = true;
            }

            // Si no cumple con los criterios, no permitir la creación de orden de laboratorio
            /*if (!$isValidForGraduation) {
                throw new \Exception('La graduación solo puede aplicarse a productos tipo armazón (clase 3) o con código de barras específico');
            }*/

            $cliente = $venta->getClienteIdcliente();

            if (!$cliente) {
                throw new \Exception('Cliente no asociado a la venta');
            }

            // Verificar si ya existe un Stockventaordenlaboratorio para este StockVenta
            $existingStockvol = $em->getRepository(Stockventaordenlaboratorio::class)
                ->findOneBy(['stockventaIdstockventa' => $stockVenta]);

            $orden = null;
            $svol = null;

            // Si existe una orden previa, no necesitamos validar el tipo de producto nuevamente
            if (!$existingStockvol) {
                // Verificar que el producto sea un armazón (clase con idclase=3)
                $stock = $stockVenta->getStockIdstock();
                if (!$stock) {
                    throw new \Exception('No se encontró el stock asociado al producto');
                }

                $producto = $stock->getProductoIdproducto();
                if (!$producto) {
                    throw new \Exception('No se encontró el producto asociado al stock');
                }

                $categoria = $producto->getCategoriaIdcategoria();

                if (!$categoria) {
                    throw new \Exception('No se encontró la categoría asociada al producto');
                }

                $clase = $categoria->getClaseIdclase();

                // Verificar si el producto cumple con los criterios para tener graduación
                $isValidForGraduation = false;

                // Obtener información del producto
                $tipoproducto = $producto->getTipoproducto();
                $barcode = $stock->getProductoIdproducto()->getCodigobarrasUniversal();

                // Validar según los criterios:
                // 1. Es un armazón (tipoproducto = '1') Y su clase es 3 (armazón)
                // 2. O tiene un código de barras específico ('111389607921')
                if ($tipoproducto === '1' && $clase) {
                    if ($clase->getIdclase() === 3) {
                        $isValidForGraduation = true;
                    }
                }

                if ($barcode === '111389607921') {
                    $isValidForGraduation = true;
                }

                // Si no cumple con los criterios, no permitir la creación de orden de laboratorio
                /*if (!$isValidForGraduation) {
                    throw new \Exception('La graduación solo puede aplicarse a productos tipo armazón (clase 3) o con código de barras específico');
                }*/
            }

            $cliente = $venta->getClienteIdcliente();

            if (!$cliente) {
                throw new \Exception('Cliente no asociado a la venta');
            }

            // Si existe, obtenemos la orden asociada
            if ($existingStockvol) {
                $orden = $existingStockvol->getOrdenlaboratorioIdordenlaboratorio();
                $svol = $existingStockvol;

                // Actualizamos la fecha de actualización
                $orden->setActualizacion(new \DateTime());
            } else {
                // Si no existe, creamos una nueva orden
                $orden = new Ordenlaboratorio();
                $orden->setClienteIdcliente($cliente);
                $orden->setCreacion(new \DateTime());
                $orden->setActualizacion(new \DateTime());
                $orden->setStatus('1');
                $orden->setRefusedclient('0');
            }

            // Actualizamos los campos de graduación
            $orden->setEsferaod($data['odEsfera'] ?? null);
            $orden->setCilindrood($data['odCilindro'] ?? null);
            $orden->setEjeod($data['odEje'] ?? null);
            $orden->setEsferaoi($data['oiEsfera'] ?? null);
            $orden->setCilindrooi($data['oiCilindro'] ?? null);
            $orden->setEjeoi($data['oiEje'] ?? null);
            $orden->setDip($data['distanciaPupilar'] ?? null);
            $orden->setAo($data['altura'] ?? null);
            $orden->setObservaciones($data['observaciones'] ?? null);
            $orden->setDiagnosis($data['diagnosis'] ?? null);
            $orden->setNotes($data['notas'] ?? $data['notasGraduacion'] ?? null);
            $orden->setAco($data['_aco'] ?? null);
            $orden->setAvcercacaddod($data['odAdicion'] ?? null);
            $orden->setAvcercacaddoi($data['oiAdicion'] ?? null);

            // Establecer el stage desde los datos enviados (por defecto 9 = Pendiente)
            $stageValue = $data['stage'] ?? '9';
            error_log("AGREGAR GRADUACION DEBUG - Datos completos: " . json_encode($data));
            error_log("AGREGAR GRADUACION DEBUG - Stage recibido: " . $stageValue);
            error_log("AGREGAR GRADUACION DEBUG - Tipo de stage: " . gettype($stageValue));
            $orden->setStage($stageValue);
            error_log("AGREGAR GRADUACION DEBUG - Stage establecido en orden: " . $orden->getStage());

            // Asignar material por defecto si no existe
            if (!$orden->getMaterialIdmaterial()) {
                $materialDefault = $em->getRepository(Material::class)->find(2);
                if ($materialDefault) {
                    $orden->setMaterialIdmaterial($materialDefault);
                }
            }

            $em->persist($orden);
            $em->flush();

            error_log("Orden guardada con ID: " . $orden->getIdordenlaboratorio());
            error_log("Stage final en BD: " . $orden->getStage());

            // Si no existe el Stockventaordenlaboratorio, creamos uno nuevo
            if (!$svol) {
                $svol = new Stockventaordenlaboratorio();
                $svol->setStockventaIdstockventa($stockVenta);
                $svol->setOrdenlaboratorioIdordenlaboratorio($orden);
                $svol->setCreacion(new \DateTime());
                $svol->setAlreadyset('1');
                $svol->setMainproduct('1');
                $svol->setStockventaordenlaboratorioIdstockventaordenlaboratorio(null);
            }


            $em->persist($svol);
            $em->flush();

            // Verificar que el stage se haya guardado correctamente
            $em->refresh($orden);
            error_log("AGREGAR GRADUACION DEBUG - Stage después del flush: " . $orden->getStage());
            error_log("AGREGAR GRADUACION DEBUG - Orden guardada con ID: " . $orden->getIdordenlaboratorio());

            return $this->json([
                'exito' => true,
                'msj' => 'Graduación guardada',
                'idOrden' => $orden->getIdordenlaboratorio()
            ]);

        } catch (\Exception $e) {
            error_log("Error en agregarGraduacion: " . $e->getMessage());
            return $this->json([
                'exito' => false,
                'msj' => 'Error en agregarGraduacion: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * @Route("/test-stage", name="test_stage", methods={"POST", "GET"})
     */
    public function testStage(Request $request): JsonResponse
    {
        $em = $this->getDoctrine()->getManager();

        if ($request->getMethod() === 'POST') {
            $data = json_decode($request->getContent(), true);
            error_log("=== TEST STAGE ===");
            error_log("Datos recibidos: " . print_r($data, true));

            // Buscar una orden existente
            $orden = $em->getRepository(Ordenlaboratorio::class)
                ->createQueryBuilder('o')
                ->where('o.status = :status')
                ->setParameter('status', '1')
                ->orderBy('o.actualizacion', 'DESC')
                ->setMaxResults(1)
                ->getQuery()
                ->getOneOrNullResult();

            if ($orden) {
                $stageOriginal = $orden->getStage();
                $nuevoStage = $data['stage'] ?? '9';

                error_log("Orden ID: " . $orden->getIdordenlaboratorio());
                error_log("Stage original: " . $stageOriginal);
                error_log("Nuevo stage: " . $nuevoStage);

                $orden->setStage($nuevoStage);
                $orden->setActualizacion(new \DateTime());

                $em->persist($orden);
                $em->flush();

                $em->refresh($orden);
                $stageFinal = $orden->getStage();

                error_log("Stage después del flush: " . $stageFinal);

                return $this->json([
                    'exito' => true,
                    'mensaje' => 'Stage actualizado',
                    'orden_id' => $orden->getIdordenlaboratorio(),
                    'stage_original' => $stageOriginal,
                    'stage_enviado' => $nuevoStage,
                    'stage_guardado' => $stageFinal,
                    'guardado_correctamente' => $stageFinal === $nuevoStage
                ]);
            } else {
                return $this->json([
                    'exito' => false,
                    'mensaje' => 'No se encontró orden para probar'
                ]);
            }
        }

        // GET request - mostrar formulario de prueba
        return $this->json([
            'mensaje' => 'Envía POST con {"stage": "9"} o {"stage": "10"} para probar'
        ]);
    }

    /**
     * @Route("/guardar-graduacion", name="guardar_graduacion", methods={"POST"})
     */
    public function guardarGraduacion(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        try {
            $idProducto = $request->request->get('idProducto');
            $graduacionData = json_decode($request->request->get('graduacion'), true);

            // Buscar el stockventa
            $stockVenta = $em->getRepository('App\Entity\Stockventa')->find($idProducto);

            if (!$stockVenta) {
                throw new \Exception('Producto no encontrado');
            }

            // Obtener o crear graduación
            $graduacion = $stockVenta->getGraduacion();
            if (!$graduacion) {
                $graduacion = new \App\Entity\Graduacion();
                $graduacion->setStockventaIdstockventa($stockVenta);
            }

            // Actualizar datos de graduación
            $graduacion->setOdEsfera($graduacionData['odEsfera'])
                ->setOdCilindro($graduacionData['odCilindro'])
                ->setOdEje($graduacionData['odEje'])
                ->setOdAdicion($graduacionData['odAdicion'])
                ->setOiEsfera($graduacionData['oiEsfera'])
                ->setOiCilindro($graduacionData['oiCilindro'])
                ->setOiEje($graduacionData['oiEje'])
                ->setOiAdicion($graduacionData['oiAdicion'])
                ->setDistanciaPupilar($graduacionData['distanciaPupilar'])
                ->setAltura($graduacionData['altura'])
                ->setAco($graduacionData['_aco'])
                ->setNotas($graduacionData['notasGraduacion'])
                ->setDiagnostico($graduacionData['diagnostico']);

            // Buscar y actualizar la orden de laboratorio relacionada para el stage
            $stockventaOL = $em->getRepository('App\Entity\Stockventaordenlaboratorio')
                ->findOneBy(['stockventaIdstockventa' => $stockVenta]);

            if ($stockventaOL) {
                $ordenLab = $stockventaOL->getOrdenlaboratorioIdordenlaboratorio();
                if ($ordenLab && isset($graduacionData['stage'])) {
                    error_log("GUARDAR GRADUACION DEBUG - Datos completos: " . json_encode($graduacionData));
                    error_log("GUARDAR GRADUACION DEBUG - Stage anterior: " . $ordenLab->getStage());
                    error_log("GUARDAR GRADUACION DEBUG - Stage nuevo: " . $graduacionData['stage']);
                    $ordenLab->setStage($graduacionData['stage']);
                    $ordenLab->setActualizacion(new \DateTime());
                    $em->persist($ordenLab);
                    error_log("GUARDAR GRADUACION DEBUG - Stage establecido en orden: " . $ordenLab->getStage());
                } else {
                    error_log("GUARDAR GRADUACION DEBUG - No se encontró orden de laboratorio o no hay stage");
                    error_log("GUARDAR GRADUACION DEBUG - stockventaOL existe: " . ($stockventaOL ? 'SI' : 'NO'));
                    error_log("GUARDAR GRADUACION DEBUG - ordenLab existe: " . ($ordenLab ? 'SI' : 'NO'));
                    error_log("GUARDAR GRADUACION DEBUG - stage en datos: " . (isset($graduacionData['stage']) ? $graduacionData['stage'] : 'NO'));
                }
            } else {
                error_log("GUARDAR GRADUACION DEBUG - No se encontró stockventaordenlaboratorio para stockventa: " . $stockVenta->getIdstockventa());
            }

            $em->persist($graduacion);
            $em->flush();

            return $this->json([
                'success' => true,
                'message' => 'Graduación guardada correctamente'
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

}
