<?php

namespace App\Controller;

use App\Service\FileUploader;
use App\Form\InvoiceClientefacturadatosType;
use App\Entity\Clientefacturadatos;
use App\Entity\Cliente;
use App\Entity\Producto;
use App\Entity\Venta;
use App\Entity\Ventafactura;
use App\Entity\Empresa;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Facturama\Client as FacturamaClient;
use Facturama\Exception as FacturamaException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mime\Address;
use App\Service\MailService;
use App\Service\FacturamaService;
use phpDocumentor\Reflection\PseudoTypes\False_;
use Symfony\Bridge\Monolog\Formatter\VarDumperFormatter;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Facturama\Exception\ErrorException as FacturamaError;
use Facturama\Exception\RequestException as FacturamaRequestException;
use Facturama\Exception\ModelException as FacturamaModelException;

use GuzzleHttp\RequestOptions;
use Facturama\Client;
use Swift_Attachment;
use Swift_Mailer;
use Swift_Message;
use Swift_SmtpTransport;

use ZipArchive;

//sudo apt-get install php-zip

class InVoiceController extends AbstractController
{
    private $mailer;
    private $mailService;
    private $httpClient;

    private $codsDeServ = [
        '42142901' => 'Anteojos',
        '42142902' => 'Lentes para anteojos',
        '42142903' => 'Monturas para anteojos',
        '42142904' => 'Hardware para anteojos',
        '42142905' => 'Anteojos de sol',
        '42142906' => 'Estuches para anteojos',
        '42142907' => 'Pañitos para limpiar anteojos',
        '42142908' => 'Kits para limpiar anteojos',
        '42142909' => 'Retenedores de anteojos',
        '42142910' => 'Estuches para lentes de contacto',
        '42142911' => 'Insertores o removedores de lentes de contacto',
        '42142912' => 'Medidores de radio de lentes de contacto',
        '42142913' => 'Lentes de contacto',
        '42142914' => 'Solución limpiadora o humidificadora de lentes de contacto'
    ];
    private $params;
    private $facturamaService;

    public function __construct(MailerInterface $mailer, MailService $mailService, HttpClientInterface $httpClient, ParameterBagInterface $params, FacturamaService $facturamaService)
    {
        $this->mailer = $mailer;
        $this->mailService = $mailService;
        $this->httpClient = $httpClient;
        $this->params = $params;
        $this->facturamaService = $facturamaService;
    }

    /** Ya no se ocupa esta función
     * @Route("/facturacion-", name="app_invoice")
     */
    public function index(Request $request, FileUploader $fileUploader): Response
    {

        return $this->render('invoice/index.html.twig', []);
    }

    /**
     * @Route("/facturacion/{e}", name="app_invoice_empresa")
     */
    public function invoiceEmpresa(Request $request, FileUploader $fileUploader, $e): Response
    {
        $em = $this->getDoctrine()->getManager();
        $msj = "";
        $exito = false;
        $logo64 = "";
        $nombre = "";

        try {

            if (!$e) throw new \Exception('Ruta inválida');
            else {
                $Empresa = $em->getRepository(Empresa::class)->findOneBy(array('prefijotickets' => $e));
                if ($Empresa) {
                    $logo64 = $Empresa->getLogo64();
                    $nombre = $Empresa->getNombre();
                } else throw new \Exception('Ruta inválida');
            }
        } catch (\Exception $exception) {
            $msj .= $exception->getMessage();
        }


        return $this->render('invoice/index2.html.twig', [
            'msj' => $msj,
            'exito' => $exito,
            'logo64' => $logo64,
            'nombre' => $nombre,
            'prefijo' => $e,

        ]);
    }

    /**
     * Endpoint in charge of showcase and retrive an especific Empresa form for Facturación.
     *
     * @Route("/facturacion/formulario-empresa/{e}", name="app_invoice_formulario_empresa")
     *
     * @param Request $request The HTTP request object with a Form containing:
     * folio : Identifier for Venta Entity.
     * brochure : .
     * notas : Additional info.
     * @return JsonResponse A JSON response containing information about the success or failure of the operation,
     *
     */
    /**
     * Endpoint in charge of showcase and retrieve an specific Empresa form for Facturación.
     *
     * @Route("/facturacion/formulario-empresa/{e}", name="app_invoice_formulario_empresa")
     */
    public function formularioEmpresa(Request $request, FileUploader $fileUploader, $e = ""): Response
    {

        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();

        $Clientefacturadatos = new Clientefacturadatos();
        $Cliente = new Cliente();
        $Venta = new Venta();
        $filename = null;
        $filenameDirXml = null;
        $filenameDirPdf = null;

        $formularioGuardado = false;
        $tipodePago = '01';

        $totalconIva = 0;

        $form = $this->createForm(InvoiceClientefacturadatosType::class, $Clientefacturadatos);
        $form->handleRequest($request);

        // Log para debugging
        $isSubmitted = $form->isSubmitted();
        $isValid = $isSubmitted ? $form->isValid() : false;
        error_log("[InVoiceController] Método: " . $request->getMethod() .
                 ", AJAX: " . ($request->isXmlHttpRequest() ? 'true' : 'false') .
                 ", Submitted: " . ($isSubmitted ? 'true' : 'false') .
                 ", Valid: " . ($isValid ? 'true' : 'false'));

        try {
            if ($form->isSubmitted() && $form->isValid()) {
                $folio = $form->get('folio')->getData();

                // Capturar los campos que vienen por separado del formulario
                $regimenFiscal = $request->request->get('regimenFiscal');
                $usocfdiMoral = $request->request->get('usocfdiMoral');

                // Validar que los campos requeridos estén presentes
                if (empty($regimenFiscal) || $regimenFiscal === 'Selecciona una opción') {
                    throw new \Exception('Debe seleccionar un régimen fiscal válido.');
                }

                if (empty($usocfdiMoral) || $usocfdiMoral === 'Selecciona una opción') {
                    throw new \Exception('Debe seleccionar un uso de CFDI válido.');
                }

                // Asignar estos valores al objeto Clientefacturadatos
                $Clientefacturadatos = $form->getData();
                $Clientefacturadatos->setRegimenfiscal($regimenFiscal);
                $Clientefacturadatos->setUsocfdi($usocfdiMoral);

                try {
                    // ---------------------------------------------------------------------------------------
                    // Búsqueda de la venta
                    // ---------------------------------------------------------------------------------------
                    $query = $em->createQuery(
                        'SELECT v
                     FROM App\Entity\Venta v
                     INNER JOIN v.sucursalIdsucursal s
                     INNER JOIN s.empresaIdempresa e
                     WHERE v.status = :status 
                       AND v.folio = :folio 
                       AND e.prefijotickets = :e'
                    )->setParameters(['status' => '1', 'folio' => $folio, 'e' => $e]);

                    $Venta = $query->getOneOrNullResult();

                    if (!$Venta) {
                        throw new \Exception('El folio de venta no existe o está inactivo');
                    }

                    // ---------------------------------------------------------------------------------------
                    // Validaciones adicionales
                    // ---------------------------------------------------------------------------------------
                    $Sucursal = $Venta->getSucursalIdsucursal();
                    $Empresa = $Sucursal->getEmpresaIdempresa();
                    $correoFacturacion = $Empresa->getEmailfacturacion();

                    $CheckVenta = $em->getRepository(Ventafactura::class)->findOneBy([
                        'ventaIdventa' => $Venta->getIdventa()
                    ]);
                    if ($CheckVenta) {
                        throw new \Exception('Ya se solicitó la factura para este folio');
                    }

                    // ---------------------------------------------------------------------------------------
                    // Verificar que la venta esté pagada
                    // ---------------------------------------------------------------------------------------
                    $pagos = $em->createQuery(
                        'SELECT p
                     FROM App\Entity\Pago p
                     INNER JOIN p.ventaIdventa v
                     WHERE p.status = :status AND v.idventa = :idventa'
                    )->setParameters([
                        'status' => '1',
                        'idventa' => $Venta->getIdventa()
                    ])->getResult();

                    $totalPagado = 0;
                    $maximopagado = 0;

                    foreach ($pagos as $Pago) {
                        $totalPagado += $Pago->getMonto();
                        if (floatval($Pago->getMonto()) > $maximopagado) {
                            $tipodePago = $Pago->getPaymenttypeIdpaymenttype()->getClavefacturama() ?? "01";
                        }
                    }

                    if ($totalPagado != $Venta->getPagado()) {
                        throw new \Exception('Tu venta debe estar completamente liquidada para poder generar la factura. Verifica que el pago esté registrado antes de continuar.');
                    }
                } catch (\Exception $exBuscarVenta) {
                    // Capturamos los errores al buscar/validar la venta
                    throw new \Exception("[Error al validar la venta / pagos] "
                        . $exBuscarVenta->getMessage()
                        . " | Archivo: " . $exBuscarVenta->getFile()
                        . " (Línea " . $exBuscarVenta->getLine() . ")");
                }

                try {
                    // ---------------------------------------------------------------------------------------
                    // Guardar/actualizar datos de facturación del cliente
                    // ---------------------------------------------------------------------------------------
                    $Cliente = $Venta->getClienteIdcliente();
                    $Clientefacturadatos = $form->getData();

                    $ClientefacturadatosExistente = $em->getRepository(Clientefacturadatos::class)
                        ->findOneBy(['rfc' => $Clientefacturadatos->getRfc(), 'status' => 1]);;

                    $razonSocial = $Clientefacturadatos->getRazonsocial();
                   // $razonSocialWithoutAccents = strtoupper(iconv('UTF-8', 'ASCII//TRANSLIT', $razonSocial));
                    $razonSocialWithoutAccents = $razonSocial;
                    if ($ClientefacturadatosExistente) {
                        $ClientefacturadatosExistente
                            ->setRazonsocial($razonSocialWithoutAccents)
                            ->setEmail($Clientefacturadatos->getEmail())
                            ->setCodigopostal($Clientefacturadatos->getCodigopostal())
                            ->setRegimenfiscal($Clientefacturadatos->getRegimenfiscal())
                            ->setUsocfdi($Clientefacturadatos->getUsocfdi())
                            ->setConstanciasituacionfiscal($Clientefacturadatos->getConstanciasituacionfiscal())
                            ->setTipopersona($Clientefacturadatos->getTipopersona());

                        $Clientefacturadatos = $ClientefacturadatosExistente;
                    }

                    $Clientefacturadatos->setClienteIdcliente($Cliente);
                    $Clientefacturadatos->setCreacion(new \DateTime("now"));
                    $Clientefacturadatos->setModificacion(new \DateTime("now"));

                    $em->persist($Clientefacturadatos);
                    $em->flush();
                } catch (\Exception $exDatosCliente) {
                    throw new \Exception("[Error al guardar datos de facturación] "
                        . $exDatosCliente->getMessage()
                        . " | Archivo: " . $exDatosCliente->getFile()
                        . " (Línea " . $exDatosCliente->getLine() . ")");
                }

                // ---------------------------------------------------------------------------------------
                // Preparar Facturama + items
                // ---------------------------------------------------------------------------------------
                try {
                    $facturama = $this->facturamaService->getClient();

                    // Consulta de productos
                    $query = $em->createQuery(
                        'SELECT prod.idproducto, 
                        sv.preciofinal as precio, 
                        SUM(sv.cantidad) as cantidad,
                        cat.sat as catsat, 
                        class.sat as classsat
                     FROM App\Entity\Stockventa sv
                     INNER JOIN sv.stockIdstock s
                     INNER JOIN sv.ventaIdventa v
                     INNER JOIN s.productoIdproducto prod
                     LEFT JOIN prod.categoriaIdcategoria cat
                     LEFT JOIN cat.claseIdclase class
                     WHERE sv.status = :status
                       AND v.folio = :folio
                       AND sv.preciofinal > 0
                     GROUP BY sv.idstockventa, prod.idproducto'
                    )->setParameters(['status' => '1', 'folio' => $folio]);

                    $productos = $query->getResult();

                    $claves = ['42142901', '42142902', '42142903', '42142907'];
                    $isOftalmico = true;

                    foreach ($productos as $producto) {
                        $satCodigo = ($producto['catsat'] ?? $producto['classsat']);
                        $cantidad = floatval($producto['cantidad']);
                        $precioProducto = $producto['precio'];

                        if (!in_array($satCodigo, $claves)) {
                            $isOftalmico = false;
                        }
                        // Sumamos total con IVA
                        $totalconIva += ($precioProducto * $cantidad);
                    }

                    // Construimos $itemsfactura
                    $itemsfactura = [];
                    if ($isOftalmico) {
                        $totalVenta = $Venta->getPagado();

                        $subtotal = round($totalVenta / 1.16, 2);  // Redondeamos el subtotal
                        $iva = round($subtotal * 0.16, 2);  // Redondeamos el IVA
                        $total = round($subtotal + $iva, 2);  // Redondeamos el total

                        $itemsfactura = [
                            [
                                "Quantity" => 1,
                                "ProductCode" => "42142902",
                                "UnitCode" => "E48",
                                "Unit" => "Pieza",
                                "Description" => "Lentes Graduados oftálmicos",
                                "IdentificationNumber" => "LENTES-GRADUADOS",
                                "UnitPrice" => $subtotal,
                                "Subtotal" => $subtotal,
                                "TaxObject" => "02",
                                "Taxes" => [
                                    [
                                        "Name" => "IVA",
                                        "Rate" => "0.16",
                                        "Total" => $iva,
                                        "Base" => $subtotal,
                                        "IsRetention" => "false",
                                        "IsFederalTax" => "true"
                                    ]
                                ],
                                "Total" => $total
                            ]
                        ];
                    } else {
                        // Escenario B: mezcla de oftálmico + no oftálmico
                        $totalOftalmicoConIVA = 0;
                        $itemsNoOftalmicos = [];

                        foreach ($productos as $producto) {
                            $satCode = ($producto['catsat'] ?? $producto['classsat']);
                            $cantidad = floatval($producto['cantidad']);
                            $pFinal = floatval($producto['precio']); // con IVA

                            // Calcular el subtotal
                            $subtotal = round($pFinal / 1.16, 2) * $cantidad;  // Redondeamos el subtotal
                            $iva = round($subtotal * 0.16, 2);  // Redondeamos el IVA
                            $total = round($subtotal + $iva, 2);  // Redondeamos el total

                            if (in_array($satCode, $claves)) {
                                $totalOftalmicoConIVA += $pFinal * $cantidad;
                            } else {
                                $itemsNoOftalmicos[] = [
                                    "Quantity" => $cantidad,
                                    "ProductCode" => $satCode ?: '42142900',
                                    "UnitCode" => "E48",
                                    "Unit" => "Pieza",
                                    "Description" => "Accesorio",
                                    "IdentificationNumber" => $producto['idproducto'],
                                    "UnitPrice" => round($pFinal / 1.16, 2),
                                    "Subtotal" => $subtotal,
                                    "TaxObject" => "02",
                                    "Taxes" => [
                                        [
                                            "Name" => "IVA",
                                            "Rate" => "0.16",
                                            "Total" => $iva,
                                            "Base" => $subtotal,
                                            "IsRetention" => "false",
                                            "IsFederalTax" => "true"
                                        ]
                                    ],
                                    "Total" => $total
                                ];
                            }
                        }

                        // Aquí agregamos la parte oftálmica
                        if ($totalOftalmicoConIVA > 0) {
                            $subOft = round($totalOftalmicoConIVA / 1.16, 2);
                            $ivaOft = round($subOft * 0.16, 2);
                            $totalOft = round($subOft + $ivaOft, 2);

                            $itemsfactura[] = [
                                "Quantity" => 1,
                                "ProductCode" => "42142902",
                                "UnitCode" => "E48",
                                "Unit" => "Pieza",
                                "Description" => "Lentes Graduados oftálmicos (agrupado)",
                                "UnitPrice" => $subOft,
                                "Subtotal" => $subOft,
                                "TaxObject" => "02",
                                "Taxes" => [
                                    [
                                        "Name" => "IVA",
                                        "Rate" => "0.16",
                                        "Total" => $ivaOft,
                                        "Base" => $subOft,
                                        "IsRetention" => "false",
                                        "IsFederalTax" => "true"
                                    ]
                                ],
                                "Total" => $totalOft
                            ];
                        }

                        // Agregamos los productos no oftálmicos
                        if (!empty($itemsNoOftalmicos)) {
                            foreach ($itemsNoOftalmicos as $item) {
                                $itemsfactura[] = $item;
                            }
                        }
                    }

                    // Aquí formamos $paramsfactura para Facturama
                    $paramsfactura = [
                        "Receiver" => [
                            "Name" => $Clientefacturadatos->getRazonsocial(),
                            "CfdiUse" => $Clientefacturadatos->getUsocfdi(),
                            "Rfc" => $Clientefacturadatos->getRfc(),
                            "FiscalRegime" => $Clientefacturadatos->getRegimenfiscal(),
                            "TaxZipCode" => $Clientefacturadatos->getCodigopostal()
                        ],
                        "CfdiType" => "I",
                        "NameId" => "1",
                        "ExpeditionPlace" => "03230",
                        "Serie" => null,
                        "Folio" => $folio,
                        "PaymentForm" => $tipodePago,
                        "PaymentMethod" => "PUE",
                        "Exportation" => "01",
                        "Items" => $itemsfactura
                    ];
                   // var_dump($paramsfactura);

                    // Timbrar con Facturama
                    $clienteFacturama = $facturama->post('3/cfdis', $paramsfactura);
                    $FACTURA_ID = $clienteFacturama->Id;

                } catch (\Exception $exFacturama) {
                    // Extraer mensaje específico de error de Facturama para el usuario
                    $userFriendlyMessage = $this->extractFacturamaErrorMessage($exFacturama);

                    // Lanzar excepción con mensaje específico para el usuario
                    throw new \Exception($userFriendlyMessage);
                }

                // ---------------------------------------------------------------------------------------
                // Guardar Ventafactura y descargar PDF/XML
                // ---------------------------------------------------------------------------------------
                $Ventafactura = new Ventafactura();
                $Ventafactura->setRfcreceptor($Clientefacturadatos->getRfc());
                $Ventafactura->setRegimenfiscal($Clientefacturadatos->getRegimenfiscal());
                $Ventafactura->setUsucfdi($Clientefacturadatos->getUsocfdi());
                $Ventafactura->setVentaIdventa($Venta);
                $Ventafactura->setClientefacturadatosIdclientefacturadatos($Clientefacturadatos);
                $Ventafactura->setFechatimbrado(new \DateTime("now"));
                $Ventafactura->setFechacomprobante(new \DateTime("now"));
                $Ventafactura->setCreacion(new \DateTime("now"));
                $Ventafactura->setEstado("1");

                $error = false;
                try {
                    // -----------------------------------------------------------------------------------
                    // Descargar PDF y XML
                    // -----------------------------------------------------------------------------------
                    $pdfResponse = $facturama->get('cfdi/pdf/issued/' . $FACTURA_ID);
                    $codedpdf = $pdfResponse->Content;
                    $decodedpdf = base64_decode($codedpdf);

                    $xmlResponse = $facturama->get('cfdi/xml/issued/' . $FACTURA_ID);
                    $codedxml = $xmlResponse->Content;
                    $decodedxml = base64_decode($codedxml);

                    $facturasDir = $this->getParameter('uploads') . DIRECTORY_SEPARATOR . "zipFActuras";
                    $clienteId = $Cliente->getIdcliente();
                    $clienteDir = $facturasDir . DIRECTORY_SEPARATOR . $clienteId;
                    $filename = uniqid('factura_', true);

                    // Ensure the client directory exists
                    if (!file_exists($facturasDir)) {
                        mkdir($facturasDir, 0777, true);
                    }

                    if (!file_exists($clienteDir)) {
                        mkdir($clienteDir, 0777, true);
                    }

                    $filenameDirPdf = $clienteDir . DIRECTORY_SEPARATOR . $filename . ".pdf";
                    $filenameDirXml = $clienteDir . DIRECTORY_SEPARATOR . $filename . ".xml";
                    $filenameZip = $clienteDir . DIRECTORY_SEPARATOR . $filename . ".zip";

                    file_put_contents($filenameDirPdf, $decodedpdf);
                    file_put_contents($filenameDirXml, $decodedxml);

                    // Crear ZIP
                    $zip = new \ZipArchive();
                    if ($zip->open($filenameZip, \ZipArchive::CREATE) === true) {
                        $zip->addFile($filenameDirPdf, basename($filenameDirPdf));
                        $zip->addFile($filenameDirXml, basename($filenameDirXml));
                        $zip->close();

                        // Opcional: borrar los archivos originales
                        unlink($filenameDirPdf);
                        unlink($filenameDirXml);
                    } else {
                        throw new \Exception("No se pudo crear el archivo ZIP para la factura");
                    }
                } catch (\Exception $exDescarga) {
                    // Si falla la descarga/creación de ZIP, marcamos Ventafactura en estado 0
                    $Ventafactura->setEstado("0");
                    $em->persist($Ventafactura);
                    $em->flush();

                    $Empresa = $Sucursal->getEmpresaIdempresa();
                    $correoFacturacion = $Empresa->getEmailfacturacion();
                    $this->sendInvoiceEmail($folio, $Clientefacturadatos, $Sucursal, null, $correoFacturacion);

                    $error = true;

                    throw new \Exception("[Error al descargar PDF/XML o crear ZIP] "
                        . $exDescarga->getMessage()
                        . " | Archivo: " . $exDescarga->getFile()
                        . " (Línea " . $exDescarga->getLine() . ")");
                }

                // ---------------------------------------------------------------------------------------
                // Envío de correo al cliente
                // ---------------------------------------------------------------------------------------
                $notas = $form->get('notas')->getData();
                if (!$error) {
                    try {
                        $clienteEmail = $Clientefacturadatos->getEmail();
                        $response = $this->sendInvoiceEmail($folio, $Clientefacturadatos, $Sucursal, $filenameZip, $clienteEmail);

                        if ($response["exito"] == false) {
                            throw new \Exception($response["msj"]);
                        }
                    } catch (\Exception $exEnvioCliente) {
                        $Ventafactura->setEstado("0");
                        $em->persist($Ventafactura);
                        $em->flush();
                        // Enviamos notificación a correo de facturación
                        $this->sendInvoiceEmail($folio, $Clientefacturadatos, $Sucursal, null, $correoFacturacion);

                        throw new \Exception("[Error al enviar correo al cliente] "
                            . $exEnvioCliente->getMessage()
                            . " | Archivo: " . $exEnvioCliente->getFile()
                            . " (Línea " . $exEnvioCliente->getLine() . ")");
                    }
                }

                // ---------------------------------------------------------------------------------------
                // Guardar en Ventafactura
                // ---------------------------------------------------------------------------------------
                $clienteId = $Cliente->getIdcliente();
                $zipPath = $clienteId . DIRECTORY_SEPARATOR . $filename . ".zip";
                $Ventafactura->setNombrezip($zipPath);
                $em->persist($Ventafactura);
                $em->flush();

                if ($Ventafactura->getIdventafactura()) {
                    $formularioGuardado = true;
                }

                $exito = true;

                // Si es AJAX y exitoso, devolver template de éxito
                if ($request->isXmlHttpRequest() && $exito && $formularioGuardado) {
                    return $this->render('invoice/factura_exitosa.html.twig', [
                        'prefijo' => $e,
                        'folio' => $folio
                    ]);
                }
            } else {
                // El formulario no es válido o no se ha enviado
                if ($form->isSubmitted()) {
                    // Recopilar errores de validación
                    $errors = [];
                    foreach ($form->getErrors(true) as $error) {
                        $errors[] = $error->getMessage();
                    }

                    if (!empty($errors)) {
                        throw new \Exception('Errores de validación: ' . implode(', ', $errors));
                    } else {
                        throw new \Exception('El formulario contiene errores. Por favor, verifica los datos ingresados.');
                    }
                } else {
                    // Formulario no enviado, mostrar formulario vacío
                    // Para peticiones AJAX, esto no debería ocurrir, pero por seguridad asignamos un mensaje
                    $msj = '';
                    $exito = false;
                }
            }
        } catch (\Exception $exception) {
            // Capturar el mensaje de error específico
            $msj = $exception->getMessage();

            // Si el mensaje está vacío, proporcionar un mensaje genérico
            if (empty($msj)) {
                $msj = "Ocurrió un error al procesar la factura. Por favor, verifica los datos e intenta nuevamente.";
            }

            $exito = false;

            // Mantener los valores de los selects dinámicos cuando hay errores
            $regimenFiscal = $request->request->get('regimenFiscal');
            $usocfdiMoral = $request->request->get('usocfdiMoral');

            if ($regimenFiscal) {
                $form->getData()->setRegimenfiscal($regimenFiscal);
            }

            if ($usocfdiMoral) {
                $form->getData()->setUsocfdi($usocfdiMoral);
            }

            // Agregar Flash Message para mostrar en el formulario
            $this->addFlash('error', $msj);

            // Log del error para debugging
            error_log("[InVoiceController Error] " . $exception->getMessage() .
                     " | Archivo: " . $exception->getFile() .
                     " (Línea " . $exception->getLine() . ")");
        }

        // Log final para debugging
        error_log("[InVoiceController Final] Exito: " . ($exito ? 'true' : 'false') .
                 ", Mensaje: '" . $msj . "', FormularioGuardado: " . ($formularioGuardado ? 'true' : 'false') .
                 ", AJAX: " . ($request->isXmlHttpRequest() ? 'true' : 'false'));

        // Determinar qué template usar basado en si es una petición AJAX
        $template =   'invoice/formularioEmpresa.html.twig';
                 /*  'invoice/formulario_fragment.html.twig' :
                   'invoice/formularioEmpresa.html.twig';*/

        // Renderizar la vista
        return $this->render($template, [
            'form' => $form->createView(),
            'cliente' => $Cliente,
            'exito' => $exito,
            'msj' => $msj,
            'formularioGuardado' => $formularioGuardado,
            'prefijo' => $e
        ]);
    }

    /**
     * @Route("/facturacion/validar-folio/{e}", name="app_invoice_validar_folio", methods={"POST"})
     */
    public function validarFolio(Request $request, $e = ""): Response
    {
        $em = $this->getDoctrine()->getManager();
        $folio = $request->request->get('folio');

        $response = [
            'valid' => false,
            'message' => '',
            'details' => []
        ];

        try {
            if (!$folio) {
                throw new \Exception('El folio es requerido');
            }

            if (!$e) {
                throw new \Exception('Empresa no válida');
            }

            // Buscar la venta
            $query = $em->createQuery(
                'SELECT v
                 FROM App\Entity\Venta v
                 INNER JOIN v.sucursalIdsucursal s
                 INNER JOIN s.empresaIdempresa e
                 WHERE v.status = :status
                   AND v.folio = :folio
                   AND e.prefijotickets = :e'
            )->setParameters(['status' => '1', 'folio' => $folio, 'e' => $e]);

            $Venta = $query->getOneOrNullResult();

            if (!$Venta) {
                throw new \Exception('El folio de venta no existe o está inactivo');
            }

            // Verificar si ya se solicitó factura
            $CheckVenta = $em->getRepository(Ventafactura::class)->findOneBy([
                'ventaIdventa' => $Venta->getIdventa()
            ]);
            if ($CheckVenta) {
                throw new \Exception('Ya se solicitó la factura para este folio');
            }

            // Verificar que la venta esté pagada
            $pagos = $em->createQuery(
                'SELECT p
                 FROM App\Entity\Pago p
                 INNER JOIN p.ventaIdventa v
                 WHERE p.status = :status AND v.idventa = :idventa'
            )->setParameters([
                'status' => '1',
                'idventa' => $Venta->getIdventa()
            ])->getResult();

            $totalPagado = 0;
            $maximopagado = 0;

            foreach ($pagos as $Pago) {
                $totalPagado += $Pago->getMonto();
                if (floatval($Pago->getMonto()) > $maximopagado) {
                    $maximopagado = floatval($Pago->getMonto());
                    $tipodePago = $Pago->getPaymenttypeIdpaymenttype()->getClavefacturama() ?? "01";
                }
            }

            if ($totalPagado <= 0) {
                throw new \Exception('Tu venta debe estar completamente liquidada para poder generar la factura. Verifica que el pago esté registrado antes de continuar.');
            }

            if ($totalPagado != $Venta->getPagado()) {
                throw new \Exception('Tu venta debe estar completamente liquidada para poder generar la factura. Verifica que el pago esté registrado antes de continuar.');
            }

            // Si llegamos aquí, todo está bien
            $response['valid'] = true;
            $response['message'] = 'Folio válido y listo para facturar';
            $response['details'] = [
                'folio' => $folio,
                'total' => $Venta->getPagado(),
                'totalPagado' => $totalPagado
            ];

        } catch (\Exception $e) {
            $response['valid'] = false;
            $response['message'] = $e->getMessage();
        }

        return new Response(json_encode($response), 200, ['Content-Type' => 'application/json']);
    }

    /**
     * @Route("/facturacion/seleccionar-tipo-persona/{tipoPersona}", name="app_invoice_plantilla_select")
     */
    public function PlantillaSelect(Request $request, $tipoPersona = "")
    {
        // $tipoPersona=$request->get("tipoPersona");


        if ($tipoPersona == "1" || $tipoPersona == "0") {

            $opcionesUsoCfdi[0] = [
                '-----Selecciona una opción-----' => '-----Selecciona una opción-----',
                'G01' => "Adquisición de mercancias",
                "G03" => "Gastos en general",
                "D01" => "Honorarios médicos, dentales y gastos hospitalarios",
                "D02" => "Gastos médicos por incapacidad o discapacidad"
            ];

            $opcionesUsoCfdi[1] = [
                '-----Selecciona una opción-----' => '-----Selecciona una opción-----',
                'G01' => "Adquisición de mercancias",
                "G02" => "Devoluciones, descuentos o bonificaciones",
                "G03" => "Gastos en general",
                "I01" => "Construcciones",
                "I02" => "Mobiliario y equipo de oficina por inversiones",
                "I03" => "Equipo de transporte",
                "I04" => "Equipo de computo y accesorios",
                "I05" => "Dados, troqueles, moldes, matrices y herramental",
                "I06" => "Comunicaciones telefónicas",
                "I07" => "Comunicaciones satelitales",
                "I08" => "Otra maquinaria y equipo",
                "S01" => "Sin efectos fiscales",
                "CP01" => "Pagos"
            ];


            $regimenFiscal[0] = [
                'Selecciona una opción' => '-----Selecciona una opción-----',
                '605' => "Sueldos y salarios e ingresos asimilados a salarios",
                "606" => "Arrendamiento",
                "607" => "Régimen de enajenación o adquisición",
                "608" => "Demás ingresos",
                "610" => "Residentes en el extranjero sin establecimiento permanente en méxico",
                "611" => "Ingresos por Dividendos (socios y acciones)",
                "612" => "Personas físicas con actividades empresariales y profesionales",
                "614" => "Ingresos por intereses",
                "615" => "Régimen de los ingresos por obtención de premios",
                "616" => "Sin obligaciones fiscales",
                "621" => "Incorporación Fiscal",
                "625" => "Régimen de las Actividades Empresariales con ingresos a través de Plataformas Tecnológicas",
                "626" => "Régimen Simplicado de Confianza"
            ];

            $regimenFiscal[1] = [
                'Selecciona una opción' => '-----Selecciona una opción-----',
                '601' => "General de Ley Personas Morales",
                "603" => "Personas morales con fines no lucrativos",
                "610" => "Residentes en el extranjero sin establecimiento permanente en México",
                "620" => "Sociedades Cooperativas de producción",
                "622" => "Actividades Agrícolas, Ganaderas, Silvícolas y Pesqueras",
                "623" => "Opcional para Grupos de Sociedades",
                "624" => "Coordinados",
                "626" => "Régimen simplificado de Confianza"
            ];
        } else {

            $tipoPersona = 2;

            $opcionesUsoCfdi[2] = [];

            $regimenFiscal[2] = [];
        }

        //$html = $this->render('PlantillaSelect.html.twig')->getContent();

        // return new Response($html);
        return $this->render('invoice/PlantillaSelect.html.twig', [
            'opcionesUsoCfdi' => $opcionesUsoCfdi[$tipoPersona],
            'regimenFiscal' => $regimenFiscal[$tipoPersona]
        ]);
    }

    /**
     * Extrae los mensajes de error específicos de las excepciones de Facturama
     * para mostrar mensajes más claros al usuario
     */
    private function extractFacturamaErrorMessage(\Exception $exception): string
    {
        // Si es una excepción de Facturama con ModelException anidada
        if ($exception instanceof FacturamaRequestException) {
            $previous = $exception->getPrevious();
            if ($previous instanceof FacturamaModelException) {
                // El mensaje de ModelException ya contiene los errores específicos concatenados
                return $previous->getMessage();
            }
            // Si no hay ModelException, usar el mensaje principal de RequestException
            return $exception->getMessage();
        }

        // Para otras excepciones, intentar extraer información de JSON si está presente
        $message = $exception->getMessage();

        // Buscar si el mensaje contiene JSON con estructura de error de Facturama
        if (preg_match('/\{.*"ModelState".*\}/', $message, $matches)) {
            try {
                $jsonData = json_decode($matches[0], true);
                if (isset($jsonData['ModelState']) && is_array($jsonData['ModelState'])) {
                    $errorMessages = [];
                    foreach ($jsonData['ModelState'] as $field => $messages) {
                        if (is_array($messages)) {
                            foreach ($messages as $msg) {
                                $errorMessages[] = $msg;
                            }
                        }
                    }
                    if (!empty($errorMessages)) {
                        return implode('. ', $errorMessages);
                    }
                }
            } catch (\Exception $e) {
                // Si falla el parsing del JSON, continuar con el mensaje original
            }
        }

        // Si no se puede extraer un mensaje específico, devolver el mensaje original
        return $message;
    }

    public function sendInvoiceEmail($folio, $clienteFacturaDatos, $Sucursal, $zipPath, $clienteEmail)
    {
        $exito = true;
        $msj = "";

        try {
            $Empresa = $Sucursal->getEmpresaIdempresa();
            $correoFacturacion = $Empresa->getEmailfacturacion();

            $error = $correoFacturacion == $clienteEmail;

            // Renderizar la vista del cuerpo del email
            $body = $this->renderView(
                'invoice/peticionFactura.html.twig',
                [
                    'logo' => $Empresa->getLogoimagen(),
                    'folio' => $folio,
                    'sucursal' => $Sucursal->getNombre(),
                    'clienteFD' => $clienteFacturaDatos,
                    'error' => $error,
                ]
            );

            // Definir los destinatarios
            $contactsArray = [$correoFacturacion];
            if (!$error) {
                $contactsArray[] = $clienteEmail;
            }


            $emailData = [
                'recipient' => $contactsArray,
                'subject' => "Factura Optimo Ópticas folio: " . $folio,
                'body' => $body,
                'isHtml' => true,
                'attachment' => null,
            ];


            if ($zipPath !== null) {
                $emailData['attachment'] = file_get_contents($zipPath);
                $emailData['extension'] = 'zip';
            }

            // Usar tu servicio de envío de correos
            $result = $this->mailService->sendEmail($emailData);

            if ($result) {
                $exito = true;
            } else {
                throw new \Exception("Error al enviar el correo");
            }

        } catch (\Exception $e) {
            $msj = 'Error al enviar factura por correo: ' . $e->getMessage();
            $exito = false;
        }

        return ['msj' => $msj, 'exito' => $exito];
    }


    private function enviarCorreo($clienteFacturaDatos, $folio, $notas, $Sucursal, $filenameDir, $filename)
    {
        $clienteRfc = $clienteFacturaDatos->getRfc();
        //$archivo = $clienteFacturaDatos->getConstanciasituacionfiscal();
        $clienteEmail = $clienteFacturaDatos->getEmail();
        $Empresa = $Sucursal->getEmpresaIdempresa();
        $msj = '';
        $exito = false;
        $path = $this->getParameter('carpetaConstancias');


        $correoFacturacion = $Empresa->getEmailfacturacion();

        $asunto = 'La factura del cliente con el - rfc: ' . $clienteRfc . ' con el -correo: ' . $clienteEmail;

        $template = $this->renderView(
            'emails/peticionFactura.html.twig',
            [
                'logo' => $Empresa->getLogoimagen(),
                'folio' => $folio,
                'sucursal' => $Sucursal->getNombre(),
                'clienteFD' => $clienteFacturaDatos,
                'notas' => $notas
            ]
        );

        $emailData = [
            'recipient' => $correoFacturacion,
            'subject' => $asunto,
            'body' => $template,
            'attachment' => $filename,
            'isHtml' => true,
            'isFactura' => true
        ];

        foreach ($emailData as $key => $value) {
            if ($value === null && $key !== 'notas') {
                $msj = "Error: El valor para '{$key}' es nulo.";
            }
        }

        try {
            $attachmentPath = $filenameDir;
            $this->mailService->sendEmail($emailData);
            $exito = true;
        } catch (\Exception $e) {

            $msj = 'Error al enviar factura por correo: ';
            $exito = false;
        }
        return ['msj' => $msj, 'exito' => $exito];
    }
}
