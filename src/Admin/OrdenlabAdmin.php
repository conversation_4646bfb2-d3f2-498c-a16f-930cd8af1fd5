<?php

declare(strict_types=1);

namespace App\Admin;

use App\Entity\Ordenlaboratorio;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\Form\Type\DateRangePickerType;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Doctrine\ORM\EntityManagerInterface;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

final class OrdenlabAdmin extends AbstractAdmin
{
    private EntityManagerInterface $entityManager;

    public $orderStages = [
        'Orden creada',
        'Completar flujo',
        'Sin micas',
        'Micas asignadas',
        'Laboratorio Esperando Material',
        'Pendiente',
        'Procesando',
        'Calidad',
        'Terminado',
        'pendiente de graduar',
        'entregado'    ];


    public function setEntityManager(EntityManagerInterface $entityManager): void
    {
        $this->entityManager = $entityManager;
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);

        $rootAlias = current($query->getRootAliases());

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');
        $query->addOrderBy($rootAlias . '.creacion', 'DESC');

        return $query;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('flujoexpedienteIdflujoexpediente.creacion', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de creación del flujo",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('creacion', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de creación de la orden",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('actualizacion', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de actualización de la orden",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.fechaventa', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de venta",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.folio', null, array('label' => 'Folio'))
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.getFormattedSaleQuotation', null, array('label' => 'Estado de venta'))
            ->add('flujoexpedienteIdflujoexpediente.sucursalIdsucursal', ModelFilter::class, ['label' => "Sucursal del flujo", 'field_options' => ['expanded' => false, 'multiple' => true]])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.sucursalIdsucursal', ModelFilter::class, ['label' => "Sucursal de la venta", 'field_options' => ['expanded' => false, 'multiple' => true]])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.tipoventaIdtipoventa.nombre', null, array('label' => 'Tipo de venta'))
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.nombre', null, array('label' => 'Nombre'))
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.apellidopaterno', null, array('label' => 'Apellido paterno'))
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.apellidomaterno', null, array('label' => 'Apellido materno'))
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.beneficiario', null, array('label' => 'Beneficiario'))
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.numeroempleado', null, array('label' => 'Número de empleado'))
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.empresaclienteIdempresacliente', ModelFilter::class, ['label' => "Empresa", 'field_options' => ['expanded' => false, 'multiple' => true]])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.unidadIdunidad.nombre', null, array('label' => 'Unidad'))
            ->add('flujoexpedienteIdflujoexpediente.usuarioIdusuario.nombre', null, array('label' => 'Optometrista'))
            ->add('tipolentecontacto', null, array('label' => 'Tipo de lente de contacto'))
            ->add('disenolenteIddisenolente.nombre', null, array('label' => 'Diseño'))
            ->add('tratamientoIdtratamiento.nombre', null, array('label' => 'Material'))
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => 'Opciones',
                'actions' => [
                    'changeStages' => ['template' => 'CRUD/list__action_changeStages.html.twig'],
                    'delete' => [],
                    'flujoe' => [
                        'template' => 'dashboard_flujo_expediente/list-action-flujo2.html.twig',
                    ],
                ],
            ])
            ->add('stage', 'choice', array(
                'label' => 'Estado',
                'choices' => [
                    '' => '',
                    '0' => '',
                    '1' => 'Orden creada',
                    '2' => 'Completar flujo',
                    '3' => 'Sin micas',
                    '4' => 'Micas asignada',
                    '5' => 'Laboratorio Esperando Material',
                    '6' => 'Pendiente',
                    '7' => 'Procesando',
                    '8' => 'Calidad',
                    '9' => 'Terminado',
                ]
            ))
            ->add('getVenta', null, [
                'label' => 'Folio(s)',
                'mapped' => false,
                'sortable' => false,
                'virtual_field' => true,
                'template' => 'admin/ordenlab/get_folios_order.html.twig',
            ])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.getFormattedSaleQuotation', null, ['label' => 'Estado de venta'])
            ->add('flujoexpedienteIdflujoexpediente.sucursalIdsucursal.nombre', null, ['label' => 'Sucursal del flujo'])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.sucursalIdsucursal.nombre', null, ['label' => 'Sucursal de la venta'])
            ->add('flujoexpedienteIdflujoexpediente.creacion', 'date', array('label' => 'Fecha de creación del flujo', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('creacion', 'date', array('label' => 'Fecha de creación de la orden', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('actualizacion', 'date', array('label' => 'Fecha de actualización de la orden', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.fechaventa', 'date', array('label' => 'Fecha de venta', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.tipoventaIdtipoventa.nombre', null, ['label' => 'Tipo de venta'])
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.nombre', null, ['label' => 'Nombre'])
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.apellidopaterno', null, ['label' => 'Apellido paterno'])
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.apellidomaterno', null, ['label' => 'Apellido materno'])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.beneficiario', null, ['label' => 'Beneficiario'])
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.numeroempleado', null, ['label' => 'Número de empleado'])
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.empresaclienteIdempresacliente.nombre', null, ['label' => 'Empresa'])
            ->add('flujoexpedienteIdflujoexpediente.clienteIdcliente.unidadIdunidad.nombre', null, ['label' => 'Unidad'])
            ->add('flujoexpedienteIdflujoexpediente.usuarioIdusuario.nombre', null, ['label' => 'Optometrista'])
            ->add('tipoorden', 'choice', array(
                'label' => 'Tipo de orden',
                'choices' => [
                    '' => '',
                    '0' => '',
                    '1' => 'Armazón',
                    '2' => 'Lente de contacto',
                ]
            ))
            ->add('esferaod', null, ['label' => 'Esfera OD'])
            ->add('esferaoi', null, ['label' => 'Esfera OI'])
            ->add('cilindrood', null, ['label' => 'Cilindro OD'])
            ->add('cilindrooi', null, ['label' => 'Cilindro OI'])
            ->add('ejeod', null, ['label' => 'Eje OD'])
            ->add('ejeoi', null, ['label' => 'Eje OI'])
            ->add('dip', null, ['label' => 'DIP'])
            ->add('ao', null, ['label' => 'AO'])
            ->add('aco', null, ['label' => 'ACO'])
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.esfodsf', null, array('label'=>'Subjetiva final Esfera OD'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.esfoisf', null, array('label'=>'Subjetiva final Esfera OI'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.cilodsf', null, array('label'=>'Subjetiva final Cilindro OD'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.ciloisf', null, array('label'=>'Subjetiva final Cilindro OI'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.ejeodsf', null, array('label'=>'Subjetiva final Eje OD'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.ejeoisf', null, array('label'=>'Subjetiva final Eje OI'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avlodsf', null, array('label'=>'Subjetiva final AV lejos OD'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avloisf', null, array('label'=>'Subjetiva final AV lejos OI'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avcsaodsf', null, array('label'=>'Subjetiva final AV cerca s/Add OD'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avcsaoisf', null, array('label'=>'Subjetiva final AV cerca s/Add OI'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avccaodsf', null, array('label'=>'Subjetiva final AV cerca c/Add OD'))
            ->add('flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avccaoisf', null, array('label'=>'Subjetiva final AV cerca c/Add OI'))
            ->add('tipolentecontacto', null, ['label' => 'Tipo de lente de contacto'])
            ->add('cb', null, ['label' => 'Curva base'])
            ->add('diam', null, ['label' => 'Diámetro'])
            ->add('armazoncliente', 'choice', array(
                'label' => 'Armazón propio del cliente',
                'choices' => [
                    '' => '',
                    '0' => 'No',
                    '1' => 'Si',
                ]
            ))
            ->add('observaciones', null, ['label' => 'Observaciones'])
            ->add('disenolenteIddisenolente.nombre', null, ['label' => 'Diseño'])
            ->add('tratamientoIdtratamiento.nombre', null, ['label' => 'Material'])
            ->add('suggestions', null, ['label' => 'Sugerencias'])
            ->add('flujoexpedienteIdflujoexpediente.ventaIdventa.products', null, [
                'label' => 'Productos de la venta',
                /*                'template' => 'admin/ordenlab/get_products.html.twig',
                                'mapped' => false,
                                'sortable' => false,
                                'virtual_field' => true,
                                'template' => 'admin/ordenlab/get_products.html.twig',*/
            ])
            ->add('products', null, [
                'label' => 'Productos de la orden',
/*                'mapped' => false,
                'sortable' => false,
                'virtual_field' => true,
                'template' => 'admin/ordenlab/get_products_order.html.twig',*/
            ])
        ;
    }

    protected function configureExportFields(): array
    {
        $exportFields = array(
            'Folio(s)' => 'flujoexpedienteIdflujoexpediente.ventaIdventa.folio',
            'Estado de venta' => 'flujoexpedienteIdflujoexpediente.ventaIdventa.getFormattedSaleQuotation',
            'Sucursal del flujo' => 'flujoexpedienteIdflujoexpediente.sucursalIdsucursal.nombre',
            'Sucursal de la venta' => 'flujoexpedienteIdflujoexpediente.ventaIdventa.sucursalIdsucursal.nombre',
            'Fecha de creación del flujo' => 'flujoexpedienteIdflujoexpediente.creacion',
            'Fecha de creación de la orden' => 'creacion',
            'Fecha de actualización de la orden' => 'actualizacion',
            'Fecha de venta' => 'flujoexpedienteIdflujoexpediente.ventaIdventa.fechaventa',
            'Tipo de venta' => 'flujoexpedienteIdflujoexpediente.ventaIdventa.tipoventaIdtipoventa.nombre',
            'Nombre' => 'flujoexpedienteIdflujoexpediente.clienteIdcliente.nombre',
            'Apellido paterno' => 'flujoexpedienteIdflujoexpediente.clienteIdcliente.apellidopaterno',
            'Apellido materno' => 'flujoexpedienteIdflujoexpediente.clienteIdcliente.apellidomaterno',
            'Beneficiario' => 'flujoexpedienteIdflujoexpediente.ventaIdventa.beneficiario',
            'Número de empleado' => 'flujoexpedienteIdflujoexpediente.clienteIdcliente.numeroempleado',
            'Empresa' => 'flujoexpedienteIdflujoexpediente.clienteIdcliente.empresaclienteIdempresacliente.nombre',
            'Unidad' => 'flujoexpedienteIdflujoexpediente.clienteIdcliente.unidadIdunidad.nombre',
            'Optometrista' => 'flujoexpedienteIdflujoexpediente.usuarioIdusuario.nombre',
            'Tipo de orden' => 'tipoorden',
            'Esfera OD' => 'esferaod',
            'Esfera OI' => 'esferaoi',
            'Cilindro OD' => 'cilindrood',
            'Cilindro OI' => 'cilindrooi',
            'Eje OD' => 'ejeod',
            'Eje OI' => 'ejeoi',
            'DIP' => 'dip',
            'AO' => 'ao',
            'ACO' => 'aco',
            'Subjetiva final Esfera OD' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.esfodsf',
            'Subjetiva final Esfera OI' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.esfoisf',
            'Subjetiva final Cilindro OD' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.cilodsf',
            'Subjetiva final Cilindro OI' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.ciloisf',
            'Subjetiva final Eje OD' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.ejeodsf',
            'Subjetiva final Eje OI' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.ejeoisf',
            'Subjetiva final AV lejos OD' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avlodsf',
            'Subjetiva final AV lejos OI' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avloisf',
            'Subjetiva final AV cerca s/Add OD' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avcsaodsf',
            'Subjetiva final AV cerca s/Add OI' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avcsaoisf',
            'Subjetiva final AV cerca c/Add OD' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avccaodsf',
            'Subjetiva final AV cerca c/Add OI' => 'flujoexpedienteIdflujoexpediente.graduacionIdgraduacion.avccaoisf',
            'Tipo de lente de contacto' => 'tipolentecontacto',
            'Curva base' => 'cb',
            'Diámetro' => 'diam',
            'Armazón propio del cliente' => 'armazoncliente',
            'Observaciones' => 'observaciones',
            'Diseño' => 'disenolenteIddisenolente.nombre',
            'Material' => 'tratamientoIdtratamiento.nombre',
            'Sugerencias' => 'suggestions',
            'Productos de la venta' => 'flujoexpedienteIdflujoexpediente.ventaIdventa.products',
            'Productos de la orden' => 'products',
        );

        return $exportFields;
    }

    public function getExportFormats(): array
    {
        return ['csv', 'xlsx'];
    }

  public function getVenta($idflujoexpediente): array
    {
        $result = $this->entityManager->getRepository(Ordenlaboratorio::class)->getVenta($idflujoexpediente);

        return $result['exito'] ? $result['result'] : [];
    }
/*
    public function getProducts($idflujoexpediente): array
    {
        $result = $this->entityManager->getRepository(Ordenlaboratorio::class)->getProducts($idflujoexpediente);

        return $result['exito'] ? $result['result'] : [];
    }

    public function getProductsOrder($idordenlaboratorio): array
    {
        $result = $this->entityManager->getRepository(Ordenlaboratorio::class)->getProductsOrder($idordenlaboratorio);

        return $result['exito'] ? $result['result'] : [];
    }*/


}
