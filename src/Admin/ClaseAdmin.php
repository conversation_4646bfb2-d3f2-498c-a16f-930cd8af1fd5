<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\DoctrineORMAdminBundle\Filter\ModelAutocompleteType;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface; //El TokenStorageInterface es una interfaz que se utiliza en el framework de Symfony para gestionar el token de autenticación de un usuario.
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;

final class ClaseAdmin extends AbstractAdmin
{

    private $em;
    private $empresasg;
    private $tokenStorage;
    public function __construct(TokenStorageInterface $tokenStorage, EntityManagerInterface $em, ?string $code = null, ?string $class = null, ?string $baseControllerName = null)
{
    parent::__construct($code, $class, $baseControllerName);
    $this->tokenStorage = $tokenStorage;

    $this->em = $em;

}


public function buscarEmpresas(){

    $user=$this->tokenStorage->getToken()->getUser();

    $query = $this-> em->createQuery(
        'SELECT e.idempresa
           FROM App\Entity\Usuarioempresapermiso up
           INNER JOIN up.empresaIdempresa e
           where up.status =:status and up.usuarioIdusuario =:idusuario
           '
    )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
    $empresas= $query->getResult();


    $this->empresasg=$empresas;

}
    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $datagridMapper
            ->add('nombre')
            ->add('sat', null, ['show_filter' => true, 'label' => "SAT"])
            ->add('empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas:',
                'show_filter' => true,
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg);
                    },
                    'expanded' => true,
                    'multiple' => true,
                ],
            ])
        ;
    }


    protected function configureListFields(ListMapper $list): void
    {
        $list
            
            ->add('nombre')
            ->add('sat', null, ['label' => "SAT"])
            ->add('empresaIdempresa.nombre',null,['label' => "Empresa"])
            ->add(ListMapper::NAME_ACTIONS, null, ['label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                ],
            ]);
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with("-", ['class' => 'col-md-12'])
                ->add('nombre')
                ->add('sat', null, ['label' => "SAT"])
                ->add('empresaIdempresa', ModelListType::class,['label' => 'Empresa del usuario'], [ 'placeholder' => 'Seleccione una opción'])
            ->end()
        ;
    }



    public function getExportFormats(): array {
        return ['xlsx'];
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {

        $Usuario=$this->tokenStorage->getToken()->getUser();
        $idusuario=$Usuario->getIdusuario();
        $mm = $this->getModelManager();
        $query = parent::configureQuery($query);
        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();

        $queryBuilder->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where('u.idusuario='.$idusuario);
    
        $rootAlias = current($query->getRootAliases());
    
        // Se une a la empresa a través de la relación de categoría, clase y empresa.
        $query->innerJoin($rootAlias.'.empresaIdempresa', 'e');
    
        // Añade una restricción para que solo se muestren los productos relacionados con la empresa del usuario autenticado.
        $query->where($queryBuilder->expr()->in("e", $queryBuilder->getDQL()));
        
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1'); 


        $query = parent::configureQuery($query);
    
        $rootAlias = current($query->getRootAliases());
    
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');
    
        return $query;
    }

    protected function configureBatchActions($actions): array 
    {
        if (
            $this->hasRoute('edit') && $this->hasAccess('edit') &&
            $this->hasRoute('delete') && $this->hasAccess('delete')
        ) {
            $actions['delete'] = [
                'ask_confirmation' => true
            ];
        }

        return $actions;
    }
}
