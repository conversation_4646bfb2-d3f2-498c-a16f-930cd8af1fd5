# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    locale: 'es_MX'
    uploads: '%kernel.project_dir%/public/uploads'
    uploads_front: '/uploads'
    uploads_carga_masiva_inventario: '%kernel.project_dir%/public/uploads/carga-masiva/inventario'
    uploads_carga_masiva_inventario_front: '/uploads/carga-masiva/inventario'
    uploads_carga_masiva_codigos: '%kernel.project_dir%/public/uploads/carga-masiva/codigos'
    uploads_carga_masiva_resultados: '%kernel.project_dir%/public/uploads/carga-masiva/resultados'
    mailer_user: <EMAIL>
    sender_name: Antonio
    carpetaTickets: 'uploads/tickets'
    carpetaDocumentos: 'uploads/documentos'
    carpetaAnuncios: 'uploads/anuncios'
    carpetaConstancias: 'uploads/constanciasSF'
    carpetaZipFacturas: 'uploads/zipFacturas'
    carpetaArchivoAutorizacion: 'uploads/archivoAutorizacion'
    carpetaDocumentosVenta: '%kernel.project_dir%/public/uploads/carpetaDocumentosVenta'
    carpetaSalidas: 'uploads/salidas'
    carpetaDocumentosFirma: 'uploads/documentosFirma'
    carpetaDocumentosExpediente: 'uploads/documentosExpediente'
    carpetaLogos: 'uploads/logos'
    authZip: 'uploads/authZip'
    profilePicsFolder: 'uploads/profilePics'
    carpetaPublicidades: 'uploads/publicidades'
    productImagesFolder: 'uploads/productImages'
    prescriptionsFolder: 'uploads/prescriptions'
    OPT: '%env(MAILER_DSN)%'
    ORT: '%env(MAILER_DSN_ORTHOPEDIE)%'
    smtpServer: "%env(SMTP_SERVER)%"
    smtpPort: '%env(SMTP_PORT)%'
    smtpEncryption: '%env(SMTP_ENCRYPTION)%'
    smtpUsername: '%env(APP_SECRET)%'
    smtpPassword: '%env(SMTP_PASSWORD)%'
    twilioSID: '%env(TWILIO_ACCOUNT_SID)%'
    twilioTOKEN: '%env(TWILIO_AUTH_TOKEN)%'
    twilioPHONE: '%env(TWILIO_PHONE_NUMBER)%'
    twilioWHATSAPP: '%env(TWILIO_WHATSAPP_NUMBER)%'
    twilioMessagingServiceSid: '%env(TWILIO_MESSAGING_SERVICE_SID)%'
    sendGridApiKey: '%env(SENDGRID_API_KEY)%'
    APP_ENV: '%env(APP_ENV)%'
    twilioVerifyServiceSid: '%env(TWILIO_VERIFY_SERVICE_SID)%'
    twilioVerifyServiceName: 'PV360 Phone Verification'
    twilioVerifyCodeLength: 6
    google_client_api: '%env(GOOGLE_CLIENT_ID)%'
    google_client_secret: '%env(GOOGLE_CLIENT_SECRET)%'
    google_OAUTH_SCOPE: '%env(GOOGLE_OAUTH_SCOPE)%'
    google_OAUTH2_TOKEN_URI: '%env(GOOGLE_OAUTH2_TOKEN_URI)%'
    google_CALENDAR_REDIRECT_URI: ''
    dev_email: '<EMAIL>'
    OPTIHUBV3: '%env(OPTIHUBV3)%'
    PV360: '%env(PV360)%'
    app.path.enterprise_logos: '%kernel.project_dir%/public/uploads/logos'
    # Facturama configuration
    facturama_username: '%env(FACTURAMA_USERNAME)%'
    facturama_password: '%env(FACTURAMA_PASSWORD)%'
    facturama_is_development: '%env(bool:FACTURAMA_IS_DEVELOPMENT)%'

services:

    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'
            - '../src/Tests/'

    App\Admin\VentafacturaAdmin:
        arguments:
            $twig: '@twig.environment'
            $mailService: '@App\Service\MailService'

    App\Security\TwoFactorSuccessHandler:
        arguments:
            $router: '@router'
            $authorizationChecker: '@security.authorization_checker'
            $redirectService: '@App\Service\RedirectService'
        tags:
            - { name: 'monolog.logger', channel: 'security' }

    App\Service\RedirectService:
        arguments:
            $router: '@router'
            $authorizationChecker: '@security.authorization_checker'




    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: '../src/Controller/'
        tags: ['controller.service_arguments']


    #app.doctrine:
    #    class: App\Entity\Venta
    #    arguments:
    #        $em: '@doctrine.orm.entity_manager'

    App\Service\FileUploader:
        arguments: ["@service_container"]

    App\Service\PagoEraser:
        arguments:
            $em: '@doctrine.orm.entity_manager'
    App\Service\SalesService:
        arguments:
            $em: '@doctrine.orm.entity_manager'

    App\Service\MailService:
        arguments:
            $params: '@parameter_bag'

    App\Service\ExamenVisualService:
        arguments:
            $mailer: '@mailer'
            $twig: '@twig'
            $entityManager: '@doctrine.orm.entity_manager'

    App\Service\TwilioService:
        arguments:
            $twilioSID: '%twilioSID%'
            $twilioTOKEN: '%twilioTOKEN%'
            $twilioPHONE: '%twilioPHONE%'
            $appEnv: '%APP_ENV%'
            $sendGridApiKey: '%sendGridApiKey%'
            $twilioWHATSAPP: '%twilioWHATSAPP%'
            $twilioMessagingServiceSid: '%twilioMessagingServiceSid%'

    App\Service\TwilioVerifyService:
        arguments:
            $accountSid: '%twilioSID%'
            $authToken: '%twilioTOKEN%'
            $verifyServiceSid: '%twilioVerifyServiceSid%'
            $serviceName: '%twilioVerifyServiceName%'
            $codeLength: '%twilioVerifyCodeLength%'
            $defaultTemplateSid: ~
            $logger: '@monolog.logger'

    App\EventListener\SaleListener:
        tags:
            -
                # these are the options required to define the entity listener
                name: 'doctrine.orm.entity_listener'
                event: 'postLoad'
                entity: 'App\Entity\Venta'
                saleService: 'App\Service\SalesService'

    App\EventListener\ProductoTranspasoAlmacenListener:
        tags:
            - { name: 'doctrine.event_listener', event: 'postLoad' }

    App\EventListener\RouteListener:
        arguments:
            $tokenStorage: '@security.token_storage'
            $urlGenerator: '@router'
        tags:
            - { name: kernel.event_listener, event: kernel.request, method: onKernelRequest }

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    admin.categoria:
        class: App\Admin\CategoriaAdmin
        arguments:
            - ~
            - App\Entity\Categoria
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, manager_type: orm, label: Subcategorías }
    admin.sucursal:
        class: App\Admin\SucursalAdmin
        arguments:
            - ~
            - App\Entity\Sucursal
            - App\Controller\AdminController
            - '@security.token_storage'
        tags:
            - { name: sonata.admin, manager_type: orm, label: Sucursales }
    admin.unidad:
        class: App\Admin\UnidadAdmin
        arguments:
            - ~
            - App\Entity\Unidad
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, manager_type: orm, label: Unidades }
    admin.producto:
        class: App\Admin\ProductoAdmin

        arguments:
            - ~
            - App\Entity\Producto
            #            - App\Controller\ProductoAdminController
            - App\Controller\AdminController
            - '@security.token_storage'
        tags:
            - { name: sonata.admin, manager_type: orm, label: Productos,role: ROLE_SUPER_ADMIN }
    admin.tratamiento:
        class: App\Admin\TratamientoAdmin
        arguments:
            - ~
            - App\Entity\Tratamiento
            #            - App\Controller\ProductoAdminController
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, manager_type: orm, label: Tratamientos }
    admin.marca:
        class: App\Admin\MarcaAdmin
        arguments:
            - ~
            - App\Entity\Marca
            #            - App\Controller\ProductoAdminController
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, manager_type: orm, label: Marcas }
    admin.membresia:
        class: App\Admin\MembresiaAdmin
        arguments:
            - ~
            - App\Entity\Membresia
            #            - App\Controller\ProductoAdminController
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, manager_type: orm, label: Membresia }
    admin.membresiacliente:
        class: App\Admin\MembresiaclienteAdmin
        arguments:
            - ~
            - App\Entity\Membresiacliente
            #            - App\Controller\ProductoAdminController
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, manager_type: orm, label: Membresia del cliente }
    admin.cliente:
        class: App\Admin\ClienteAdmin
        arguments:
            - ~
            - App\Entity\Cliente
            #            - App\Controller\ProductoAdminController
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, manager_type: orm, label: Clientes }
    admin.beneficiario:
        class: App\Admin\BeneficiarioAdmin
        arguments:
            - ~
            - App\Entity\Beneficiario
            #            - App\Controller\ProductoAdminController
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, manager_type: orm, label: Beneficiarios }

    admin.venta:
        class: App\Admin\VentaAdmin
        arguments:
            - ~
            - App\Entity\Venta
            - App\Controller\AdminController
            - '@security.token_storage'
            - 'venta'
        tags:
            - { name: sonata.admin, manager_type: orm, label: Ventas, default: true }
    admin.ventaalmacen:
        class: App\Admin\VentaAdmin
        arguments:
            - ~
            - App\Entity\Venta
            - App\Controller\AdminController
            - '@security.token_storage'
            - 'ventaAlmacen'
        tags:
            - { name: sonata.admin, manager_type: orm, label: Ventas }

    admin.usuario:
        class: App\Admin\UsuarioAdmin
        arguments:
            - ~
            - App\Entity\Usuario
            #            - App\Controller\ProductoAdminController
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, manager_type: orm, label: Usuarios }

    admin.proveedor:
        class: App\Admin\ProveedorAdmin
        arguments: [~, App\Entity\Proveedor, ~]
        tags:
            - { name: sonata.admin, manager_type: orm, group: admin, label: Proveedores }
        public: true

    admin.stock:
        class: App\Admin\StockAdmin
        arguments:
            - ~
            - App\Entity\Stock
            - App\Controller\AdminController
            - '@security.token_storage'
            - 'stock'
        tags:
            - { name: sonata.admin, manager_type: orm, group: admin, label: Stock, default: true }
        public: true

    admin.stockalmacen:
        class: App\Admin\StockAdmin
        arguments:
            - ~
            - App\Entity\Stock
            - App\Controller\AdminController
            - '@security.token_storage'
            - 'stockAlmacen'
        tags:
            - { name: sonata.admin, manager_type: orm, group: admin, label: Stock }
        public: true

    admin.tipobisel:
        class: App\Admin\TipobiselAdmin
        arguments: [ ~, App\Entity\Tipobisel, ~ ]
        tags:
            - { name: sonata.admin, manager_type: orm, group: admin, label: Tipos de bisel }
        public: true

    admin.disenolentes:
        class: App\Admin\DisenolenteAdmin
        arguments: [ ~, App\Entity\Disenolente, ~ ]
        tags:
            - { name: sonata.admin, manager_type: orm, group: admin, label: Diseño de lentes }
        public: true

    admin.material:
        class: App\Admin\MaterialAdmin
        arguments: [ ~, App\Entity\Material, ~ ]
        tags:
            - { name: sonata.admin, manager_type: orm, group: admin, label: Materiales }
        public: true

    #admin.pago:
    #    class: App\Admin\PagoAdmin
    #    arguments: [ '@App\Service\PagoEraser', ~, App\Entity\Pago, App\Controller\AdminController, '@security.token_storage' ]
    #    tags:
    #        - { name: sonata.admin, manager_type: orm, group: admin, label: Pagos, model_class: App\Entity\Pago }
    #    public: true

    admin.pago:
        class: App\Admin\PagoAdmin
        arguments:
            - ~
            - App\Entity\Pago
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, manager_type: orm, group: admin, label: Pagos }
        public: true



    admin.laboratorio:
        class: App\Admin\LaboratorioAdmin
        arguments: [ ~, App\Entity\Laboratorio, ~ ]
        tags:
            - { name: sonata.admin, manager_type: orm, group: admin, label: Laboratorio }
        public: true

    admin.traspasoalmacen:
        class: App\Admin\TraspasoalmacenAdmin
        arguments:
            - ~
            - App\Entity\Transpasoalmacen
            #- App\Controller\ProductoAdminController
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, manager_type: orm, group: admin, label: Registro de traspasos }
        public: true

    admin.ordensalida:
        class: App\Admin\TraspasosrecibirAdmin
        arguments:
            - ~
            - App\Entity\Ordensalida
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, manager_type: orm, group: admin, label: Traspasos por recibibir }


    admin.productotraspasoalmacen:
        class: App\Admin\ProductotranspasoalmacenAdmin
        arguments: [ ~, App\Entity\Productostranspasoalmacen, ~ ]
        tags:
            - { name: sonata.admin, manager_type: orm, group: admin, label: Historial de traspasos por producto}
        public: true


    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    admin.cupon:
        class: App\Admin\CuponAdmin
        arguments: [~, App\Entity\Cupon, App\Controller\CuponAdminController]
        tags:
            - { name: sonata.admin, manager_type: orm, group: admin, label: Cupones }
        public: true

    admin.cuponmarca:
        class: App\Admin\CuponmarcaAdmin
        arguments: [~, App\Entity\Cuponmarca, ~]
        tags:
            - { name: sonata.admin, manager_type: orm, group: admin, label: Cupones en marcas }
        public: true

    admin.ventacupon:
        class: App\Admin\VentacuponAdmin
        arguments: [~, 'App\Entity\Ventacupon', ~, '@security.token_storage']
        tags:
            - { name: sonata.admin, model_class: App\Entity\Ventacupon, controller: ~, manager_type: orm, group: admin, label: Ventas con cupón }

    admin.tipoventa:
        class: App\Admin\TipoventaAdmin
        arguments: [~, 'App\Entity\Tipoventa', 'App\Controller\AdminController', '@security.token_storage', '@doctrine.orm.entity_manager']
        tags:
            - { name: sonata.admin, model_class: App\Entity\Tipoventa, controller: App\Controller\AdminController, manager_type: orm, group: admin, label: Tipos de venta }

    admin.ventafactura:
        class: App\Admin\VentafacturaAdmin
        arguments:
            - ~
            - App\Entity\Ventafactura
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, model_class: App\Entity\Ventafactura, controller: App\Controller\AdminController, manager_type: orm, group: admin, label: Solicitud de factura }

    admin.clientefacturadatos:
        class: App\Admin\ClientefacturadatosAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Clientefacturadatos, controller: ~, manager_type: orm, group: admin, label: Datos de facturación }

    admin.clase:
        class: App\Admin\ClaseAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Clase, controller: ~, manager_type: orm, group: admin, label: Categorías }

    admin.laboratorioorden:
        class: App\Admin\LaboratorioordenAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Laboratorioorden, controller: ~, manager_type: orm, group: admin, label: Laboratorioorden }

    admin.stockventa:
        class: App\Admin\StockventaAdmin
        arguments:
            - ~
            - App\Entity\Stockventa
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, model_class: App\Entity\Stockventa, controller: App\Controller\AdminController, manager_type: orm, group: admin, label: Productos vendidos }
        public: true

    admin.cargainventariolog:
        class: App\Admin\CargainventariologAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Cargainventariolog, controller: ~, manager_type: orm, group: admin, label: Historial de Carga de Inventario}

    app.menu_listener:
        class: App\EventListener\MenuBuilderListener
        tags:
            - { name: kernel.event_listener, event: sonata.admin.event.configure.menu.sidebar, method: addMenuItems }
        arguments: ['@router','@doctrine.orm.entity_manager']

    admin.empresa:
        class: App\Admin\EmpresaAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Empresa, controller: ~, manager_type: orm, group: admin, label: Empresa del usuario }

    admin.empresacliente:
        class: App\Admin\EmpresaclienteAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Empresacliente, controller: ~, manager_type: orm, group: admin, label: Empresa del cliente }

    admin.documentos:
        class: App\Admin\DocumentosAdmin
        arguments:
            - '@security.token_storage'
        tags:
            - { name: sonata.admin, model_class: App\Entity\Documentos, controller: App\Controller\AdminController, manager_type: orm, group: admin, label: Documentos }

    admin.categoriadocumentos:
        class: App\Admin\CategoriadocumentosAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Categoriadocumentos, controller: ~, manager_type: orm, group: admin, label: Categorías de documentos }

    admin.categoriaanuncio:
        class: App\Admin\CategoriaanuncioAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Categoriaanuncio, controller: ~, manager_type: orm, group: admin, label: Categoría de anuncios }

    admin.anuncios:
        class: App\Admin\AnunciosAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Anuncios, controller: ~, manager_type: orm, group: admin, label: Anuncios }

    admin.ordenlaboratorio:
        class: App\Admin\OrdenlaboratorioAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Ordenlaboratorio, controller: ~, manager_type: orm, group: admin, label: Orden de laboratorio }

    admin.flujoexpediente:
        class: App\Admin\FlujoexpedienteAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Flujoexpediente, controller: App\Controller\AdminController, manager_type: orm, group: admin, label: Flujos }


    admin.usuarioempresapermiso:
        class: App\Admin\UsuarioempresapermisoAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Usuarioempresapermiso, controller: ~, manager_type: orm, group: admin, label: Permisos del usuario }

    admin.medida:
        class: App\Admin\MedidaAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Medida, controller: ~, manager_type: orm, group: admin, label: Medida }

    admin.unidad_medida:
        class: App\Admin\UnidadMedidaAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Unidadmedida, controller: ~, manager_type: orm, group: admin, label: Unidad de medida }

    admin.proveedorcontacto:
        class: App\Admin\ProveedorcontactoAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Proveedorcontacto, controller: ~, manager_type: orm, group: admin, label: Contactos de proveedores }

    admin.proveedoremail:
        class: App\Admin\ProveedoremailAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Proveedoremail, controller: ~, manager_type: orm, group: admin, label: Correos de proveedores }

    admin.proveedortelefono:
        class: App\Admin\ProveedortelefonoAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Proveedortelefono, controller: ~, manager_type: orm, group: admin, label: Teléfonos de proveedores }

    admin.reglaabastecimiento:
        class: App\Admin\ReglaabastecimientoAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Reglaabastecimiento, controller: ~, manager_type: orm, group: admin, label: Regla de abastecimiento }

    admin.stockventaordenlaboratorio:
        class: App\Admin\StockventaordenlaboratorioAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Stockventaordenlaboratorio, controller: ~, manager_type: orm, group: admin, label: Ordenes de Laboratorio }

    admin.mkttemplates:
        class: App\Admin\MkttemplatesAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Mkttemplates, controller: ~, manager_type: orm, group: admin, label: Mkttemplates }
    admin.sellreference:
        class: App\Admin\SellreferenceAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Sellreference, controller: ~, manager_type: orm, group: admin, label: Referencias de ventas }

    admin.framecolor:
        class: App\Admin\FramecolorAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Framecolor, controller: ~, manager_type: orm, group: admin, label: Framecolor }

    admin.framematerial:
        class: App\Admin\FramematerialAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Framematerial, controller: ~, manager_type: orm, group: admin, label: Framematerial }

    admin.paymenttype:
        class: App\Admin\PaymenttypeAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Paymenttype, controller: ~, manager_type: orm, group: admin, label: Paymenttype }

    admin.ventagroup:
        class: App\Admin\VentagroupAdmin
        arguments:
            - ~
            - App\Entity\Ventagroup
            - App\Controller\AdminController
        tags:
            - { name: sonata.admin, model_class: App\Entity\Ventagroup, controller: App\Controller\AdminController, manager_type: orm, group: admin, label: Facturas UAM }

    admin.graduacion:
        class: App\Admin\GraduacionAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Graduacion, controller: ~, manager_type: orm, group: admin, label: Examenes visuales }

    admin.customflow:
        class: App\Admin\CustomflowAdmin
        tags:
            - { name: sonata.admin, model_class: App\Entity\Customflow, controller: App\Controller\CustomflowAdminController, manager_type: orm, group: admin, label: Flujos  }

    admin.ordenlab:
        class: App\Admin\OrdenlabAdmin
        calls:
            - [ setEntityManager, ['@doctrine.orm.entity_manager'] ]
        tags:
            - { name: sonata.admin, model_class: App\Entity\Ordenlaboratorio, controller: ~, manager_type: orm, group: admin, label: Orden de laboratorio }

    app.login_listener:
        class: App\EventListener\AuthenticationSuccessHandler
        tags:
            - { name: kernel.event_listener, event: lexik_jwt_authentication.on_authentication_success, method: onAuthenticationSuccess }

    App\Service\FacturamaService:
        arguments:
            $username: '%facturama_username%'
            $password: '%facturama_password%'
            $isDevelopment: '%facturama_is_development%'
