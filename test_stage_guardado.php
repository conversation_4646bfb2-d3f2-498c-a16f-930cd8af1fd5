<?php
/**
 * Script para verificar que el stage se está guardando correctamente
 * en las órdenes de laboratorio
 */

// Configuración de la base de datos (ajusta según tu configuración)
$host = 'localhost';
$dbname = 'pv360'; // Ajusta el nombre de tu base de datos
$username = 'root'; // Ajusta tu usuario
$password = ''; // Ajusta tu contraseña

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== VERIFICACIÓN: STAGE SE GUARDA CORRECTAMENTE ===\n\n";
    
    // 1. Verificar órdenes de laboratorio recientes con stage
    echo "1. ÓRDENES DE LABORATORIO RECIENTES (últimas 10):\n";
    $stmt = $pdo->prepare("
        SELECT 
            ol.idordenLaboratorio,
            ol.stage,
            ol.creacion,
            ol.actualizacion,
            ol.status,
            c.nombre as cliente_nombre,
            CASE 
                WHEN ol.stage = '1' THEN 'Inicial'
                WHEN ol.stage = '9' THEN 'Pendiente de graduar'
                WHEN ol.stage = '10' THEN 'Terminado'
                ELSE CONCAT('Stage ', ol.stage)
            END as stage_descripcion
        FROM ordenLaboratorio ol
        LEFT JOIN cliente c ON ol.cliente_idcliente = c.idcliente
        WHERE ol.status = '1'
        ORDER BY ol.actualizacion DESC
        LIMIT 10
    ");
    $stmt->execute();
    $ordenes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($ordenes) > 0) {
        foreach ($ordenes as $orden) {
            $stageColor = $orden['stage'] == '9' ? '🟡' : ($orden['stage'] == '10' ? '🟢' : '⚪');
            echo "  {$stageColor} ID: {$orden['idordenLaboratorio']} | Stage: {$orden['stage']} ({$orden['stage_descripcion']})\n";
            echo "    Cliente: {$orden['cliente_nombre']}\n";
            echo "    Actualización: {$orden['actualizacion']}\n\n";
        }
    } else {
        echo "  ❌ No se encontraron órdenes de laboratorio\n\n";
    }
    
    // 2. Estadísticas de stages
    echo "2. ESTADÍSTICAS DE STAGES:\n";
    $stmt = $pdo->prepare("
        SELECT 
            stage,
            COUNT(*) as cantidad,
            CASE 
                WHEN stage = '1' THEN 'Inicial'
                WHEN stage = '9' THEN 'Pendiente de graduar'
                WHEN stage = '10' THEN 'Terminado'
                ELSE CONCAT('Stage ', stage)
            END as descripcion
        FROM ordenLaboratorio 
        WHERE status = '1'
        GROUP BY stage
        ORDER BY 
            CASE 
                WHEN stage = '1' THEN 1
                WHEN stage = '9' THEN 2
                WHEN stage = '10' THEN 3
                ELSE 4
            END
    ");
    $stmt->execute();
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $total = 0;
    foreach ($stats as $stat) {
        $total += $stat['cantidad'];
        $emoji = $stat['stage'] == '9' ? '🟡' : ($stat['stage'] == '10' ? '🟢' : '⚪');
        echo "  {$emoji} Stage {$stat['stage']} ({$stat['descripcion']}): {$stat['cantidad']} órdenes\n";
    }
    echo "  📊 Total: {$total} órdenes activas\n\n";
    
    // 3. Verificar órdenes creadas hoy
    echo "3. ÓRDENES CREADAS HOY:\n";
    $stmt = $pdo->prepare("
        SELECT 
            ol.idordenLaboratorio,
            ol.stage,
            ol.creacion,
            c.nombre as cliente_nombre,
            v.folio as venta_folio,
            CASE 
                WHEN ol.stage = '9' THEN 'Pendiente de graduar'
                WHEN ol.stage = '10' THEN 'Terminado'
                ELSE CONCAT('Stage ', ol.stage)
            END as stage_descripcion
        FROM ordenLaboratorio ol
        LEFT JOIN cliente c ON ol.cliente_idcliente = c.idcliente
        LEFT JOIN stockVentaOrdenLaboratorio svol ON ol.idordenLaboratorio = svol.ordenLaboratorio_idordenLaboratorio
        LEFT JOIN stockVenta sv ON svol.stockVenta_idstockVenta = sv.idstockventa
        LEFT JOIN venta v ON sv.venta_idventa = v.idventa
        WHERE ol.status = '1' 
        AND DATE(ol.creacion) = CURDATE()
        ORDER BY ol.creacion DESC
    ");
    $stmt->execute();
    $ordenesHoy = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($ordenesHoy) > 0) {
        foreach ($ordenesHoy as $orden) {
            $stageColor = $orden['stage'] == '9' ? '🟡' : ($orden['stage'] == '10' ? '🟢' : '⚪');
            echo "  {$stageColor} Orden {$orden['idordenLaboratorio']} | Stage: {$orden['stage']} ({$orden['stage_descripcion']})\n";
            echo "    Cliente: {$orden['cliente_nombre']}\n";
            echo "    Venta: {$orden['venta_folio']}\n";
            echo "    Creada: {$orden['creacion']}\n\n";
        }
    } else {
        echo "  ℹ️ No se crearon órdenes hoy\n\n";
    }
    
    // 4. Verificar integridad de datos
    echo "4. VERIFICACIÓN DE INTEGRIDAD:\n";
    
    // Órdenes sin stage o con stage NULL
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM ordenLaboratorio 
        WHERE (stage IS NULL OR stage = '') AND status = '1'
    ");
    $stmt->execute();
    $sinStage = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($sinStage > 0) {
        echo "  ⚠️ ADVERTENCIA: {$sinStage} órdenes sin stage definido\n";
    } else {
        echo "  ✅ Todas las órdenes activas tienen stage definido\n";
    }
    
    // Órdenes con stages no válidos
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM ordenLaboratorio 
        WHERE stage NOT IN ('1', '9', '10') AND status = '1'
    ");
    $stmt->execute();
    $stageInvalido = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($stageInvalido > 0) {
        echo "  ⚠️ ADVERTENCIA: {$stageInvalido} órdenes con stage no válido\n";
    } else {
        echo "  ✅ Todos los stages son válidos (1, 9, 10)\n";
    }
    
    // 5. Prueba de funcionalidad
    echo "\n5. PRUEBA DE FUNCIONALIDAD:\n";
    echo "Para probar que el stage se guarda correctamente:\n\n";
    echo "a) CREAR NUEVA VENTA:\n";
    echo "   1. Ir a 'Nueva Venta'\n";
    echo "   2. Agregar un armazón\n";
    echo "   3. Hacer clic en el icono de graduación 👓\n";
    echo "   4. Verificar que el select de 'Etapa' aparece\n";
    echo "   5. Seleccionar 'Pendiente de graduar' (9) o 'Terminado' (10)\n";
    echo "   6. Llenar otros campos de graduación\n";
    echo "   7. Guardar graduación y venta\n\n";
    
    echo "b) VERIFICAR EN BASE DE DATOS:\n";
    echo "   Ejecutar: SELECT stage FROM ordenLaboratorio ORDER BY idordenLaboratorio DESC LIMIT 1;\n";
    echo "   Resultado esperado: '9' o '10' según lo seleccionado\n\n";
    
    echo "c) VERIFICAR TICKET DE GRADUACIÓN:\n";
    echo "   1. Después de guardar la venta, debe aparecer el botón 'Ticket de Graduación'\n";
    echo "   2. Al hacer clic, debe generar el PDF correctamente\n\n";
    
    // 6. Diagnóstico final
    echo "6. DIAGNÓSTICO FINAL:\n";
    
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_ordenes,
            COUNT(CASE WHEN stage = '9' THEN 1 END) as pendientes,
            COUNT(CASE WHEN stage = '10' THEN 1 END) as terminadas,
            COUNT(CASE WHEN stage NOT IN ('1', '9', '10') OR stage IS NULL THEN 1 END) as problematicas
        FROM ordenLaboratorio 
        WHERE status = '1' AND creacion >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $stmt->execute();
    $diagnostico = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "En las últimas 24 horas:\n";
    echo "- Total de órdenes: {$diagnostico['total_ordenes']}\n";
    echo "- Pendientes de graduar (9): {$diagnostico['pendientes']}\n";
    echo "- Terminadas (10): {$diagnostico['terminadas']}\n";
    echo "- Problemáticas: {$diagnostico['problematicas']}\n\n";
    
    if ($diagnostico['problematicas'] > 0) {
        echo "🚨 HAY ÓRDENES CON PROBLEMAS EN EL STAGE\n";
    } else if ($diagnostico['pendientes'] > 0 || $diagnostico['terminadas'] > 0) {
        echo "✅ EL STAGE SE ESTÁ GUARDANDO CORRECTAMENTE\n";
    } else {
        echo "ℹ️ No hay órdenes recientes para evaluar\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Error de conexión a la base de datos: " . $e->getMessage() . "\n";
    echo "Por favor, ajusta la configuración de la base de datos en este script.\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== FIN DE LA VERIFICACIÓN ===\n";
?>
