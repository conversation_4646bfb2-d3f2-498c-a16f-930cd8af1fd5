# Ejemplos de Uso - Guardar Stage 9 en Base de Datos

## Resumen
El stage 9 representa el estado "Pendiente" en las órdenes de laboratorio. Este documento muestra cómo guardar el stage 9 en la base de datos usando los endpoints creados.

## Endpoints Disponibles

### 1. Endpoint Específico para Stage 9 (Pendiente)
- **URL**: `/admin/dashboard/set-stage-pendiente`
- **Método**: POST
- **Parámetros**: `idordenlaboratorio` (ID de la orden de laboratorio)

### 2. Endpoint Genérico para Cualquier Stage
- **URL**: `/admin/dashboard/update-stage`
- **Método**: POST
- **Content-Type**: application/json
- **Parámetros**: `{"idordenlaboratorio": ID, "stage": "9"}`

## Ejemplos de Uso

### 1. Usando JavaScript/jQuery (Método Específico)

```javascript
// Función simple para establecer stage 9
function guardarStageNueve(idOrden) {
    $.ajax({
        url: '/admin/dashboard/set-stage-pendiente',
        type: 'POST',
        data: {
            idordenlaboratorio: idOrden
        },
        success: function(response) {
            if (response.success) {
                alert('Stage actualizado a Pendiente correctamente');
                console.log('Respuesta:', response);
            } else {
                alert('Error: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            alert('Error al actualizar el stage: ' + error);
        }
    });
}

// Uso
guardarStageNueve(123); // Donde 123 es el ID de la orden
```

### 2. Usando JavaScript/jQuery (Método Genérico)

```javascript
// Función genérica para cualquier stage
function actualizarStage(idOrden, stage) {
    $.ajax({
        url: '/admin/dashboard/update-stage',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            idordenlaboratorio: idOrden,
            stage: stage
        }),
        success: function(response) {
            if (response.success) {
                alert('Stage actualizado correctamente');
            } else {
                alert('Error: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            alert('Error: ' + error);
        }
    });
}

// Uso para stage 9
actualizarStage(123, '9');
```

### 3. Usando cURL (Línea de comandos)

```bash
# Método específico para stage 9
curl -X POST http://tu-dominio.com/admin/dashboard/set-stage-pendiente \
     -d "idordenlaboratorio=123"

# Método genérico
curl -X POST http://tu-dominio.com/admin/dashboard/update-stage \
     -H "Content-Type: application/json" \
     -d '{"idordenlaboratorio": 123, "stage": "9"}'
```

### 4. Desde PHP (Controlador)

```php
// En cualquier controlador
public function ejemploActualizarStage()
{
    $em = $this->getDoctrine()->getManager();
    $idOrden = 123; // ID de la orden de laboratorio
    
    $orden = $em->getRepository(Ordenlaboratorio::class)->find($idOrden);
    
    if ($orden) {
        $orden->setStage('9'); // Establecer stage 9 (Pendiente)
        $orden->setActualizacion(new \DateTime());
        
        $em->persist($orden);
        $em->flush();
        
        return $this->json(['success' => true, 'message' => 'Stage actualizado']);
    }
    
    return $this->json(['success' => false, 'message' => 'Orden no encontrada']);
}
```

### 5. Integración en Formularios HTML

```html
<!-- Botón simple -->
<button onclick="guardarStageNueve({{ orden.idordenlaboratorio }})">
    Marcar como Pendiente
</button>

<!-- Formulario completo -->
<form id="stage-form">
    <input type="hidden" name="idordenlaboratorio" value="{{ orden.idordenlaboratorio }}">
    <button type="button" onclick="setStageNueve({{ orden.idordenlaboratorio }})">
        Establecer Stage 9 (Pendiente)
    </button>
</form>
```

### 6. Usando el archivo stage-manager.js

```javascript
// Incluir el archivo JavaScript
<script src="{{ asset('js/stage-manager.js') }}"></script>

// Usar las funciones disponibles
setStageNueve(123); // Establecer stage 9

// Con callbacks
setStageNueve(123, 
    function(response) {
        // Éxito
        console.log('Stage actualizado:', response);
    },
    function(error) {
        // Error
        console.error('Error:', error);
    }
);
```

## Respuestas de los Endpoints

### Respuesta Exitosa
```json
{
    "success": true,
    "message": "Stage actualizado a Pendiente (9) correctamente",
    "stage": "9",
    "stage_name": "Pendiente"
}
```

### Respuesta de Error
```json
{
    "success": false,
    "message": "Orden de laboratorio no encontrada"
}
```

## Stages Disponibles

- **1**: Stage inicial
- **9**: Pendiente
- **10**: Entregado

## Notas Importantes

1. **Validación**: Los endpoints validan que la orden de laboratorio exista antes de actualizar
2. **Fecha de actualización**: Se actualiza automáticamente el campo `actualizacion`
3. **Transacciones**: Los cambios se guardan usando transacciones de Doctrine
4. **Manejo de errores**: Todos los endpoints incluyen manejo de errores apropiado

## Ejemplo de Implementación Completa

```twig
{# En tu template Twig #}
<table class="table">
    {% for orden in ordenes %}
    <tr>
        <td>{{ orden.idordenlaboratorio }}</td>
        <td>{{ orden.clienteIdcliente.nombre }}</td>
        <td>
            {% if orden.stage == '9' %}
                <span class="badge badge-warning">Pendiente</span>
            {% elseif orden.stage == '10' %}
                <span class="badge badge-success">Entregado</span>
            {% else %}
                <span class="badge badge-secondary">Stage {{ orden.stage }}</span>
            {% endif %}
        </td>
        <td>
            <button class="btn btn-warning btn-sm" 
                    onclick="setStageNueve({{ orden.idordenlaboratorio }})">
                Marcar Pendiente
            </button>
        </td>
    </tr>
    {% endfor %}
</table>

<script>
// Incluir stage-manager.js y usar las funciones
function setStageNueve(id) {
    setStageNueve(id, function(response) {
        // Recargar la página o actualizar la tabla
        location.reload();
    });
}
</script>
```

Este sistema te permite guardar fácilmente el stage 9 en la base de datos desde cualquier parte de tu aplicación.
