<td>
    {% if object.flujoexpedienteIdflujoexpediente %}
        <!-- DEBUG: Flujo ID = {{ object.flujoexpedienteIdflujoexpediente.idflujoexpediente ?? 'NULL' }} -->
        {% set folios = admin.getVenta(object.flujoexpedienteIdflujoexpediente) %}
        <!-- DEBUG: Folios count = {{ folios|length }} -->
        {% if folios is iterable and folios|length > 0 %}
            {% for folio in folios %}
                {{ folio.folio }}{% if not loop.last %}, {% endif %}
            {% endfor %}
        {% else %}
            <span style="color: orange;">No hay folio ({{ folios|length }} resultados)</span>
        {% endif %}
    {% else %}
        <span style="color: red;">Información no disponible</span>
    {% endif %}
</td>
