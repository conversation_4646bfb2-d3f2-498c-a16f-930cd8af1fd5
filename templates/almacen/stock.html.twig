{% extends 'admin/layout.html.twig' %}
{% block titleHead %}{% endblock %}
{% block title %}Stock por Sucursal{% endblock %}
{% block content %}
    <input id="url-almacen-obtener-informacion-stock" type="hidden" value="{{ path('almacen-obtener-informacion-stock') }}">
    <input id="url-autocompletar-modelo" type="hidden" value="{{path('autocompletar-modelo')}}">
    <input id="url-almacen-stock-report-table" type="hidden" value="{{path('almacen-stock-report-table')}}"> 
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.3.2/css/buttons.dataTables.min.css">
    <link rel="stylesheet" href="{{ asset('/css/puntodeventa/almacen_stock.css') }}">

    <div class="container-fluid">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12 ">
                        <h5 class="card-title reporte">INVENTARIO POR EMPRESA</h5>
                    </div>
                    <br>
                    <div class="col-md-12 opciones" >
                        <div class="col-md-9 usuario">
                                <strong>Usuario conectado: {{ app.user.nombre ~" "~ app.user.apellidopaterno }}</strong>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary reporte" onClick="obtenerStock()">Buscar</button>
                        </div>
                    </div>
                    <br>
                    <div class="col-md-12 opciones">
                        <div class="col-md-4 ">
                                <label class="lab reporte" for="idempresa">Empresas: </label><br>
                                {% if empresas is not empty %}
                                    {# Si hay empresas en la lista, se creará una opción en el select para cada una de ellas. #}
                                    <select name="empresa" id="idempresa" class="form-control" onchange="obtenerSucursales(); obtenerClases(); getTags();">
                                        <option value=-1>Seleccione una empresa</option>
                                        {% for empresa in empresas %}
                                            <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
                                        {% endfor %}
                                    </select>
                                {% else %}
                                    {# Si la lista de empresas está vacía (es decir, el usuario no tiene permiso para ninguna empresa), se mostrará este mensaje. #}
                                    <h4>Aún no tienes empresas asignadas</h4>
                                {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label class="lab reporte">Modelo: </label>
                            <input id="modelo" type="text" class="form-control reset reporte" autocomplete="off">
                        </div>
                        <div class="col-md-4">
                            <label class="lab reporte">Descripción: </label>
                            <input id="descripcion" type="text" class="form-control reset reporte" autocomplete="off" readonly>
                        </div>
                    </div>
                    <br>
                    <div class="col-md-12 filter">
                        <label class="lab reporte">Filtros: </label>
                    </div>
                    <br>
                    <div class="col-md-12 contenedor">
                        <div class="row">
                            <div class="col-12" id="sucursales"></div>
                            <div class="col-sm-6 col-md-3" id="clases"></div>
                            <div class="col-sm-6 col-md-3" id="subcategorias"></div>
                            <div class="col-sm-6 col-md-3" id="marcas"></div>
                            <div class="col-sm-6 col-md-3" id="tags"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container-fluid mt-2">
        <div class="card">
            <div class="card-body">
              <div class="row">
                 <div id="stock-report-table-container"></div>
              </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body" id="contenedor-graficas" >
              <div class="row">
                  <div class="col-md-6">
                      <div id="chart"></div>
                  </div>
                  <div class="col-md-6">
                  </div>
              </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
{{parent()}}

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- DataTables y sus extensiones -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.2/js/buttons.html5.min.js"></script>

    <!-- Tus scripts personalizados -->
    <script src="{{ asset('lib/apexcharts-bundle/dist/apexcharts.js') }}"></script>
    <script src="{{ asset('js/funciones.js') }}"></script>
    <script src="{{ asset('lib/jQuery-Autocomplete-master/dist/jquery.autocomplete.min.js') }}"></script>


    <script>
    
        document.querySelectorAll('.collapse').forEach (ocurrence => {
            ocurrence.addEventListener('show.bs.collapse', function(event) {
                console.log(event.target.id);
                $(".collapse").removeClass("show");
                $(event.target.id).addClass("show");
            })
        })
    

        $(document).ready(function(){

            $("#contenedor-graficas").addClass("d-none");
            var urlAutocompletarModelo=$("#url-autocompletar-modelo").val();

            $('#modelo').autocomplete({
                serviceUrl: urlAutocompletarModelo,
                delay: 100,
                minChars: 3,
                search: function(e,ui){
                    $(this).data("ui-autocomplete").menu.bindings = $();
                },
                onSelect: function (suggestion) {

                $("#modelo").val(suggestion.value);
                $("#descripcion").val(suggestion.descripcion);
                }
            });
        });
        function obtenerSucursales(){
            var idempresa=$("#idempresa").val();

            //console.log(url);
            $.ajax({
                url: "{{path('almacen-obtener-sucursal')}}",
                
                data: {idempresa:idempresa},
                dataType: "html"
            }).done(function( html ) {
                $("#sucursales").html(html);
                obtenerCategorias();

            }).fail(function() {
                alert( "error" );
            });
        }

        function obtenerClases(){
            var idempresa=$("#idempresa").val();

            //console.log(url);
            $.ajax({
                url: "{{path('almacen-obtener-clase')}}",
                
                data: {idempresa:idempresa},
                dataType: "html"
            }).done(function( html ) {
                $("#clases").html(html);
                obtenerCategorias();

            }).fail(function() {
                alert( "error" );
            });
        }

        function obtenerCategorias(){
            
            checkboxesClase = document.getElementsByName('clase');

            clases = [];

            for(var i=0, n=checkboxesClase.length;i<n;i++) {
                
                if(checkboxesClase[i].checked)
                {
                    clases.push(checkboxesClase[i].value);
                }
                    
            }

            $.ajax({
                url: "{{path('almacen-obtener-subcategoria')}}",
                
                data: {clases:clases},
                dataType: "html"
            }).done(function( html ) {
                $("#subcategorias").html(html);
                obtenerMarcas();

            }).fail(function() {
                alert( "error" );
            });
        }

        function obtenerMarcas(){
            
            checkboxesCategorias = document.getElementsByName('categoria');

            categorias = [];

            for(var i=0, n=checkboxesCategorias.length;i<n;i++) {
                
                if(checkboxesCategorias[i].checked)
                {
                    categorias.push(checkboxesCategorias[i].value);
                }
                    
            }

            //console.log(url);
            $.ajax({
                url: "{{path('almacen-obtener-marcas')}}",
        
                data: {categorias:categorias},
                dataType: "html"
            }).done(function( html ) {
                $("#marcas").html(html);

            }).fail(function() {
                alert( "error" );
            });
        }

        function getTags(){
            var enterpriseId=$("#idempresa").val();
            $.ajax({
                url: "{{path('almacen-get-tags')}}",
                data: {enterpriseId:enterpriseId},
                dataType: "html"
            }).done(function( html ) {
                $("#tags").html(html);
            }).fail(function() {
                alert( "error" );
            });
        }

        function obtenerStock(){
            
            $("#contenedor-graficas").removeClass("d-none");
            checkboxesSucursal = document.getElementsByName('sucursal');
            checkboxesBodegas = document.getElementsByName('bodega');
            checkboxesCampañas = document.getElementsByName('campaña');
            checkboxesClase = document.getElementsByName('clase');
            checkboxesCategorias = document.getElementsByName('categoria');
            checkboxesMarcas = document.getElementsByName('marca');
            checkboxesTags = document.getElementsByName('tag');
            modelo = $("#modelo").val();
            idempresa = $('#idempresa').val();

            sucursales = [];
            bodegas = [];
            campañas = [];
            clases = [];
            categorias = [];
            marcas = [];
            tags = [];

            for(var i=0, n=checkboxesSucursal.length;i<n;i++) {
                
                if(checkboxesSucursal[i].checked)
                {
                    sucursales.push(checkboxesSucursal[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesBodegas.length;i<n;i++) {
                
                if(checkboxesBodegas[i].checked)
                {
                    bodegas.push(checkboxesBodegas[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesCampañas.length;i<n;i++) {
                
                if(checkboxesCampañas[i].checked)
                {
                    campañas.push(checkboxesCampañas[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesClase.length;i<n;i++) {
                
                if(checkboxesClase[i].checked)
                {
                    clases.push(checkboxesClase[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesCategorias.length;i<n;i++) {
                
                if(checkboxesCategorias[i].checked)
                {
                    categorias.push(checkboxesCategorias[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesMarcas.length;i<n;i++) {
                
                if(checkboxesMarcas[i].checked)
                {
                    marcas.push(checkboxesMarcas[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesTags.length;i<n;i++) {
                
                if(checkboxesTags[i].checked)
                {
                    tags.push(checkboxesTags[i].value);
                }
                    
            }

            sucursalesSeleccionadas = [...sucursales, ...bodegas, ...campañas];

            var url =$("#url-almacen-obtener-informacion-stock").val();

            $.ajax({
                url: url,
                data: {clases:clases, sucursales:sucursalesSeleccionadas, categorias:categorias, marcas:marcas, tags:tags, modelo:modelo},
                dataType: "html",
                beforeSend: function( xhr ) {

                    loadingGif("contenedor-graficas");
                    //$("#contenedor-graficas").html('<img src="{{asset('img/log.gif')}}">');
                  //git de carga
                }
            }).done(function( html ) {
                $("#contenedor-graficas").html(html);
                stockReportTable();

            }).fail(function() {
                alert( "error" );
            });
        }


        function stockReportTable(){

            var url=$("#url-almacen-stock-report-table").val();

            checkboxesSucursal = document.getElementsByName('sucursal');
            checkboxesBodegas = document.getElementsByName('bodega');
            checkboxesCampañas = document.getElementsByName('campaña');
            checkboxesClase = document.getElementsByName('clase');
            checkboxesCategorias = document.getElementsByName('categoria');
            checkboxesMarcas = document.getElementsByName('marca');
            checkboxesTags = document.getElementsByName('tag');
            modelo = $("#modelo").val();
            idempresa = $('#idempresa').val();

            sucursales = [];
            bodegas = [];
            campañas = [];
            clases = [];
            categorias = [];
            marcas = [];
            tags = [];

            for(var i=0, n=checkboxesSucursal.length;i<n;i++) {
                
                if(checkboxesSucursal[i].checked)
                {
                    sucursales.push(checkboxesSucursal[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesBodegas.length;i<n;i++) {
                
                if(checkboxesBodegas[i].checked)
                {
                    bodegas.push(checkboxesBodegas[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesCampañas.length;i<n;i++) {
                
                if(checkboxesCampañas[i].checked)
                {
                    campañas.push(checkboxesCampañas[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesClase.length;i<n;i++) {
                
                if(checkboxesClase[i].checked)
                {
                    clases.push(checkboxesClase[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesCategorias.length;i<n;i++) {
                
                if(checkboxesCategorias[i].checked)
                {
                    categorias.push(checkboxesCategorias[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesMarcas.length;i<n;i++) {
                
                if(checkboxesMarcas[i].checked)
                {
                    marcas.push(checkboxesMarcas[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesTags.length;i<n;i++) {
                
                if(checkboxesTags[i].checked)
                {
                    tags.push(checkboxesTags[i].value);
                }
                    
            }


            sucursalesSeleccionadas = [...sucursales, ...bodegas, ...campañas];

            $.ajax({
                url: url,
                data: {clases:clases, sucursales:sucursalesSeleccionadas, categorias:categorias, marcas:marcas, tags:tags, modelo:modelo},
                dataType: "html",
                beforeSend: loadingGif("stock-report-table-container"),
            }).done(function( html ) {
                $("#stock-report-table-container").html(html);

            }).fail(function() {
                alert( "error" );
            });
        }

    </script>
{% endblock %}
