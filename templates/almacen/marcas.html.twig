<div class="cont">
    <label class="filter-title">MARCAS:</label>
    <div class="col-md-12 miLista">
        <fieldset id="idmarcas" >
            <label class="check" for="todasMarcas" >
                <input type="checkbox" name="todasMarcas" id="todasMarcas" onClick="toggleMarcas(this)">
            TODAS</label>
            {% for marca in marcas %}
                <label class="check" for="checkbox-marca-{{ marca.idmarca  }}" > 
                    <input type="checkbox" id="checkbox-marca-{{ marca.idmarca }}" name="marca" value="{{ marca.idmarca  }}">
                {{ marca.nombre }}</label>
            {% endfor %}
        </fieldset>
    </div>
</div>


<script language="JavaScript">
    function toggleMarcas(source) 
    {
        checkboxes = document.getElementsByName('marca');
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = source.checked;
        }
    }
</script>