<link rel="stylesheet" href="{{ asset('/css/puntodeventa/filter_bussines.css') }}"> 
<div class="row">
    <div class="col-12 col-sm-6 col-md-4 col-xl-4">
        <label class="filter-title">Sucursales: </label> 
        <div class="col-md-12 miLista">
            <fieldset id="idsucursal">
                <label class="check" for="todasSucursales">
                    <input type="checkbox" name="todasSucursales" onClick="toggleSucursales(this)" id="todasSucursales" checked>TODAS</label>
                {% for sucursal in sucursales %}
                    <label class="check" for="checkbox-sucursal-{{ sucursal.idsucursal }}">
                        <input type="checkbox" id="checkbox-sucursal-{{ sucursal.idsucursal }}" name="sucursal" value="{{ sucursal.idsucursal }}" checked>
                    {{ sucursal.nombre }}</label>
                {% endfor %}
                <br> <br>
            </fieldset>
        </div>
    </div>

    <div class="col-12 col-sm-6 col-md-4 col-xl-4">
        <label class="filter-title">Bodegas:</label>
        <div class="col-md-12 miLista">
            <fieldset id="idsucursal">
                <label class="check" for="todasBodegas">
                    <input type="checkbox" name="todasBodegas" onClick="toggleBodegas(this)" id="todasBodegas" checked>
                TODAS</label>
                {% for bodega in bodegas %}
                    <label class="check" for="checkbox-bodega-{{ bodega.idsucursal }}">
                        <input type="checkbox" id="checkbox-bodega-{{ bodega.idsucursal }}" name="bodega" value="{{ bodega.idsucursal }}" checked>
                    {{ bodega.nombre }}</label>
                {% endfor %}
            </fieldset>
        </div>
    </div>

    <div class="col-12 col-sm-12 col-md-4 col-xl-4">
        <label class="filter-title">Campañas:</label>
        <div class="col-md-12 miLista">
            <fieldset id="idsucursal">
                <label class="check" for="todasCampañas">
                    <input type="checkbox" name="todasCampañas" onClick="toggleCampañas(this)" id="todasCampañas" checked>
                TODAS</label>
                {% for campaña in campañas %}
                    <label class="check" for="checkbox-campana-{{ campaña.idsucursal }}">
                        <input type="checkbox" id="checkbox-campana-{{ campaña.idsucursal }}" name="campaña" value="{{ campaña.idsucursal }}" checked>
                    {{ campaña.nombre }}</label>
                {% endfor %}
            </fieldset>
        </div>
    </div>
</div>

<script language="JavaScript">
    function toggleSucursales(source) {
        var checkboxes = document.getElementsByName('sucursal');
        for(var i=0, n=checkboxes.length; i<n; i++) {
            checkboxes[i].checked = source.checked;
        }
    }

    function toggleBodegas(source) {
        var checkboxes = document.getElementsByName('bodega');
        for(var i=0, n=checkboxes.length; i<n; i++) {
            checkboxes[i].checked = source.checked;
        }
    }

    function toggleCampañas(source) {
        var checkboxes = document.getElementsByName('campaña');
        for(var i=0, n=checkboxes.length; i<n; i++) {
            checkboxes[i].checked = source.checked;
        }
    }
</script>
