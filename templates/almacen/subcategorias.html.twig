<div class="cont">
    <label class="filter-title">SUBCATEGORÍAS:</label>
    <div class="col-md-12 miLista">
        <fieldset id="idcategoria" onchange="obtenerMarcas()" >
            <label class="check" for="todasCategorias" >
                <input type="checkbox" name="todasCategorias" id="todasCategorias" onClick="toggleCategorias(this)">
            TODAS</label>
            {% for categoria in categorias %}
                <label class="check" for="checkbox-categoria-{{ categoria.idcategoria }}" >
                    <input type="checkbox" id="checkbox-categoria-{{ categoria.idcategoria }}" name="categoria" value="{{ categoria.idcategoria }}">
                {{ categoria.nombre }}</label>
            {% endfor %}
            <br> <br>
        </fieldset>
    </div>
</div>


<script language="JavaScript">
    function toggleCategorias(source) 
    {
        checkboxes = document.getElementsByName('categoria');
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = source.checked;
        }
    }
</script>