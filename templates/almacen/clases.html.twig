<div class="cont">
    <label class="filter-title">CATEGORÍAS:</label>
    <div class="col-md-12 miLista">
        <fieldset id="idclase" onChange="obtenerCategorias()">
            <label class="check" for="todasClases" >
                <input type="checkbox" name="todasClases" id="todasClases" onClick="toggleClases(this)">
            Todas</label>
            {% for clase in clases %}
                <label class="check" for="checkbox-clase-{{ clase.idclase }}" >
                    <input type="checkbox" id="checkbox-clase-{{ clase.idclase }}" name="clase" value="{{ clase.idclase }}">
                {{ clase.nombre }}</label>
            {% endfor %}
        </fieldset>
    </div>
</div>

<script language="JavaScript">

    function toggleClases(source) 
    {
        checkboxes = document.getElementsByName('clase');
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = source.checked;
        }
    }
</script>