{% extends 'admin/layout.html.twig' %}
{% block titleHead %}{% endblock %}
{% block title %}Nueva Venta{% endblock %}
{% block content %}

    <!--These are the libraries that I used for the CSS of this new version of the new sale, it also includes the icon library.-->
    <link rel="stylesheet" href="{{ asset('/css/puntodeventa/nuevaVenta.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/puntodeventa/nuevaVenta.css') }}">

    <link rel="stylesheet" href="{{ asset('/lib/jQuery-Autocomplete-master/content/styles.css') }}">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <style>
        /* Estilos compartidos para los modales */
        .mod {
            display: none !important; /* Ensure modals are hidden by default */
        }

        .mod.show {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex !important; /* Override the display:none when showing */
            align-items: center;
            justify-content: center;
            z-index: 1050;
        }

        .mod-dialog {
            width: 95%;
            max-width: 1200px;
            margin: 1.75rem auto;
        }

        .modal-content {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;
            padding: 1.5rem;
            position: sticky;
            top: 0;
            background: #0d6efd;
            z-index: 1;
            color: white;
        }

        .modal-body {
            padding: 2rem;
        }

        .form-floating > .form-control {
            border-radius: 8px;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
            height: 3.5rem;
        }

        .form-floating > label {
            padding: 1rem;
        }

        .form-floating > .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        .btn-primary {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.8rem 2rem;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .table-responsive {
            margin-top: 2rem;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }

        .table {
            margin-bottom: 0;
        }

        /* Estilos para los inputs de graduación */
        .graduation-select {
            font-size: 6.1rem !important;
            padding: 0.5rem !important;
            height: auto !important;
        }

        /* Estilos para el select de etapas con colores */
        #stage {
            font-weight: bold;
            transition: all 0.3s ease;
        }

        /* Stage Pendiente (9) - Amarillo */
        #stage.stage-pendiente {
            background-color: #fff3cd !important;
            border-color: #ffc107 !important;
            color: #856404 !important;
        }

        /* Stage Terminado (10) - Verde */
        #stage.stage-terminado {
            background-color: #d4edda !important;
            border-color: #28a745 !important;
            color: #155724 !important;
        }

        /* Estilos para las opciones del select */
        #stage option[value="9"] {
            background-color: #fff3cd;
            color: #856404;
        }

        #stage option[value="10"] {
            background-color: #d4edda;
            color: #155724;
        }

        /* Estilos para productos según stage de graduación */
        .producto-stage-pendiente {
            background-color: #fff3cd !important;
            border-left: 4px solid #ffc107 !important;
        }

        .producto-stage-terminado {
            background-color: #d4edda !important;
            border-left: 4px solid #28a745 !important;
        }

        /* Indicador visual en el icono de graduación */
        .fa-glasses.stage-pendiente {
            color: #ffc107 !important;
        }

        .fa-glasses.stage-terminado {
            color: #28a745 !important;
        }

        .option-graduacion{
            font-size: 1.7rem; !important;
        }

        /* Estilos para los botones de ticket */
        .ticket-buttons-container {
            margin: 20px 0;
        }

        .ticket-button-wrapper {
            flex: 0 0 auto;
            width: 200px;
            margin-bottom: 15px;
        }

        .ticket-button {
            width: 100%;
            height: 140px;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            background: linear-gradient(145deg, #ffffff, #f0f0f0);
            color: #333;
            font-weight: 600;
            padding: 15px;
        }

        .ticket-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }

        .ticket-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .ticket-button i {
            margin-bottom: 10px;
            color: #0d6efd;
        }

        .ticket-button span {
            font-size: 16px;
            text-align: center;
        }

        /* Estilos específicos para cada tipo de botón */
        .ticket-button {
            background: linear-gradient(145deg, #e3f2fd, #bbdefb);
            border-left: 5px solid #2196f3;
        }

        .ticket-button-especial {
            background: linear-gradient(145deg, #e8f5e9, #c8e6c9);
            border-left: 5px solid #4caf50;
        }

        .ticket-button-especial i {
            color: #4caf50;
        }

        .ticket-button-graduacion {
            background: linear-gradient(145deg, #fff3e0, #ffe0b2);
            border-left: 5px solid #ff9800;
        }

        .ticket-button-graduacion i {
            color: #ff9800;
        }

        .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            padding: 1.2rem 1rem;
            font-weight: 600;
            white-space: nowrap;
        }

        .table tbody td {
            padding: 1.2rem 1rem;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: rgba(13, 110, 253, 0.05);
        }

        .btn-info {
            background-color: #0dcaf0;
            border-color: #0dcaf0;
            color: white;
            border-radius: 6px;
            padding: 0.7rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn-info:hover {
            background-color: #0bacce;
            border-color: #0bacce;
            transform: translateY(-1px);
        }

        .btn-close-white {
            filter: brightness(0) invert(1);
        }

        @media (max-width: 768px) {
            .mod-dialog {
                width: 95%;
                margin: 0.5rem auto;
            }

            .modal-body {
                padding: 1rem;
            }
        }

        /* Estilos para el modal de búsqueda de clientes */
        #modalBuscarCliente {
            display: none !important; /* Ensure modal is hidden by default */
        }

        #modalBuscarCliente.mod.show {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex !important; /* Override the display:none when showing */
            align-items: center;
            justify-content: center;
            z-index: 1050;
        }

        #modalBuscarCliente .mod-dialog {
            width: 95%;
            max-width: 1200px;
            margin: 1.75rem auto;
        }

        #modalBuscarCliente .modal-content {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            max-height: 90vh;
            overflow-y: auto;
        }

        #modalBuscarCliente .modal-header {
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;
            padding: 1.5rem;
            position: sticky;
            top: 0;
            background: #0d6efd;
            z-index: 1;
        }

        #modalBuscarCliente .modal-body {
            padding: 2rem;
        }

        #modalBuscarCliente .form-floating > .form-control {
            border-radius: 8px;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
            height: 3.5rem;
        }

        #modalBuscarCliente .form-floating > label {
            padding: 1rem;
        }

        #modalBuscarCliente .form-floating > .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        #modalBuscarCliente .btn-primary {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        #modalBuscarCliente .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        #modalBuscarCliente .table {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }

        #modalBuscarCliente .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            padding: 1rem;
            font-weight: 600;
        }

        #modalBuscarCliente .table tbody td {
            padding: 1rem;
            vertical-align: middle;
        }

        #modalBuscarCliente .table tbody tr:hover {
            background-color: rgba(13, 110, 253, 0.05);
        }

        #modalBuscarCliente .btn-info {
            background-color: #0dcaf0;
            border-color: #0dcaf0;
            color: white;
            border-radius: 6px;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        #modalBuscarCliente .btn-info:hover {
            background-color: #0bacce;
            border-color: #0bacce;
            transform: translateY(-1px);
        }
    </style>

    <input id="url-imprimir-ticket" type="hidden" value="{{ path('imprimir-ticket') }}">
    <input id="url-ticket" type="hidden" value="{{ path('ticket') }}">
    <input id="url-guardar-venta" type="hidden" value="{{ path('guardar-venta') }}">
    <input id="url-buscar-cliente" type="hidden" value="{{ path('buscar-cliente') }}">
    <input id="url-buscar-numero-cliente" type="hidden" value="{{ path('buscar-numero-cliente') }}">
    <input id="url-buscar-empresa-cliente" type="hidden" value="{{ path('buscar-empresa-cliente') }}">

    <input id="url-venta-search-client-email" type="hidden" value="{{ path('venta-search-client-email') }}">
    <input id="url-venta-search-client-phone" type="hidden" value="{{ path('venta-search-client-phone') }}">

    <!-- Paso 3 -->
    <input id="url-seleccionar-clientes" type="hidden" value="{{ path('seleccionar-clientes') }}">
    <input id="url-seleccionar-email" type="hidden" value="{{ path('seleccionar-email') }}">
    <input id="url-seleccionar-telefono" type="hidden" value="{{ path('seleccionar-telefono') }}">

    <input id="url-buscar-beneficiarios" type="hidden" value="{{ path('buscar-beneficiarios') }}">
    <input id="url-buscar-producto-codigo" type="hidden" value="{{ path('buscar-producto-codigo') }}">
    <input id="url-buscar-producto-nombre" type="hidden" value="{{ path('buscar-producto-nombre') }}">
    <input id="url-buscar-tratamiento" type="hidden" value="{{ path('buscar-tratamiento') }}">
    <input id="url-buscar-venta" type="hidden" value="{{ path('buscar-venta') }}">
    <input id="url-nueva-venta" type="hidden" value="{{ path('nueva-venta') }}">
    <input id="btn-facturar" type="hidden" value="{{ path('ventafactura') }}">
    <input id="url-buscar-cupon" type="hidden" value="{{ path('buscar-cupon') }}">
    <input id="url-quitar-cupon" type="hidden" value="{{ path('quitar-cupon') }}">
    <input id="url-visor-documentos" type="hidden" value="{{ path('visor-documentos') }}">
    <input id="url-obtener-informacion-tipo-venta" type="hidden" value="{{ path('obtener-informacion-tipo-venta') }}">
    <input id="url-venta-subir-documento-venta" type="hidden" value="{{ path('venta-subir-documento-venta') }}">

    <input id="url-ventas-sale-document-table" type="hidden" value="{{ path('ventas-sale-document-table') }}">
    <input id="url-ventas-sale-document-form" type="hidden" value="{{ path('ventas-sale-document-form') }}">
    <input id="url-ventas-upload-sale-document" type="hidden" value="{{ path('ventas-upload-sale-document') }}">

    <input id="url-ventas-beneficiary-table" type="hidden" value="{{ path('ventas-beneficiary-table') }}">
    <input id="url-ventas-delete-beneficiary" type="hidden" value="{{ path('ventas-delete-beneficiary') }}">
    <input id="url-ventas-get-fixed-products" type="hidden" value="{{ path('ventas-get-fixed-products') }}">
    <input id="ventas-check-poli" type="hidden" value="{{ path('ventas-check-poli') }}">

    <input id="idventa" type="hidden" value="">
    <input id="folio" type="hidden" value="">
    <input id="escotizacion" type="hidden" value="">
    <input id="descuentoproductosalmacenables" type="hidden" value="">
    <input id="descuentoservicios" type="hidden" value="">
    <input id="preciofijoproductosalmacenables" type="hidden" value="">
    <input id="preciofijoservicios" type="hidden" value="">
    <input id="idempresa" type="hidden" value={{ idempresa }} disabled>
    <input id="preciobase" type="hidden" value="">
    <input disabled type="text" class="form-control reset text-center d-none" id="fix-payment-type">
    <input disabled type="text" class="form-control reset text-center d-none" id="fix-payment-id">
    <input disabled type="text" class="form-control reset text-center d-none" id="totalventaiva">
    <input disabled type="text" class="form-control reset text-center d-none" id="subtotalfijo">
    <input disabled type="text" class="form-control reset text-center d-none" id="ivafijo">
    <input disabled type="text" class="form-control reset text-center d-none" id="documentoobligatoriocerrarventa">

    <input disabled type="number" class="d-none" id="iva-value"
           value={{ app.user.sucursalIdsucursal.porcentajeiva ?? 16 }}>



    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card nueva-venta ">
                    <!--This part is the header the option-->
                    <!--Include:The title, New Sale, Search cotizathion, More information about the user-->
                    <div class="card-header card-header-info">
                        <div class="row">
                            <div class="col-10 col-md-11 col-lg-11 col-xl-4 text-left">
                                <h2 class="card-title"><a href="{{ path('nueva-venta') }}" type="button"
                                                          style="color: #000000">Nueva Venta</a></h2>
                            </div>

                            <div class="col-2 col-md-1 col-lg-1 d-xl-none ">
                                <i class="fa-solid fa-bars fa-xl" style="color: #000000; padding-top: 25px;"></i>
                                <i class="fa-solid fa-xmark fa-xl d-none"
                                   style="color: #000000; padding-top: 25px;"></i>
                            </div>
                            <div class="col-12 col-md-12 col-lg-12 col-xl-3">
                                <div class="container-information-new-sale bg-light">
                                    <p class="txt-information-new-sale">
                                        Sucursal: {{ app.user.sucursalIdsucursal.nombre }}</p>
                                    <p class="txt-information-new-sale">Usuario
                                        conectado: {{ app.user.nombre ~" "~app.user.apellidopaterno }}</p>
                                </div>
                            </div>
                            <div class="col-12 col-md-12 col-lg-12 col-xl-5 stash d-xl-block text-end" id="options-bar">
                                <div class="row container-option-new-sale">
                                    <div class="col-12 col-md-4 option-1-new-sale">
                                        <buttom class="text-option-new-sale"><i
                                                    class="fa-solid fa-circle-info separation-new-sale fa-lg "
                                                    style="color: #00aaff;"></i>nformación
                                        </buttom>
                                    </div>
                                    <div class="col-12 col-md-4 option-2-new-sale">
                                        <buttom class="text-option-new-sale"><i
                                                    class="fa-solid fa-tag separation-new-sale fa-lg"
                                                    style="color: #adadad;"></i> <a href="" id="venta" type="button">Venta</a>
                                        </buttom>
                                    </div>
                                    <div class="col-12 col-md-4 option-3-new-sale">
                                        <buttom class="text-option-new-sale" type="button" data-bs-toggle="mod"
                                                data-bs-target="#modal-buscar-cotizacion"><i
                                                    class="fa-solid fa-magnifying-glass separation-new-sale fa-lg"
                                                    style="color: #000000;"></i>Cotización
                                        </buttom>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <p class="card-category"></p>
                        <!-- Modal de busqueda de cotización-->
                        <div class="mod" id="modal-buscar-cotizacion" aria-labelledby="modal-buscar-cotizacionlLabel"
                             aria-hidden="true" tabindex="-1">
                            <div class="mod-dialog mod-dialog-centered">
                                <div class="modal-content">
                                    <div class="modal-header justify-content-center">
                                        <h5 class="modal-title" id="exampleModalLabel">Buscar cotización</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="mod"
                                                aria-label="Close"></button>
                                    </div>

                                    <div class="modal-body d-flex flex-column align-items-center">
                                        <div class="col-md-9">
                                            <label for="inputPassword2" class="visually-hidden"></label>
                                            <input type="text" class="form-control text-center"
                                                   id="busqueda-numero-folio" placeholder="#######"
                                                   style="border-bottom: 1px solid #c3c3c3;">
                                            <button type="button" class="btn btn-primary mb-3 mt-1"
                                                    onclick="buscarCotizacion();return false;">Buscar
                                            </button>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12 text-center" id="modal-busqueda-msj">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="modal-footer text-center">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body container-form-new-sale">
                        <div class="container-modules-new-sale">
                            <!--Module to Sale-->
                            <div class="row margin-buttom-new-sale sale">
                                <div class="col-12">
                                    <div class="row border-title-new-sale">
                                        <div class="col-8 col-md-10">
                                            <h4 class="div-title">Venta</h4>
                                        </div>

                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="row mb-4">
                                        <div class="col-12 col-md-6">
                                            <div class="form-group">

                                                <input type="hidden" id="pagoAlFinal" value=""/>
                                                <label class="titulotext">Tipo de venta <span class="text-danger">(obligatorio)</span>
                                                </label>
                                                <select id="convenio" class="form-control"
                                                        onchange="cambiarFormulario(this)"
                                                        data-mostrarnumeroempleado="Prueba">
                                                    <option value="">Selecciona una opción</option>
                                                    {% for tipoventa in tiposVenta %}
                                                        <option value="{{ tipoventa.idtipoventa }}"
                                                                data-mostrarnumeroempleado="{{ tipoventa.mostrarnumeroempleado }}"
                                                                data-mostrarbeneficiario="{{ tipoventa.mostrarbeneficiario }}"
                                                                data-mostrarunidadprocedencia="{{ tipoventa.mostrarunidadprocedencia }}"
                                                                data-mostrarcampogarantia="{{ tipoventa.mostrarcampogarantia }}"
                                                                data-nota="{{ tipoventa.nota }}"
                                                                data-pagoalfinal="{{ tipoventa.pagoalfinal }}">
                                                            {% if tipoventa.nombre is defined and tipoventa.nombre =="" %}Público en General{% elseif tipoventa.nombre is defined and tipoventa.nombre =="UAM" %}Prestación UAM{% elseif tipoventa.nombre is defined %}{{ tipoventa.nombre }}{% else %}Sin nombre{% endif %}</option>
                                                    {% endfor %}
                                                </select>


                                            </div>
                                            <div class="col-md-12 col-md-12">
                                                <div class="col-12 col-md-12  d-none" id="title-warranty">
                                                    <h4 class="">Garantia</h4>
                                                </div>

                                                <div class="col-12 separation-top-new-sale margin-buttom-new-sale d-none datos-extra"
                                                     id="contenedor-folio-venta-garantia">

                                                    <label class="titulotext">Folio de venta de garantía</label>
                                                    <div class="container-code-bar-new-sale">
                                                        <input id="folio-venta-garantia" type="text" value=""
                                                               class="form-control reset ">
                                                        <button style="margin-top: 0px !important;"
                                                                id="buscar-venta-garantia" type="button"
                                                                class="btn btn-success mb-2 agregar-venta mt-1"
                                                                onclick="buscarVenta()"><i
                                                                    class="fa-solid fa-magnifying-glass"
                                                                    style="col-mdor: #ffffff;"></i></button>
                                                        <button style="margin-top: 0px !important;"
                                                                id="quitar-venta-garantia" type="button"
                                                                class="btn btn-warning mb-2 quitar-venta d-none mt-1"
                                                                onclick="quitarVenta()"><i class="fa-solid fa-xmark"
                                                                                           style="color: #ff0000;"></i>
                                                        </button>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 col-md-6 margin-buttom-new-sale">

                                            <label class="titulotext">Información del Tipo de Venta </label>
                                            <div id="msj-tipo-venta" class=" form-control"></div>
                                            {% if venta is not null or venta is not empty %}
                                                <h4>Folio: {{ venta.folio }}</h4>
                                                <h4>
                                                    Folio: {% if venta.cotizacion =="1" %}Es cotización{% else %}Es Venta{% endif %}</h4>
                                            {% endif %}
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <!--Module to User Date-->
                            <div class="row margin-buttom-new-sale userdate" id="datos-cliente-sin-convenio">
                                <div class="col-12">
                                    <div class="row border-title-new-sale">
                                        <div class="col-12 col-md-12 col-xl-8">
                                            <h4 class="div-title">Datos del cliente <strong class="text-danger">(obligatorio)</strong>
                                            </h4>
                                        </div>
                                        <div class="col-6 col-md-6 col-xl-2 bottom-align-new-sale">
                                            <button id="buscar-cliente-button" type="button"
                                                    class="btn btn-success mt-5 btn-nuevo-cliente style-button-new-sale client-button"
                                                    data-bs-toggle="mod" data-bs-target="#modalBuscarCliente">Buscar
                                                Cliente
                                            </button>
                                        </div>
                                        <div class="col-6 col-md-6 col-xl-2 bottom-align-new-sale">
                                            <!--<button id="resaltar-formulario"  type="button" class="btn btn-primary mt-5 btn-nuevo-cliente style-button-new-sale" onclick="resaltarFormularioRegistrar(1)">Nuevo Cliente</button>
                        <button type="button" class="btn btn-warning mt-5 btn-reset-cliente d-none style-button-new-sale cancel" onclick="resaltarFormularioRegistrar(0)">Cancelar</button>-->
                                            <button id="nuevo-cliente-button" type="button"
                                                    onclick="obtenerFormularioAgregarBeneficiario(1)"
                                                    class="btn btn-primary client-button" data-bs-toggle="mod"
                                                    data-bs-target="#modalAgregarBeneficiarios">Nuevo Cliente
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="row">
                                        <div class="col-12 col-md-6 col-xl-4 separation-top-movil-new-sale">
                                            <label class="titulotext">Apellido paterno: </label>
                                            <input id="apellidoP" type="text"
                                                   class="form-control reset registro disabled-escritura" disabled>
                                        </div>
                                        <div class="col-12 col-md-6 col-xl-4 separation-top-movil-new-sale">
                                            <label class="titulotext">Apellido materno: </label>
                                            <input id="apellidoM" type="text"
                                                   class="form-control reset registro disabled-escritura" disabled>
                                        </div>
                                        <div class="col-12 col-md-6 col-xl-4 separation-top-movil-new-sale">
                                            <label class="titulotext">Nombre(s):</label>
                                            <input id="nombre" type="text"
                                                   class="form-control reset registro disabled-escritura" disabled>
                                        </div>
                                        <div class="col-12 col-md-6 col-xl-4 separation-top-new-sale">
                                            <label class="titulotext">Empresa de cliente:</label>
                                            <input id="empresa-cliente" type="text"
                                                   class="form-control reset registro disabled-escritura">
                                            <input id="empresa-cliente-id" type="hidden"
                                                   class="form-control reset registro disabled-escritura" disabled>
                                            <input id="cliente-id" type="hidden"
                                                   class="form-control reset registro disabled-escritura" disabled>
                                            <input id="dias-credito" type="hidden"
                                                   class="form-control reset registro disabled-escritura" disabled>
                                        </div>
                                        <div class="col-12 col-md-6 col-xl-4 separation-top-new-sale">
                                            <label class="titulotext">Correo electrónico</label>
                                            <input id="sin-convenio-email-cliente"
                                                   class="form-control reset registro sin-convenio-email-cliente disabled-escritura">
                                        </div>
                                        <div class="col-12 col-md-6 col-xl-4 separation-top-new-sale">
                                            <label class="titulotext">Teléfono <span
                                                        class="text-danger">(obligatorio):</span></label>
                                            <input id="cliente-telefono"
                                                   class="form-control reset registro cliente-telefono">
                                        </div>
                                        <div class="col-12 col-md-6 col-xl-4 separation-top-new-sale">
                                            <label class="titulotext">Unidad de procedencia:</label>
                                            <select id="unidad" class="form-control registro disabled-escritura">
                                                <option value=""></option>
                                                {% for unidad in unidades %}
                                                    <option value="{{ unidad.idunidad }}">{{ unidad.nombre ?? 'Sin nombre' }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>

                                        <div class="col-12 col-md-6 col-xl-4 separation-top-new-sale d-none datos-extra"
                                             id="contenedor-numero-empleado">
                                            <div class="form-group">
                                                <label class="titulotext">Número de empleado</label>
                                                <input id="numero-empleado" type="text" value=""
                                                       class="numero-empleado form-control reset registro ">
                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <!--Modal to Search User (moved to the end of the file)-->
                                <!--modal para el tipo de venta--->
                                <div class="mod " id="modal-tipo-venta" tabindex="-1"
                                     aria-labelledby="modal-tipo-ventaLabel" aria-hidden="true">
                                    <div class=" mod-dialog modal-sm modal-dialog-centered modal-dialog-scrollable">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h1 class="modal-title fs-5" id="exampleModalLabel">Información del tipo
                                                    de venta</h1>
                                                <button type="button" class="btn-close" data-bs-dismiss="mod"
                                                        aria-label="Cerrar"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div id="msj-tipo-venta" class=" form-control"></div>

                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">
                                                    Cerrar
                                                </button>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!--Module for beneficiaries-->

                            <div class="row margin-buttom-new-sale userdate d-none" id="contenedor-beneficiario">
                                <div class="col-12">
                                    <div class="row border-title-new-sale">
                                        <div class="col-12 col-md-12 col-xl-8">
                                            <h4 class="div-title">Beneficiarios</h4>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-md-6 col-xl-4 separation-top-new-sale">
                                    <div class="form-group">
                                        <button onclick="beneficiaryTable()" type="button" class="btn btn-success "
                                                data-bs-toggle="mod" data-bs-target="#modal-select-beneficiaries">
                                            Seleccionar beneficiarios
                                        </button>
                                    </div>
                                </div>

                                <table class="table" id="beneficiary-selected-table">

                                    <thead>
                                    <th>Apellido paterno</th>
                                    <th>Apellido materno</th>
                                    <th>Nombre</th>
                                    <th>Relación con el cliente</th>
                                    <th></th>
                                    </thead>
                                    <tbody id="beneficiary-tablebody">

                                    </tbody>
                                </table>

                                <div class="col-12 col-md-6 col-xl-4 separation-top-new-sale datos-extra">
                                    <div class="form-group">
                                        <label class="titulotext">Beneficiario:</label>
                                        <input id="beneficiario-id" type="hidden" value="" class="reset"/>
                                        <input id="beneficiario-nombre" type="hidden" value="" class="reset"/>
                                        <select id="beneficiario" class="form-control reset"
                                                onchange="seleccionarBeneficiario(this)">
                                        </select>

                                    </div>
                                </div>


                            </div>


                            <!--Module to Add products-->
                            <div class="row margin-buttom-new-sale addproducts d-none">
                                <div class="col-12 border-title-new-sale">
                                    <h4 class="div-title">Agregar Productos</h4>
                                </div>
                                <div class="col-12 col-md-6">
                                    <div class="form-group">
                                        <label class="titulotext">Código de barras o SKU:</label>
                                        <div class="container-code-bar-new-sale">
                                            <input id="producto-codigo" type="text" class="form-control reset "
                                                   autocomplete="off">
                                            <button class="btn btn-success" id="btn-producto-codigo" type="button"
                                                    onclick="buscarProductoPorCódigo()"><i
                                                        class="fa-solid fa-magnifying-glass"
                                                        style="col-mdor: #ffffff;"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-6">
                                    <div class="form-group">
                                        <label class="titulotext">Micas / Servicios:</label>
                                        <input id="producto-nombre" type="text" class="form-control reset">
                                        <input id="producto-nombre-id" type="hidden" class="form-control reset">
                                        <input id="producto-tipoproducto" type="hidden" class="form-control reset">
                                        <input id="producto-idproducto" type="hidden" class="form-control reset">
                                        <input id="estratamiento" type="hidden" class="form-control reset">
                                        <input id="producto-marca" type="hidden" class="form-control reset">
                                        <input id="producto-codigobarras" type="hidden" class="form-control reset">
                                        <input id="producto-codigobarrasuniversal" type="hidden"
                                               class="form-control reset">
                                        <input id="producto-masivounico" type="hidden" class="form-control reset">
                                        <input id="categoria-producto" type="hidden" class="form-control reset">
                                        {% for producto in productos %}
                                            {{ producto.isomittable }}
                                            {{ producto.categoria }}
                                        {% endfor %}
                                        <input id="producto-precioespecial" type="hidden" class="form-control reset">
                                        <input id="producto-preciosubdistribuidor" type="hidden"
                                               class="form-control reset">
                                        <input id="producto-preciodistribuidor" type="hidden"
                                               class="form-control reset">
                                        <input id="producto-cantidad-max" type="hidden" class="form-control reset">

                                        <input id="" type="hidden" class="form-control reset">
                                    </div>
                                    <div id="msj-error-armazon-codigo" class="text-danger">
                                    </div>
                                </div>
                                <div class="d-none col-6">
                                    <div class="form-group">
                                        <label class="titulotext">Servicios / Tratamientos</label>
                                        <input id="tratamiento" type="text" class="form-control reset">
                                        <input id="tratamiento-id" type="hidden" class="form-control reset">
                                        <input id="tratamiento-precio" type="hidden" class="form-control reset">
                                    </div>
                                </div>
                            </div>
                            <!--Module to Products-->
                            <div class="row margin-buttom-new-sale products d-none">
                                <div class="col-12 border-title-new-sale">
                                    <h4 class="div-title">Productos Seleccionados</h4>
                                </div>
                                <div class="col-12">
                                    <div class="table-responsive">
                                        <table class="table table-borderless">
                                            <thead>
                                            <tr class="bg-info text-black">
                                                <th class="text-center">Producto</th>
                                                <th class="text-center">Precio</th>
                                                <th class="text-center">Descuento</th>
                                                <th class="text-center">Precio Final</th>
                                                <th class="text-center">Cantidad</th>
                                                <th class="text-center">Subtotal</th>
                                                <th></th>
                                            </tr>
                                            </thead>
                                            <tbody id="productos">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-12 col-md-6 col-xl-4 text">
                                    <div class="form-group ">
                                        <label>SUBTOTAL</label>
                                        <input disabled type="text" class="form-control reset text-center"
                                               id="subtotal">
                                    </div>
                                </div>
                                <div class="col-12 col-md-6 col-xl-4">
                                    <div class="form-group">
                                        {% set checkIva = app.user.sucursalIdsucursal.porcentajeiva %}
                                        {% set checkIva = checkIva == 0.0 ? 16.00 : checkIva %}
                                        <label>IVA ({{ checkIva }}%)</label>
                                        <input disabled type="text" class="form-control reset text-center" id="iva">
                                    </div>
                                </div>
                                <div class="col-12 col-md-6 col-xl-4">
                                    <div class="form-group">
                                        <label>TOTAL</label>
                                        <span id="textopreciofijotipoventa" class="d-none">El precio es fijo por el tipo
                                        de venta</span>
                                        <input disabled type="text" class="form-control reset text-center" id="total">
                                    </div>
                                </div>
                            </div>
                            <!--Module Payment of Products-->
                            <div class="row margin-buttom-new-sale payment d-none">
                                <div class="col-12 border-title-new-sale">
                                    <h4 class="div-title">Pago de Productos</h4>
                                </div>

                                <div class="col-12">
                                    <div class="row">
                                        {% if 1==1 %}
                                            <div class="col-12 col-md-6 col-xl-6 ">
                                                <div class="card" id="bloque-agregar-pagos">
                                                    <div class="card-body">
                                                        <label class="titulotext">Agregar Pago(s)</label>
                                                        <div class="row">
                                                            <div class="col-md-4 col-sm-4">
                                                                <input type="checkbox" class="btn-check"
                                                                       id="ventaCredito"
                                                                       autocomplete="off"/>
                                                                <label class="btn btn-outline-primary"
                                                                       for="ventaCredito">Venta
                                                                    a crédito</label>
                                                            </div>
                                                            <div class="col-md-4 col-sm-4 d-none"
                                                                 id="client-credit-container">
                                                                <label class="" for="client-credit">Crédito
                                                                    restante</label>
                                                                <input type="text" class="disabled" name="client-credit"
                                                                       id="client-credit" autocomplete="off" disabled>
                                                            </div>
                                                            <div class="col-md-4 col-sm-4 d-none"
                                                                 id="selectDiasCredito">
                                                                <label class="" for="diasCredito">Dias a crédito</label>
                                                                <select id="diasCredito" name="diasCredito">
                                                                    <option value="15">15</option>
                                                                    <option value="30">30</option>
                                                                    <option value="60">60</option>
                                                                    <option value="90">90</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="form-group  label-floating has-success">
                                                            <label class="" for="tipo-pago-anticipo">Tipo de
                                                                Pago</label>
                                                            <select name="" id="tipo-pago-anticipo" class="form-control"
                                                                    data-style="btn btn-link" onchange="cargarMeses()">
                                                                <option value="" data-payment-name=""></option>
                                                                {% for PaymentType in paymentTypes %}
                                                                    <option value="{{ PaymentType.idpaymenttype }}"
                                                                            data-payment-name="{{ PaymentType.name }}">{{ PaymentType.name }}</option>
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                        <div id="meses"></div>
                                                        <div class="form-group  label-floating has-success">
                                                            <label class="" for="anticipo">Cantidad (pesos)</label>
                                                            <input id="anticipo" type="text"
                                                                   class="form-control mb-2 mr-sm-2 valor-pesos"
                                                                   autocomplete="off">
                                                        </div>
                                                        <div class="col-md-12 text-center">
                                                            <button id="agregar-pago" type="button"
                                                                    class="btn pt-4 fix-payment-btn"
                                                                    style="vertical-align:middle;background:#fff"
                                                                    onclick="agregarPago();return false;">
                                                                <img src="{{ asset('img/icon-add-pay.jpg') }}" alt=""
                                                                     style="width: 50px;">
                                                            </button>
                                                        </div>

                                                        <div class="form-group">
                                                            <label class="" for="anticipo">Limite Convenio</label>
                                                            <input id="total-limit" type="text"
                                                                   class="form-control text-center" readonly>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        {% endif %}
                                        <div class="col-12 col-md-6 col-xl-6">
                                            <div class="card">
                                                <div class="card-body">
                                                    <label class="titulotext">Total Pagado</label>
                                                    <div class="form-group">
                                                        <input id="anticipo-total-pagado" type="text"
                                                               class="form-control text-center" readonly>
                                                    </div>
                                                    <table class="table">
                                                        <thead>
                                                        <tr>
                                                            <th>Cantidad</th>
                                                            <th>Método</th>
                                                            <th></th>
                                                        </tr>
                                                        </thead>
                                                        <tbody id="tabla-body-pagos">
                                                        </tbody>
                                                    </table>
                                                    <!--<div class="row justify-content-md-center">
                          <div class="col-md-10">
                            <table class="table">
                              <thead>
                                <tr class="text-start">
                                  <th class="text-start" scope="col">#</th>
                                  <th class="text-start" scope="col">Apellido paterno</th>
                                  <th class="text-start" scope="col">Apellido materno</th>
                                  <th class="text-start" scope="col">Nombre</th>
                                  <th class="text-start" scope="col">Correo</th>
                                  <th class="text-start" scope="col">Teléfono</th>
                                  <th class="text-start" scope="col">Número de empleado</th>
                                  <th></th>
                                </tr>
                              </thead>
                              <tbody id = "tableBodyBuscarCliente">
                              </tbody>
                            </table>
                          </div>
                        </div>-->
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 col-md-6 col-xl-6 margin-buttom-new-sale separation-top-movile-new-sale">
                                            <div class="card">
                                                <div class="card-body">
                                                    <label class="titulotext">Restan</label>
                                                    <div class="form-group">
                                                        <input id="anticipo-restan" type="text"
                                                               class="form-control text-center" readonly>
                                                    </div>
                                                    <form class="form-inline">
                                                        <div class="form-group mb-2">
                                                            <label for="staticEmail2" class="sr-only">Cupón de
                                                                Descuento</label>
                                                        </div>
                                                        <div class="form-group mx-sm-3 mb-2 text-center">
                                                            <label for="inputPassword2" class="text-primary">Cupón de
                                                                Descuento</label>
                                                            <input type="text" class="form-control" id="cupon-descuento"
                                                                   placeholder="Cupón de Descuento">
                                                            <button id="buscar-cupon" type="button"
                                                                    class="btn btn-success mb-2 agregar-cupon mt-1"
                                                                    onclick="buscarCupon()">Aplicar
                                                            </button>
                                                            <button type="button"
                                                                    class="btn btn-warning mb-2 quitar-cupon d-none mt-1"
                                                                    onclick="quitarCupon()">Quitar
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--Module Extra information-->
                                {% include 'ventas/modules/extra_info_module.html.twig' with {
                                    'usuarios': usuarios,
                                    'sellReferences': sellReferences,
                                } %}

                                <div class="row d-none" style="display:none">
                                    <div class="col-md-2 col-sm-2"></div>
                                    <div class="col-md-4 col-sm-4">
                                        <div class="form-group">
                                            <label class="">Descripción</label>
                                            <input id="producto-descripcion" disabled type="text" class="form-control">
                                        </div>
                                    </div>
                                    <div class="col-md-2 col-sm-2">
                                        <div class="form-group">
                                            <label class="">Precio</label>
                                            <input id="producto-precio" disabled type="text" class="form-control"
                                                   onkeydown="formatoPesos(this,2);calcularTotal()"
                                                   onkeyup="formatoPesos(this,2);calcularTotal()">
                                        </div>
                                    </div>
                                    <div class="col-md-2 col-sm-2">
                                        <div class="form-group">
                                            <label class="">Cantidad</label>
                                            <input id="producto-cantidad" type="text" class="form-control" value="1"
                                                   onkeydown="validarNumero(this);calcularTotal()"
                                                   onkeyup="validarNumero(this);calcularTotal()">
                                        </div>
                                    </div>
                                    <div class="col-md-2 col-sm-2">
                                        <div class="form-group">
                                            <label class="">Total</label>
                                            <input id="producto-total" type="text" class="form-control" disabled
                                                   onkeydown="validarNumero(this)" onkeyup="validarNumero(this)">
                                        </div>
                                    </div>
                                    <div class="col-md-2 col-sm-2 text-center">
                                        <button class="btn btn-success" type="submit"
                                                onclick="agregarProducto();return false;">Agregar
                                        </button>
                                    </div>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <!--Controllers about the modules secondarys-->
                            <div class="container-button margin-buttom-new-sale">
                                <a id="button-left-new-sale" href="#" class="button-arrow-new-sale">
                                    <i class="fa-solid fa-arrow-left-long fa-2xl" style="color: #adadad;"></i>
                                    ATRAS
                                </a>
                                <a id="button-right-new-sale" href="#" class="button-arrow-new-sale ms-5">
                                    <i class="fa-solid fa-arrow-right-long fa-2xl" style="color: #000000;"></i>
                                    SIGUIENTE
                                </a>
                            </div>
                        </div>
                    </div><!--findenueva venta-->

                </div>
            </div>
        </div><!-- fin decontainer fluid-->
        <div class="row " style="">
            <div class="" id="ticket-generado" style="width:325px;position:absolute;right:-800px">
            </div>
        </div><!-- fin de row ticket generado-->
        <div class="card botones-finales d-none">
            <div class="card-body">
                <div class="row justify-content-center mb-4">
                    <div class="col-md-8">
                        <div class="ticket-buttons-container d-flex flex-wrap justify-content-center gap-4">
                            <!-- Ticket en PDF -->
                            <div class="ticket-button-wrapper">
                                <button id="btn-pdf" class="btn btn-lg ticket-button"
                                        data-bs-toggle="mod" data-bs-target="#modal-visor-documentos"
                                        onclick="abrirVisor();return false;">
                                    <i class="fas fa-file-pdf fa-2x mb-2"></i>
                                    <span>Ticket en PDF</span>
                                </button>
                            </div>

                            <!-- Ticket Especial en PDF -->
                            <div id="ticket-especial" class="ticket-button-wrapper">
                                <button id="btn-pdf-especial" class="btn btn-lg ticket-button ticket-button-especial"
                                        data-bs-toggle="mod" data-bs-target="#modal-visor-documentos"
                                        onclick="abrirVisor('especial');return false;">
                                    <i class="fas fa-file-contract fa-2x mb-2"></i>
                                    <span>Ticket Especial</span>
                                </button>
                            </div>

                            <!-- Ticket de Graduación -->
                            <div id="ticket-graduacion" class="ticket-button-wrapper">
                                <button id="btn-pdf-graduacion" class="btn btn-lg ticket-button ticket-button-graduacion"
                                        onclick="abrirVisor('graduacion');return false;">
                                    <i class="fas fa-glasses fa-2x mb-2"></i>
                                    <span>Ticket de Graduación</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row justify-content-center mt-4">
                    <div class="col-md-4">
                        <a id="btn-nueva-venta" href="{{ path('nueva-venta') }}" type="button"
                           class="btn btn-warning btn-lg btn-block d-flex align-items-center justify-content-center">
                            <i class="fas fa-plus-circle me-2"></i> Nueva Venta
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--botonesfinales-->
    <div class="mod " id="modal-visor-documentos" tabindex="-1" aria-labelledby="modal-visor-documentosLabel"
         aria-hidden="true">
        <div class="mod-dialog mod-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ticket de venta</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="modal-visor-body">
                </div>
                <div class="modal-footer text-center">
                    <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="mod">
                        <i class="fas fa-times me-2"></i>Cerrar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="mod " id="modal-visor-graduacion" tabindex="-1" aria-labelledby="modal-buscar-cotizacionlLabel"
         aria-hidden="true">
        <div class="mod-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ticket Graduación</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Close"></button>
                </div>

                <div class="modal-body" id="modal-visor-body-graduacion">
                </div>
                <div class="modal-footer text-center">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <div class="mod " id="modal-select-beneficiaries" tabindex="-1" aria-labelledby="modal-select-beneficiariesLabel"
         aria-hidden="true">
        <div class="mod-dialog">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header bg-primary">
                    <h5 class="modal-title" id="exampleModalLabel">Seleccionar beneficiarios</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Close"></button>
                </div>

                <div class="modal-body">

                    <button id="boton-agregar-beneficiario" onclick="obtenerFormularioAgregarBeneficiario()"
                            type="button" class="btn btn-success mt-3 d-none" data-bs-toggle="mod"
                            data-bs-target="#modalAgregarBeneficiarios">
                        <i class="fa-solid fa-plus"></i>
                    </button>

                    <div id="select-beneficiaries-container"></div>

                </div>
                <div class="modal-footer text-center">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <div class="mod " id="modalAgregarBeneficiarios" tabindex="-1" role="dialog"
         aria-labelledby="modal-agregar-beneficiarioLabel" aria-hidden="true">
        <div class="mod-dialog mod-lg" role="document">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header bg-primary">
                    <h5 class="modal-title" id="beneficiary-modal-title">Agregar beneficiario</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Close"></button>
                </div>

                <div class="modal-body" id="modal-visor-body">

                    <div id="formularioBeneficiario"></div>

                </div>
                <div class="modal-footer text-center">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para agregar graduación -->
    <div class="mod" id="modal-graduacion" tabindex="-1" aria-labelledby="modal-graduacion-label" aria-hidden="true">
        <div class="mod-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-graduacion-label">Agregar Graduación</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="producto-graduacion-id" value="">
                    <input type="hidden" id="producto-graduacion-idRow" value="">
                        <!-- Select de Etapas -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="stage" class="form-label fw-bold">
                                <i class="fas fa-tasks "></i>Etapa
                            </label>
                            <select class="form-select graduation-select" id="stage" name="stage">
                                <option value="9" selected>Pendiente</option>
                                <option value="10">Entregado</option>
                            </select>
                            <div class="form-text">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Selecciona la etapa actual de la orden de laboratorio
                                </small>
                            </div>
                        </div>
                    </div>


                    <table class="table table-bordered text-center">
                        <tr>
                            <th style="background-color: #8a939a; color: white;"></th>
                            <th style="background-color: #8a939a; color: white;">Esf</th>
                            <th style="background-color: #8a939a; color: white;">Cil</th>
                            <th style="background-color: #8a939a; color: white;">Eje</th>
                            <th style="background-color: #8a939a; color: white;">ADD</th>
                        </tr>
                        <tr>
                            <th style="background-color: #8a939a; color: white;">OJO DERECHO</th>
                            <td>
                                <select class="form-select graduation-select" id="od-esfera">
                                    <option value="" class="option-graduacion">Seleccionar</option>
                                    <!-- Valores negativos -->
                                    {% for i in range(-20, -0.25, +0.25) %}
                                        <option value="{{ i }}" class="option-graduacion">{{ i }}</option>
                                    {% endfor %}
                                    <option value="0" class="option-graduacion">0.00</option>
                                    <!-- Valores positivos -->
                                    {% for i in range(0.25, +20, +0.25) %}
                                        {% set value = '+' ~ i|number_format(2, '.', '') %}
                                        <option value="{{ value }}" class="option-graduacion">{{ value }}</option>
                                    {% endfor %}
                                </select>
                            </td>
                            <td>
                                <select class="form-select graduation-select" id="od-cilindro">
                                    <option value="" class="option-graduacion">Seleccionar</option>
                                    <!-- Valores negativos -->
                                    {% for i in range(-9, -0.25, 0.25) %}
                                        <option value="{{ i }}" class="option-graduacion">{{ i }}</option>
                                    {% endfor %}
                                    <option value="0" class="option-graduacion">0.00</option>
                                    <!-- Valores positivos -->
                                    {% for i in range(0.25, 9, 0.25) %}
                                        {% set value = '+' ~ i|number_format(2, '.', '') %}
                                        <option value="{{ value }}" class="option-graduacion">{{ value }}</option>
                                    {% endfor %}
                                </select>
                            </td>
                            <td>
                                <select class="form-select graduation-select" id="od-eje">
                                    <option value="" class="option-graduacion">Seleccionar</option>
                                    {% for i in range(0, 180, 5) %}
                                        <option value="{{ i }}" class="option-graduacion">{{ i }}°</option>
                                    {% endfor %}
                                </select>
                            </td>
                            <td>
                                <select class="form-select graduation-select" id="od-adicion">
                                    <option value="" class="option-graduacion">Seleccionar</option>
                                    <option value="0" class="option-graduacion">0.00</option>
                                    {% for i in range(0.25, 4, 0.25) %}
                                        {% set value = '+' ~ i|number_format(2, '.', '') %}
                                        <option value="{{ value }}" class="option-graduacion">{{ value }}</option>
                                    {% endfor %}
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th style="background-color: #8a939a; color: white;">OJO IZQUIERDO</th>
                            <td>
                                <select class="form-select graduation-select" id="oi-esfera">
                                    <option value="" class="option-graduacion">Seleccionar</option>
                                    <!-- Valores negativos -->
                                    {% for i in range(-20, -0.25, 0.25) %}
                                        <option value="{{ i }}" class="option-graduacion">{{ i }}</option>
                                    {% endfor %}
                                    <option value="0" class="option-graduacion">0.00</option>
                                    <!-- Valores positivos -->
                                    {% for i in range(0.25, 20, 0.25) %}
                                        {% set value = '+' ~ i|number_format(2, '.', '') %}
                                        <option value="{{ value }}" class="option-graduacion">{{ value }}</option>
                                    {% endfor %}
                                </select>
                            </td>
                            <td>
                                <select class="form-select graduation-select" id="oi-cilindro">
                                    <option value="" class="option-graduacion">Seleccionar</option>
                                    <!-- Valores negativos -->
                                    {% for i in range(-9, -0.25, 0.25) %}
                                        <option value="{{ i }}" class="option-graduacion">{{ i }}</option>
                                    {% endfor %}
                                    <option value="0" class="option-graduacion">0.00</option>
                                    <!-- Valores positivos -->
                                    {% for i in range(0.25, 9, 0.25) %}
                                        {% set value = '+' ~ i|number_format(2, '.', '') %}
                                        <option value="{{ value }}" class="option-graduacion">{{ value }}</option>
                                    {% endfor %}
                                </select>
                            </td>
                            <td>
                                <select class="form-select graduation-select" id="oi-eje">
                                    <option value="" class="option-graduacion">Seleccionar</option>
                                    {% for i in range(0, 180, 5) %}
                                        <option value="{{ i }}" class="option-graduacion">{{ i }}°</option>
                                    {% endfor %}
                                </select>
                            </td>
                            <td>
                                <select class="form-select graduation-select" id="oi-adicion">
                                    <option value="" class="option-graduacion">Seleccionar</option>
                                    <option value="0" class="option-graduacion">0.00</option>
                                    {% for i in range(0.25, 4, 0.25) %}
                                        {% set value = '+' ~ i|number_format(2, '.', '') %}
                                        <option value="{{ value }}" class="option-graduacion">{{ value }}</option>
                                    {% endfor %}
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th style="background-color: #8a939a; color: white;">DIP</th>
                            <td>
                                <textarea class="form-control graduation-input"
                                          id="distancia-pupilar"
                                          placeholder="Ej. 12/12, 62mm, 30/32"
                                          rows="1">

                                </textarea>
                            </td>
                            <th style="background-color: #8a939a; color: white;">AO</th>
                            <td>
                                <select class="form-select graduation-select" id="altura">
                                    <option value="" class="option-graduacion">Seleccionar</option>
                                    {% for i in range(10, 40) %}
                                        <option value="{{ i }}" class="option-graduacion">{{ i }} mm</option>
                                    {% endfor %}
                                </select>
                            </td>
                            <th style="background-color: #8a939a; color: white;">ACO</th>
                            <td>
                                <select class="form-select graduation-select" id="_aco">
                                    <option value="" class="option-graduacion">Seleccionar</option>
                                    {% for i in range(10, 40) %}
                                        <option value="{{ i }}" class="option-graduacion">{{ i }} mm</option>
                                    {% endfor %}
                                </select>
                            </td>
                        </tr>
                    </table>

                
                    <div class="row">
                        <div class="col-md-6">
                            <label for="diagnostico" class="form-label fw-bold">Diagnóstico</label>
                            <textarea class="form-control" id="diagnostico" rows="3"
                                      placeholder="Escribe el diagnóstico aquí..."></textarea>
                        </div>
                        <div class="col-md-6">
                            <label for="notas" class="form-label fw-bold">Notas</label>
                            <textarea class="form-control" id="notas-graduacion" rows="3"
                                      placeholder="Agrega notas aquí..."></textarea>
                        </div>
                        <div class="col-md-6">
                            <label for="notas" class="form-label fw-bold">Nombre del Optometrista:</label>
                            <textarea class="form-control" id="nombre-optometrista" placeholder=""></textarea>

                        </div>
                    </div>

                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>
                    <button type="button" class="btn btn-primary" onclick="guardarGraduacion()">Guardar Graduación
                    </button>
                </div>
                
            </div>
        </div>
    </div>

    <!--Modal Nuevo Cliente-->
    <div class="mod" id="modalNuevoCliente" tabindex="-1" aria-labelledby="modalNuevoClienteLabel" aria-hidden="true">
        <div class="mod-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-4" id="modalNuevoClienteLabel">
                        <i class="fas fa-user-plus me-2"></i>NUEVO CLIENTE
                    </h1>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="mod"
                            aria-label="Cerrar"></button>
                </div>
                <div class="modal-body p-4">
                    <div id="create-user-form-container">
                        <!-- El contenido del formulario se cargará dinámicamente -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--Modal Buscar Cliente-->
    <div class="mod" id="modalBuscarCliente" tabindex="-1" aria-labelledby="modalBuscarClienteLabel" aria-hidden="true">
        <div class="mod-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-4" id="modalBuscarClienteLabel">
                        <i class="fas fa-search me-2"></i>BUSCAR CLIENTE
                    </h1>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="mod"
                            aria-label="Cerrar"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row g-4">
                        <div class="col-12 col-md-4">
                            <div class="form-floating">
                                <input id="BuscarNombre" class="form-control" type="text" placeholder="Nombre"/>
                                <label for="BuscarNombre">Nombre(s)</label>
                            </div>
                        </div>
                        <div class="col-12 col-md-4">
                            <div class="form-floating">
                                <input id="BuscarApellidoP" class="form-control" type="text"
                                       placeholder="Apellido paterno"/>
                                <label for="BuscarApellidoP">Apellido paterno</label>
                            </div>
                        </div>
                        <div class="col-12 col-md-4">
                            <div class="form-floating">
                                <input id="BuscarApellidoM" class="form-control" type="text"
                                       placeholder="Apellido materno"/>
                                <label for="BuscarApellidoM">Apellido materno</label>
                            </div>
                        </div>
                        <div class="col-12 col-md-4">
                            <div class="form-floating">
                                <input id="BuscarEmail" class="form-control" type="email"
                                       placeholder="Correo electrónico"/>
                                <label for="BuscarEmail">Correo electrónico</label>
                            </div>
                        </div>
                        <div class="col-12 col-md-4">
                            <div class="form-floating">
                                <input id="BuscarTelefono" class="form-control" type="tel" placeholder="Teléfono"/>
                                <label for="BuscarTelefono">Teléfono</label>
                            </div>
                        </div>
                        <div class="col-12 col-md-4">
                            <div class="form-floating">
                                <input id="BuscarNumeroEmpleado" class="form-control" type="text"
                                       placeholder="Número de empleado"/>
                                <label for="BuscarNumeroEmpleado">Número de empleado</label>
                            </div>
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="button" class="btn btn-primary px-5 py-3" onclick="BuscarCliente()">
                                <i class="fas fa-search me-2"></i>Buscar
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive mt-4">
                        <table class="table table-hover table-striped align-middle">
                            <thead class="table-light">
                            <tr>
                                <th class="text-center">#</th>
                                <th class="text-center">Apellido paterno</th>
                                <th class="text-center">Apellido materno</th>
                                <th class="text-center">Nombre</th>
                                <th class="text-center">Correo</th>
                                <th class="text-center">Teléfono</th>
                                <th class="text-center">Número de empleado</th>
                                <th class="text-center">Acción</th>
                            </tr>
                            </thead>
                            <tbody id="tableBodyBuscarCliente">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% endblock %}



{% block javascripts %}

    {{ parent() }}

    <script src="{{ asset('js/jquery.formatCurrency-1.4.0.pack.js') }}"></script>
    <script src="{{ asset('lib/jQuery-Autocomplete-master/dist/jquery.autocomplete.min.js') }}"></script>
    <script src="{{ asset('js/format-input-field.js') }}"></script>
    <script src="{{ asset('js/iniciar-formato-campos.js') }}"></script>
    <script src="{{ asset('js/nueva-venta.js') }}?v={{ version }}"></script>
    <script src="{{ asset('lib/dataTables/dataTables.min.js') }}"></script>
    <link rel="stylesheet" href="{{ asset('lib/dataTables/dataTables.min.css') }}">
    <script type="text/javascript">

        var ivaValue;
        const checkProductos = new Set();
        const beneficiaries = new Map();
        const servicesQuantities = new Map();
        var storablesQuantities = new Map();
        const dbServicesQuantities = new Map();
        var dbStorablesQuantities = new Map();
        var curCupon = {};
        var savedVentaCupon = null;
        var numberUploadedFiles = 0;
        var curMSI = false;
        var curProducts = 0;
        let graduacionesPendientes = {};

        // --- MODIFICACIÓN: Siempre inicializar graduacionesPendientes al cargar la venta/cotización ---
        function inicializarGraduacionesPendientes() {
            graduacionesPendientes = {};
            {% if graduacionesData is defined and graduacionesData.graduaciones is defined %}
            // Inicializar graduacionesPendientes con los datos de graduación de la venta/cotización
            {% for graduacion in graduacionesData.graduaciones %}
            graduacionesPendientes[{{ graduacion.producto_id }}] = {
                odEsfera: {{ graduacion.odEsfera|json_encode|raw }},
                odCilindro: {{ graduacion.odCilindro|json_encode|raw }},
                odEje: {{ graduacion.odEje|json_encode|raw }},
                odAdicion: {{ graduacion.odAdicion|json_encode|raw }},
                oiEsfera: {{ graduacion.oiEsfera|json_encode|raw }},
                oiCilindro: {{ graduacion.oiCilindro|json_encode|raw }},
                oiEje: {{ graduacion.oiEje|json_encode|raw }},
                oiAdicion: {{ graduacion.oiAdicion|json_encode|raw }},
                distanciaPupilar: {{ graduacion.distanciaPupilar|json_encode|raw }},
                altura: {{ graduacion.altura|json_encode|raw }},
                _aco: {{ graduacion._aco|json_encode|raw }},
                notasGraduacion: {{ graduacion.notes|default('')|json_encode|raw }},
                diagnostico: {{ graduacion.diagnostico|default('')|json_encode|raw }}
            };
            {% endfor %}
            {% endif %}
        }

        // Llamar siempre al cargar la página
        inicializarGraduacionesPendientes();

        $(document).ready(function () {
            checkIva = parseFloat($("#iva-value").val())
            checkIva = (checkIva == 0.0) ? 16 : checkIva
            ivaValue = checkIva / 100;

            // Marcar visualmente los productos que ya tienen graduación
            Object.keys(graduacionesPendientes).forEach(function(key) {
                // Si la clave comienza con "producto-", es un idRow
                if (key.startsWith("producto-")) {
                    $("#" + key + " .fa-glasses").addClass("text-success");
                } else {
                    // Si no, asumimos que es un idProducto (compatibilidad con código anterior)
                    $("#producto-" + key + " .fa-glasses").addClass("text-success");
                }
            });

            // Mostrar el botón de ticket de graduación si hay un folio de venta y graduaciones
            if ($("#folio").val() && Object.keys(graduacionesPendientes).length > 0) {
                $("#ticket-graduacion").removeClass("d-none");
            }

            $(".option-1-new-sale").click(function () {
                $(".container-information-new-sale").toggle();
            });
            /*Controllers Buttoms*/
            $("#button-right-new-sale").click(function () {
                $(".products").removeClass("d-none");
                $(".payment").removeClass("d-none");
                $(".extrainformation").removeClass("d-none");
                $(".addproducts").removeClass("d-none");
                $(".sale").addClass("d-none");
                $(".userdate").addClass("d-none");
                $(".fa-arrow-left-long").css("color", "#000000");
                $(".fa-arrow-right-long").css("color", "#adadad");
                $(".fa-tag").css("color", "#cd1d1d");
                $(".text-option-new-sale a").css("color", "#000000");
                $("#venta").attr("href", "{{ path('nueva-venta') }}");

            });

            $("#button-left-new-sale").click(function () {
                const color = $(".fa-arrow-left-long").css("color");
                if (color.toLowerCase() === "rgb(0, 0, 0)" || color.toLowerCase() === "black") {
                    $(".products").addClass("d-none");
                    $(".payment").addClass("d-none");
                    $(".extrainformation").addClass("d-none");
                    $(".addproducts").addClass("d-none");
                    $(".sale").removeClass("d-none");
                    $(".userdate").removeClass("d-none");
                    $(".fa-arrow-left-long").css("color", "#adadad");
                    $(".fa-arrow-right-long").css("color", "#000000");
                    $(".fa-tag").css("color", "#adadad");
                    $(".text-option-new-sale a").css("color", "#adadad");
                    $("#venta").removeAttr("href");
                }
            });

            /*Event of menus*/
            $(".fa-bars").click(function () {
                $(".fa-xmark").removeClass("d-none");
                $(".fa-bars").addClass("d-none");
                $("#options-bar").removeClass("stash");
            });
            $(".fa-xmark").click(function () {
                $(".fa-xmark").addClass("d-none");
                $(".fa-bars").removeClass("d-none");
                $("#options-bar").addClass("stash");
            });

            // Custom modal handling
            // Make sure modals are hidden by default
            $(".mod").hide();

            // Handle modal toggling
            $("[data-bs-toggle='mod']").click(function (e) {
                e.preventDefault();
                var target = $(this).data("bs-target");
                $(target).addClass("show").show();
            });

            // Handle modal dismissal
            $("[data-bs-dismiss='mod']").click(function () {
                var $modal = $(this).closest('.mod');
                $modal.removeClass("show").hide();
                $("body").removeClass("modal-open");
                $(".modal-backdrop").remove();
            });

            // Close modal when clicking outside
            $(document).on("click", ".mod.show", function (e) {
                if ($(e.target).hasClass("mod")) {
                    $(this).removeClass("show").hide();
                    $("body").removeClass("modal-open");
                    $(".modal-backdrop").remove();
                }
            });

            // Asegurarse de que el backdrop se elimine al presionar ESC
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape' && $('.mod.show').length > 0) {
                    $('.mod.show').removeClass("show").hide();
                    $("body").removeClass("modal-open");
                    $(".modal-backdrop").remove();
                }
            });
        });

        // Esta función verifica si cada campo está vacío y, de ser así, lo habilita
        function verificarYHabilitarCampos() {
            $('.disabled-escritura').each(function () {
                if ($(this).val().trim() === "") {
                    $(this).prop('disabled', false);
                } else {
                    $(this).prop('disabled', true);
                }
            });
        }

        // Función para establecer automáticamente stage pendiente en productos con graduación
        function establecerStagePendienteAutomatico(idProducto, idRow, forzarSoloSiEsNuevo = true) {
            console.log("=== ESTABLECER STAGE PENDIENTE AUTOMÁTICO ===");
            console.log("ID Producto:", idProducto);
            console.log("ID Row:", idRow);
            console.log("Forzar solo si es nuevo:", forzarSoloSiEsNuevo);

            // Verificar si ya tiene graduación en memoria
            if (graduacionesPendientes[idProducto] || graduacionesPendientes[idRow]) {
                console.log("El producto ya tiene graduación en memoria, aplicando color según stage existente");
                const graduacionExistente = graduacionesPendientes[idProducto] || graduacionesPendientes[idRow];
                actualizarColorProductoPorStage(idProducto, graduacionExistente.stage || "9", idRow);
                return;
            }

            // Si forzarSoloSiEsNuevo es true, solo crear graduación para productos realmente nuevos
            if (forzarSoloSiEsNuevo) {
                console.log("Verificando si el producto es realmente nuevo (sin graduación en BD)");
                // Para productos existentes, no crear graduación automática
                // La graduación se cargará desde la BD si existe
                return;
            }

            // Crear graduación básica con stage pendiente SOLO para productos nuevos
            const graduacionPendiente = {
                odEsfera: "",
                odCilindro: "",
                odEje: "",
                odAdicion: "",
                oiEsfera: "",
                oiCilindro: "",
                oiEje: "",
                oiAdicion: "",
                distanciaPupilar: "",
                altura: "",
                _aco: "",
                notasGraduacion: "",
                diagnostico: "",
                stage: "9" // Pendiente de graduar
            };

            // Guardar en memoria
            graduacionesPendientes[idRow] = graduacionPendiente;
            graduacionesPendientes[idProducto] = graduacionPendiente;

            // Aplicar color amarillo inmediatamente
            actualizarColorProductoPorStage(idProducto, "9", idRow);

            console.log("✅ Stage pendiente establecido automáticamente para producto NUEVO", idProducto);
        }

        $(document).on("click", ".btn-graduacion", function () {
            const idProducto = $(this).data("idproducto");
            const idRow = $(this).data("idrow");
            agregarGraduacion(idProducto,idRow);
        });

        function cargarGraduacionDesdeDB(idStockVenta, callback) {
            console.log("cargarGraduacionDesdeDB "+idStockVenta);
            // URL para obtener la graduación
            const url = `/venta/obtener-graduacion/${idStockVenta}`;

            $.ajax({
                url: url,
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.exito && response.graduacion) {
                        console.log("Graduación cargada desde la base de datos:", response.graduacion);
                        callback(response.graduacion);
                    } else {
                        console.log("No se encontró graduación en la base de datos");
                        callback(null);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error al cargar graduación:", error);
                    callback(null);
                }
            });
        }

        // Función para actualizar el color del producto según el stage de graduación
        function actualizarColorProductoPorStage(idProducto, stage, idRow = null) {
            console.log("=== ACTUALIZAR COLOR PRODUCTO ===");
            console.log("ID Producto:", idProducto);
            console.log("ID Row:", idRow);
            console.log("Stage:", stage);

            // Intentar encontrar el elemento usando idRow primero, luego idProducto
            let productoElement;
            if (idRow) {
                productoElement = $("#" + idRow);
                console.log("Buscando por idRow:", idRow, "- Encontrado:", productoElement.length > 0);
            }

            if (!productoElement || productoElement.length === 0) {
                productoElement = $("#producto-" + idProducto);
                console.log("Buscando por idProducto:", "#producto-" + idProducto, "- Encontrado:", productoElement.length > 0);
            }

            if (productoElement.length === 0) {
                console.error("❌ No se encontró el elemento. Intentado:");
                console.error("  - #" + (idRow || "null"));
                console.error("  - #producto-" + idProducto);
                return;
            }

            const iconoGraduacion = productoElement.find(".fa-glasses");
            console.log("Icono graduación encontrado:", iconoGraduacion.length > 0);

            // Remover clases anteriores
            productoElement.removeClass("producto-stage-pendiente producto-stage-terminado");
            iconoGraduacion.removeClass("stage-pendiente stage-terminado");

            // Agregar clase según el stage
            if (stage === '9') {
                // Pendiente de graduar - Amarillo
                productoElement.addClass("producto-stage-pendiente");
                iconoGraduacion.addClass("stage-pendiente");
                console.log("✅ Producto " + idProducto + " marcado como PENDIENTE (amarillo)");
                console.log("Clases del producto:", productoElement.attr('class'));
            } else if (stage === '10') {
                // Terminado - Verde
                productoElement.addClass("producto-stage-terminado");
                iconoGraduacion.addClass("stage-terminado");
                console.log("✅ Producto " + idProducto + " marcado como TERMINADO (verde)");
                console.log("Clases del producto:", productoElement.attr('class'));
            } else {
                console.log("⚪ Stage no reconocido:", stage);
            }
        }

        // Función para actualizar colores de todos los productos con graduación
        function actualizarColoresProductos() {
            console.log("Actualizando colores de productos...");
            for (const [key, graduacion] of Object.entries(graduacionesPendientes)) {
                if (graduacion && graduacion.stage) {
                    if (key.startsWith('producto-')) {
                        // Si la key es "producto-123", usar directamente como idRow
                        const idRow = key;
                        const idProducto = key.replace('producto-', '');
                        actualizarColorProductoPorStage(idProducto, graduacion.stage, idRow);
                    } else {
                        // Si la key es solo el idProducto, intentar encontrar el elemento
                        actualizarColorProductoPorStage(key, graduacion.stage);
                    }
                }
            }
        }

        function agregarGraduacion(idProducto,idRow) {
            console.log("agregarGraduacion "+idProducto);
            $("#producto-graduacion-id").val(idProducto);
            $("#producto-graduacion-idRow").val(idRow);

            console.log(graduacionesPendientes);
            // Primero verificamos si ya tenemos la graduación en memoria
            if (graduacionesPendientes[idProducto] || graduacionesPendientes[idRow]) {
                const g = graduacionesPendientes[idProducto] || graduacionesPendientes[idRow];
                $("#od-esfera").val(g.odEsfera);
                $("#od-cilindro").val(g.odCilindro);
                $("#od-eje").val(g.odEje);
                $("#oi-esfera").val(g.oiEsfera);
                $("#oi-cilindro").val(g.oiCilindro);
                $("#oi-eje").val(g.oiEje);
                $("#distancia-pupilar").val(g.distanciaPupilar);
                $("#altura").val(g.altura);
                $("#_aco").val(g._aco);
                $("#od-adicion").val(g.odAdicion);
                $("#oi-adicion").val(g.oiAdicion);
                $("#notas-graduacion").val(g.notasGraduacion);
                $("#diagnostico").val(g.diagnostico);
                $("#stage").val(g.stage || "9"); // Establecer el stage, por defecto 9

                // Mostrar el modal
                const modalGraduacion = new bootstrap.Modal(document.getElementById('modal-graduacion'));
                modalGraduacion.show();
            } else {
                // Si no está en memoria, intentamos cargarla desde la base de datos
                // Buscamos el idstockventa del producto
                const idStockVenta = $(`#producto-${idProducto}`).find(".producto-seleccionado").data("idstockventa");
                console.log("entra a obtener desde la base de datos "+ idStockVenta);
                if (idStockVenta) {
                    // Mostramos un indicador de carga
                    Swal.fire({
                        title: 'Cargando...',
                        text: 'Obteniendo datos de graduación',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    cargarGraduacionDesdeDB(idStockVenta, function(graduacion) {
                        Swal.close();

                        if (graduacion) {
                            // Guardamos la graduación en memoria
                            graduacionesPendientes[idRow] = {
                                odEsfera: graduacion.odEsfera || "",
                                odCilindro: graduacion.odCilindro || "",
                                odEje: graduacion.odEje || "",
                                odAdicion: graduacion.odAdicion || "",
                                oiEsfera: graduacion.oiEsfera || "",
                                oiCilindro: graduacion.oiCilindro || "",
                                oiEje: graduacion.oiEje || "",
                                oiAdicion: graduacion.oiAdicion || "",
                                distanciaPupilar: graduacion.distanciaPupilar || "",
                                altura: graduacion.altura || "",
                                _aco: graduacion._aco || "",
                                notasGraduacion: graduacion.notas || "",
                                diagnostico: graduacion.diagnostico || "",
                                stage: graduacion.stage || "9" // Agregar el stage desde la BD
                            };

                            // Rellenamos los campos
                            $("#od-esfera").val(graduacion.odEsfera || "");
                            $("#od-cilindro").val(graduacion.odCilindro || "");
                            $("#od-eje").val(graduacion.odEje || "");
                            $("#od-adicion").val(graduacion.odAdicion || "");
                            $("#oi-esfera").val(graduacion.oiEsfera || "");
                            $("#oi-cilindro").val(graduacion.oiCilindro || "");
                            $("#oi-eje").val(graduacion.oiEje || "");
                            $("#oi-adicion").val(graduacion.oiAdicion || "");
                            $("#distancia-pupilar").val(graduacion.distanciaPupilar || "");
                            $("#altura").val(graduacion.altura || "");
                            $("#_aco").val(graduacion._aco || "");
                            $("#notas-graduacion").val(graduacion.notas || "");
                            $("#diagnostico").val(graduacion.diagnostico || "");
                            $("#stage").val(graduacion.stage || "9"); // Establecer el stage desde la BD

                            // Marcamos visualmente que el producto tiene graduación
                            $("#producto-" + idProducto + " .fa-glasses").addClass("text-success");

                            // Actualizar color del producto según el stage
                            actualizarColorProductoPorStage(idProducto, graduacion.stage || "9", idRow);

                            // Mostrar el botón de ticket de graduación si hay un folio de venta
                            if ($("#folio").val()) {
                                $("#ticket-graduacion").removeClass("d-none");
                            }
                        } else {
                            // Si no hay graduación en la base de datos, verificar si hay graduación temporal
                            if (graduacionesPendientes[idProducto] || graduacionesPendientes[idRow]) {
                                const graduacionTemporal = graduacionesPendientes[idProducto] || graduacionesPendientes[idRow];
                                $("#od-esfera").val(graduacionTemporal.odEsfera || "");
                                $("#od-cilindro").val(graduacionTemporal.odCilindro || "");
                                $("#od-eje").val(graduacionTemporal.odEje || "");
                                $("#od-adicion").val(graduacionTemporal.odAdicion || "");
                                $("#oi-esfera").val(graduacionTemporal.oiEsfera || "");
                                $("#oi-cilindro").val(graduacionTemporal.oiCilindro || "");
                                $("#oi-eje").val(graduacionTemporal.oiEje || "");
                                $("#oi-adicion").val(graduacionTemporal.oiAdicion || "");
                                $("#distancia-pupilar").val(graduacionTemporal.distanciaPupilar || "");
                                $("#altura").val(graduacionTemporal.altura || "");
                                $("#_aco").val(graduacionTemporal._aco || "");
                                $("#notas-graduacion").val(graduacionTemporal.notasGraduacion || "");
                                $("#diagnostico").val(graduacionTemporal.diagnostico || "");
                                $("#stage").val(graduacionTemporal.stage || "9"); // Establecer el stage temporal
                            } else {
                                // Solo limpiar si no hay graduación temporal
                                $("#od-esfera").val("");
                                $("#od-cilindro").val("");
                                $("#od-eje").val("");
                                $("#od-adicion").val("");
                                $("#oi-esfera").val("");
                                $("#oi-cilindro").val("");
                                $("#oi-eje").val("");
                                $("#oi-adicion").val("");
                                $("#distancia-pupilar").val("");
                                $("#altura").val("");
                                $("#_aco").val("");
                                $("#notas-graduacion").val("");
                                $("#diagnostico").val("");
                                $("#stage").val("9"); // Establecer stage por defecto
                            }
                        }

                        // Mostrar el modal
                        const modalGraduacion = new bootstrap.Modal(document.getElementById('modal-graduacion'));
                        modalGraduacion.show();
                    });
                } else {
                    // Si no hay idstockventa, verificar si hay graduación temporal
                    if (graduacionesPendientes[idProducto] || graduacionesPendientes[idRow]) {
                        const graduacionTemporal = graduacionesPendientes[idProducto] || graduacionesPendientes[idRow];
                        $("#od-esfera").val(graduacionTemporal.odEsfera || "");
                        $("#od-cilindro").val(graduacionTemporal.odCilindro || "");
                        $("#od-eje").val(graduacionTemporal.odEje || "");
                        $("#od-adicion").val(graduacionTemporal.odAdicion || "");
                        $("#oi-esfera").val(graduacionTemporal.oiEsfera || "");
                        $("#oi-cilindro").val(graduacionTemporal.oiCilindro || "");
                        $("#oi-eje").val(graduacionTemporal.oiEje || "");
                        $("#oi-adicion").val(graduacionTemporal.oiAdicion || "");
                        $("#distancia-pupilar").val(graduacionTemporal.distanciaPupilar || "");
                        $("#altura").val(graduacionTemporal.altura || "");
                        $("#_aco").val(graduacionTemporal._aco || "");
                        $("#notas-graduacion").val(graduacionTemporal.notasGraduacion || "");
                        $("#diagnostico").val(graduacionTemporal.diagnostico || "");
                        $("#stage").val(graduacionTemporal.stage || "9"); // Establecer el stage temporal
                    } else {
                        // Solo limpiar si no hay graduación temporal
                        $("#od-esfera").val("");
                        $("#od-cilindro").val("");
                        $("#od-eje").val("");
                        $("#od-adicion").val("");
                        $("#oi-esfera").val("");
                        $("#oi-cilindro").val("");
                        $("#oi-eje").val("");
                        $("#oi-adicion").val("");
                        $("#distancia-pupilar").val("");
                        $("#altura").val("");
                        $("#_aco").val("");
                        $("#notas-graduacion").val("");
                        $("#diagnostico").val("");
                        $("#stage").val("9"); // Establecer stage por defecto
                    }

                    // Mostrar el modal
                    const modalGraduacion = new bootstrap.Modal(document.getElementById('modal-graduacion'));
                    modalGraduacion.show();
                }
            }
        }

        function guardarGraduacion() {
            const idProducto = $("#producto-graduacion-id").val();
            const idRow = $("#producto-graduacion-idRow").val();
            console.log("guardar graduacion "+idRow);
            
            const graduacion = {
                odEsfera: $("#od-esfera").val(),
                odCilindro: $("#od-cilindro").val(),
                odEje: $("#od-eje").val(),
                odAdicion: $("#od-adicion").val(),
                oiEsfera: $("#oi-esfera").val(),
                oiCilindro: $("#oi-cilindro").val(),
                oiEje: $("#oi-eje").val(),
                oiAdicion: $("#oi-adicion").val(),
                distanciaPupilar: $("#distancia-pupilar").val().trim(),
                altura: $("#altura").val(),
                _aco: $("#_aco").val(),
                notasGraduacion: $("#notas-graduacion").val(),
                diagnostico: $("#diagnostico").val(),
                stage: $("#stage").val() || "9" // Agregar el stage seleccionado, por defecto 9
            };

        // Guardar en memoria
        graduacionesPendientes[idRow] = graduacion;
        graduacionesPendientes[idProducto] = graduacion;

        // Marcar visualmente que el producto tiene graduación
        $("#" + idRow + " .fa-glasses").addClass("text-success");

        // Actualizar color del producto según el stage
        actualizarColorProductoPorStage(idProducto, graduacion.stage || "9", idRow);
        $("#producto-" + idProducto + " .fa-glasses").addClass("text-success");

        // Mostrar el botón de ticket de graduación si hay un folio de venta
        if ($("#folio").val()) {
            $("#ticket-graduacion").removeClass("d-none");
        }

        // --- MODIFICACIÓN: Siempre guardar graduaciones aunque sea cotización ---
        // Detectar si el formulario tiene el botón de guardar venta/cotización
        // y agregar las graduaciones al request antes de enviar
        if (typeof window._graduacionesGuardadasCotizacion === 'undefined') {
            window._graduacionesGuardadasCotizacion = false;
        }

        // Hook para guardar graduaciones en el submit de venta/cotización
        if (!window._graduacionesGuardadasCotizacion) {
            window._graduacionesGuardadasCotizacion = true;
            $(document).on('submit', 'form', function(e) {
                // Si el form ya tiene el campo, no lo agregues de nuevo
                if ($(this).find('input[name="graduaciones"]').length === 0) {
                    var input = $('<input>').attr({
                        type: 'hidden',
                        name: 'graduaciones',
                        value: JSON.stringify(graduacionesPendientes)
                    });
                    $(this).append(input);
                } else {
                    $(this).find('input[name="graduaciones"]').val(JSON.stringify(graduacionesPendientes));
                }
            });
        }

        // Cerrar el modal
        const modalGraduacion = bootstrap.Modal.getInstance(document.getElementById('modal-graduacion'));
        modalGraduacion.hide();

        // Mostrar mensaje de éxito
        Swal.fire({
            title: '¡Éxito!',
            text: 'La graduación ha sido guardada temporalmente',
            icon: 'success',
            confirmButtonColor: '#00BFFF'
        });

        // Log para debugging
        console.log("Se ha guardado temporalmente la graduación del producto " + idProducto + ":");
        console.log("OJO DERECHO - Esf: " + graduacion.odEsfera + ", Cil: " + graduacion.odCilindro + ", Eje: " + graduacion.odEje + ", ADD: " + graduacion.odAdicion);
        console.log("OJO IZQUIERDO - Esf: " + graduacion.oiEsfera + ", Cil: " + graduacion.oiCilindro + ", Eje: " + graduacion.oiEje + ", ADD: " + graduacion.oiAdicion);
        console.log("DIP: " + graduacion.distanciaPupilar + ", AO: " + graduacion.altura + ", ACO: " + graduacion._aco);
        console.log("Diagnóstico:" + graduacion.diagnostico);
        console.log("Notas:" + graduacion.notasGraduacion);
        }

        // Función para abrir el visor de graduación
        function abrirVisorGraduacion() {
            var folio = $("#folio").val();
            var idempresa = $("#idempresa").val();
            var archivoautorizacion = $("#archivoautorizacion").val();

            // Obtener la graduación del último producto
            var idProducto = $("#producto-graduacion-id").val();
            var idRow = $("#producto-graduacion-idRow").val();
            var graduacion = graduacionesPendientes[idRow];

            if (!graduacion) {
                // Si no hay graduación, mostrar un mensaje de error
                Swal.fire({
                    title: 'Error',
                    text: 'No hay datos de graduación para mostrar',
                    icon: 'error',
                    confirmButtonColor: '#00BFFF'
                });
                return;
            }

            $("#modal-visor-body").html("");
            let url = $("#url-visor-documentos").val();

            // Convertir el objeto de graduación a JSON para enviarlo al servidor
            var graduacionJSON = JSON.stringify(graduacion);

            $.ajax({
                method: "POST",
                url: url,
                data: {
                    opcion: "graduacion",
                    folio: folio,
                    archivoautorizacion: archivoautorizacion,
                    idempresa: idempresa,
                    graduacion: graduacionJSON
                }
            })
                .done(function (html) {
                    $("#modal-visor-body").html(html);
                });
        }

        async function uploadFile() {
            let formData = new FormData();
            var url = $("#url-venta-subir-documento-venta").val();
            formData.append("file", fileupload.files[0]);

            var todoBien = false;
            var nombreDocumento = "";
            var msj = "";


            const response = await fetch(url, {
                method: "POST",
                body: formData
            });
            const result = await response.json();
            return result;


        }

        function abrirVisor(opcion = "ticket") {
            var folio = $("#folio").val();
            var idempresa = $("#idempresa").val();
            var archivoautorizacion = $("#archivoautorizacion").val();

            let url = $("#url-visor-documentos").val();
            let timestamp = new Date().getTime();

            let data = {
                opcion: opcion,
                folio: folio,
                archivoautorizacion: archivoautorizacion,
                idempresa: idempresa,
                _: timestamp // Evitar caché
            };

            if (opcion === "graduacion") {
                // Limpiar el contenedor del modal de graduación y backdrop
                $("#modal-visor-body-graduacion").html("");
                $(".modal-backdrop").remove();
                $("body").removeClass("modal-open");
                $("#modal-graduacion .modal-title").text("Ticket de Graduación");

                $.ajax({
                    method: "POST",
                    url: url,
                    data: data,
                    cache: false
                })
                .done(function (html) {
                    $("#modal-visor-body-graduacion").html(html);
                    // Forzar recarga de PDF si hay object embebido
                    let pdfObject = $("#modal-visor-body-graduacion object");
                    if (pdfObject.length > 0) {
                        let currentSrc = pdfObject.attr("data");
                        // Evitar duplicar el parámetro si ya existe
                        if (currentSrc && !currentSrc.includes("?t=")) {
                            pdfObject.attr("data", currentSrc + "?t=" + timestamp);
                        } else if (currentSrc) {
                            pdfObject.attr("data", currentSrc.replace(/([?&])t=\d+/, "$1t=" + timestamp));
                        }
                    }
                    // Mostrar el modal de graduación
                    var $modal = $("#modal-visor-graduacion");
                    $modal.show().addClass("show");
                    $("body").addClass("modal-open");
                    if ($(".modal-backdrop").length === 0) {
                        $("body").append('<div class="modal-backdrop show"></div>');
                    }
                })
                .fail(function(jqXHR, textStatus, errorThrown) {
                    console.error("Error al abrir el visor graduación:", textStatus, errorThrown);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'No se pudo cargar el documento de graduación. Por favor, intente nuevamente.'
                    });
                });
            } else {
                // Limpiar el contenedor del modal general y backdrop
                $("#modal-visor-body").html("");
                $(".modal-backdrop").remove();
                $("body").removeClass("modal-open");
                if (opcion === "especial") {
                    $("#exampleModalLabel").text("Ticket Especial en PDF");
                } else {
                    $("#exampleModalLabel").text("Ticket en PDF");
                }
                $.ajax({
                    method: "POST",
                    url: url,
                    data: data,
                    cache: false
                })
                .done(function (html) {
                    $("#modal-visor-body").html(html);
                    var $modal = $("#modal-visor-documentos");
                    $modal.show().addClass("show");
                    $("body").addClass("modal-open");
                    if ($(".modal-backdrop").length === 0) {
                        $("body").append('<div class="modal-backdrop show"></div>');
                    }
                })
                .fail(function(jqXHR, textStatus, errorThrown) {
                    console.error("Error al abrir el visor:", textStatus, errorThrown);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'No se pudo cargar el documento. Por favor, intente nuevamente.'
                    });
                });
            }
        }

        // Función para cerrar los modales
        $(document).on('click', '[data-bs-dismiss="mod"]', function() {
            var $modal = $(this).closest('.mod');
            $modal.removeClass("show").hide();
            $("body").removeClass("modal-open");
            $(".modal-backdrop").remove();
        });

        // Cerrar modal al hacer clic fuera del contenido
        $(document).on('click', '.mod.show', function(e) {
            if ($(e.target).hasClass('mod')) {
                $(this).removeClass("show").hide();
                $("body").removeClass("modal-open");
                $(".modal-backdrop").remove();
            }
        });

        // Asegurarse de que el backdrop se elimine al presionar ESC
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && $('.mod.show').length > 0) {
                $('.mod.show').removeClass("show").hide();
                $("body").removeClass("modal-open");
                $(".modal-backdrop").remove();
            }
        });

        function obtenerFormularioAgregarBeneficiario(isClient = -1) {

            $("#formularioBeneficiario").html("");

            if (isClient != -1)
                $("#beneficiary-modal-title").text("Agregar cliente");
            else
                $("#beneficiary-modal-title").text("Agregar beneficiario");
            $.ajax({
                url: "{{ path('agregar-formulario-beneficiario') }}",
                type: 'GET',
                data: {isClient: isClient},
                beforeSend: loadingGif("formularioBeneficiario"),
                dataType: "html"
            }).done(function (html) {
                $("#formularioBeneficiario").html(html);






            }).fail(function () {
                alert("error");
            });
        }


        $(document).ready(function () {

            $('#ventaCredito').on('ifChanged', function (event) {

                checked = $("#ventaCredito").is(":checked");

                if (checked) {

                    let restanStr = $("#anticipo-restan").val();
                    let creditoUsuarioStr = $("#client-credit").val();

                    // Eliminarcaracteres
                    let restan = parseFloat(restanStr.replace(/[^0-9.-]+/g, ""));
                    let creditoUsuario = parseFloat(creditoUsuarioStr.replace(/[^0-9.-]+/g, ""));

                    console.log("Valor inicial de restan:", restanStr);
                    console.log("Valor inicial de creditoUsuario:", creditoUsuarioStr);

                    if (isNaN(restan) || isNaN(creditoUsuario)) {
                        console.error("Error: Valores inválidos en el monto restante o crédito.");
                        return;
                    }


                    if (creditoUsuario < restan) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Crédito insuficiente',
                            text: `El crédito disponible (${creditoUsuario}) es menor que el monto restante (${restan}).`,
                            confirmButtonText: 'Entendido'
                        }).then(() => {
                            // Desmarcar el checkbox utilizando iCheck
                            $("#ventaCredito").iCheck('uncheck');
                            console.log("entro");
                            // Esconder los contenedores nuevamente
                            $("#selectDiasCredito").addClass("d-none");
                            $("#client-credit-container").addClass("d-none");
                        });
                    } else {
                        $("#selectDiasCredito").removeClass("d-none");
                        $("#client-credit-container").removeClass("d-none");
                    }

                } else {
                    $("#selectDiasCredito").addClass("d-none");
                    $("#client-credit-container").addClass("d-none");
                }

            });

            cargarMeses();
            resetCampos();
            reset();

            {% if rol == "ROLE_VENDEDOR" %}

            $("#select-permisos").addClass("d-none");
            //$("ventaCredito").attr("disabled", true);

            {% endif %}

            $("#permisosempresas").select2({
                dropdownParent: $("#modal-buscar-cotizacion .mod-body")
            });


            {% if venta is not null or venta is not empty %}
            $("#boton-agregar-beneficiario").removeClass("d-none");
            saleDocumentTable("{{ venta.idventa }}", "{{ venta.gerente }}");
            saleDocumentForm("{{ venta.gerente }}");
            calcularTotal();

            $("#authorization-number-input").val('{{ venta.authorizationnumber }}');
            $("#authorization-scan-input").val('{{ venta.authscan }}');
            $('#convenio').select2("enable", false);

            {% if beneficiaries != null %}

            {% for beneficiary in beneficiaries %}

            beneficiaries.set('{{ beneficiary.idcliente }}', '{{ beneficiary |json_encode() | raw }}');

            {% endfor %}

            {% endif %}

            {% if beneficiaries != null %}

            {% for beneficiary in beneficiaries %}

            beneficiaries.set('{{ beneficiary.idcliente }}', '{{ beneficiary |json_encode() | raw }}');

            {% endfor %}

            {% endif %}

            //$('#numero-empleado').attr("disabled", true);
            //$('#cliente-telefono').attr("disabled", true);
            //$('#sin-convenio-email-cliente').attr("disabled", true);

            $('#apellidoP').attr("disabled", false);
            $('#apellidoM').attr("disabled", false);
            $('#nombre').attr("disabled", false);
            // Permitir edición libre del campo empresa-cliente
            $('#empresa-cliente').prop('disabled', false);

            {% if venta.ventaGarantiaFolio is defined and venta.ventaGarantiaFolio != null %}

            $("#folio-venta-garantia").val("{{ venta.ventaGarantiaFolio }}");
            $(".agregar-venta").addClass("d-none");
            $(".quitar-venta").removeClass("d-none");
            $("#folio-venta-garantia").attr("disabled", true);
            $("#folio-venta-garantia").addClass("check");

            {% endif %}

            {% if venta.archivoautorizacion is  null or  venta.archivoautorizacion =="" %}
            $("#boton-modal-archivoautorizacion").addClass("d-none");
            {% else %}
            $("#boton-modal-archivoautorizacion").removeClass("d-none");
            {% endif %}

            $("#escotizacion").val({{ venta.cotizacion }});
            $('#archivoautorizacion').val('{{ venta.archivoautorizacion }}');
            $('#notas').val('{{ venta.notas }}');

            {% if venta.cotizacion !="1" %}
            //$('#convenio').select2("enable", false);
            $('#producto-codigo').attr("disabled", true);
            $('#producto-nombre').attr("disabled", true);
            $('#cupon-descuento').attr("disabled", true);
            $('#usuario-venta').select2("enable", false);
            $('#usuario-donde-nos-conocio').select2("enable", false);
            $('#guardar-venta').text("Actualizar Venta");
            $('#buscar-cupon').remove();
            $('#buscarCupon').remove();
            //$('#resaltar-formulario').remove();
            $('.client-button').remove();
            //$('#guardar-cotizacion').remove();

            //$("#ventaCredito").attr("disabled", true);

            {% endif %}

            {% if venta.credito == '1' %}
            $('#ventaCredito').prop('checked', true);
            $("#selectDiasCredito").removeClass("d-none");
            $("#client-credit-container").removeClass("d-none");
            {% else %}
            $('#ventaCredito').prop('checked', false);
            $("#selectDiasCredito").addClass("d-none");
            $("#client-credit-container").addClass("d-none");
            {% endif %}

            $('#diasCredito').val({{ venta.diascredito }}).trigger('change');

            let remainingCredit = parseFloat("{{ venta.authorizedcredit }}") - parseFloat("{{ venta.debt }}");
            $("#client-credit").val(remainingCredit);
            ponerFormatoPesos("#client-credit");


            $(".btn-pdf").each(function () {
                // Remove the 'd-none' class for each element with the 'btn-pdf' class
                $(this).removeClass("d-none");
            });

            $("#idventa").val("{{ venta.idventa }}");
            $("#apellidoP").val("{{ venta.clienteApellidopaterno }}");
            $("#apellidoM").val("{{ venta.clienteApellidomaterno }}");
            $("#nombre").val("{{ venta.nombreCliente ?? '' }}");
            $("#cliente-id").val("{{ venta.idcliente ?? '' }}");
            $("#cliente-telefono").val("{{ venta.telefono ?? '' }}");

            $("#sin-convenio-nombre-cliente").val("{{ venta.nombreCliente ?? '' }}");
            $("#sin-convenio-email-cliente").val("{{ venta.email }}");

            buscarBeneficiarios("{{ venta.idcliente }}", '{{ venta.beneficiarioNombre }}', '');
            $("#beneficiario").val("{{ venta.beneficiarioNombre }}");
            $("#beneficiario-nombre").val("{{ venta.beneficiario }}");

            $("#empresa-cliente").val("{{ venta.nombreEmpresa }}");
            $("#empresa-cliente-id").val("{{ venta.idempresacliente }}");

            //  $("#convenio").val("{{ venta.idtipoventa }}");
            // $('#convenio').val({{ venta.idtipoventa }});
            $('#convenio').val({{ venta.idtipoventa }}).trigger('change');
            //  alert("{{ venta.idtipoventa }}");
            cambiarFormulario();
            {% if venta.idunidad !="" and venta.idunidad is not null %}
            $('#unidad').val({{ venta.idunidad }}).trigger('change');
            {% endif %}

            $("#numero-empleado").val("{{ venta.numeroempleado }}").trigger('change');
            $("#usuario-venta").val("{{ venta.idusuario }}").trigger('change');
            $("#usuario-donde-nos-conocio").val("{{ venta.comonosconocio }}").trigger('change');

            $("#folio").val("{{ venta.folio }}");
            /*  if(response.cupon !=""){
            //$("#cupon-descuento").val(venta.cupon);
            $(".agregar-cupon").addClass("d-none");
            $(".quitar-cupon").removeClass("d-none");
            $("#cupon-descuento").attr("disabled",true);
          }*/

            var exists = false;
            var usuarioVenta = "{{ venta.idusuario }}";

            $("#usuario-venta option").each(function () {
                    //  alert("  "+this.value+" vs "+response.venta.idusuario);
                    if (this.value == usuarioVenta) {
                        exists = true;
                        return false;
                    }
                }
            );
            if (exists) {
                // alert("entra 0");
                $("#usuario-venta").val(usuarioVenta);
            } else {
                //agregamos la opcion
                //  alert("entra 1");
                //    $("#usuario-venta").append('<option selected value="'+"{{ venta.idusuario }}"+'">'+"{{ venta.vendedorNombre }}"+" "+"{{ venta.vendedorApellidopaterno }}"+'</option>' );
                var option = new Option('{{ venta.vendedorNombre~" "~venta.vendedorApellidopaterno }}', '{{ venta.idusuario }}', true, true);
                $("#usuario-venta").append(option).trigger('change');

            }


            console.log("Aquíx2");

            {% if productos %}
            {% for producto in productos %}
            agregarProductoDB(
                '{{ producto.modelo }}',
                '{{ producto.preciofinal }}',  // Precio original de la base de datos
                '{{ producto.descripcion }}',
                '{{ producto.porcentajedescuento }}',
                '{{ producto.modelo }}',
                '{{ producto.marca }}',
                '{{ producto.tipoproducto }}',
                '{{ producto.idproducto }}',
                "",
                '{{ producto.idstockventa }}',
                '{{ producto.idstock }}',
                '{{ producto.marca }}',
                '{{ producto.codigobarras }}',
                '{{ producto.cantidad }}',
                '{{ producto.masivounico }}',
                '{{ producto.cantidadMax }}',
                '{{ producto.codigobarrasuniversal }}',
                '{{ producto.codigo }}',
                '{{ producto.fixproduct }}',
                '{{ producto.isomittable }}',
            );
            {% endfor %}

            // Cargar automáticamente las graduaciones para los productos que las tengan
            setTimeout(function() {
                {% for producto in productos %}
                    // Verificar si el producto tiene un idstockventa
                    if ('{{ producto.idstockventa }}') {
                        const idProducto = '{{ producto.idproducto }}';
                        const idStockVenta = '{{ producto.idstockventa }}';

                        // Cargar la graduación desde la base de datos
                        cargarGraduacionDesdeDB(idStockVenta, function(graduacion) {
                            if (graduacion) {
                                // Obtener el idRow si está disponible
                                const row = $("#producto-" + idProducto);
                                const idRow = row.attr("id") || "producto-" + idProducto;

                                // Datos de graduación
                                const gradData = {
                                    odEsfera: graduacion.odEsfera || "",
                                    odCilindro: graduacion.odCilindro || "",
                                    odEje: graduacion.odEje || "",
                                    odAdicion: graduacion.odAdicion || "",
                                    oiEsfera: graduacion.oiEsfera || "",
                                    oiCilindro: graduacion.oiCilindro || "",
                                    oiEje: graduacion.oiEje || "",
                                    oiAdicion: graduacion.oiAdicion || "",
                                    distanciaPupilar: graduacion.distanciaPupilar || "",
                                    altura: graduacion.altura || "",
                                    _aco: graduacion._aco || "",
                                    notasGraduacion: graduacion.notas || "",
                                    diagnostico: graduacion.diagnostico || "",
                                    stage: graduacion.stage || "9" // Mantener el stage desde la BD
                                };

                                // Guardar la graduación en memoria usando idRow (nuevo enfoque)
                                graduacionesPendientes[idRow] = gradData;

                                // También guardar con idProducto para compatibilidad con código existente
                                graduacionesPendientes[idProducto] = gradData;

                                // Marcar visualmente que el producto tiene graduación
                                $("#producto-" + idProducto + " .fa-glasses").addClass("text-success");

                                // Aplicar color según el stage guardado en la BD
                                console.log("Aplicando color para producto existente:", idProducto, "Stage:", gradData.stage);
                                actualizarColorProductoPorStage(idProducto, gradData.stage, idRow);

                                // Mostrar el botón de ticket de graduación si hay un folio de venta
                                if ($("#folio").val()) {
                                    $("#ticket-graduacion").removeClass("d-none");
                                }
                            }
                        });
                    }
                {% endfor %}
            }, 1000); // Esperar 1 segundo para asegurar que todos los productos se hayan cargado
            {% endif %}



            {% if pagos %}
            {% for pago in pagos %}

            agregarPagoDB('{{ pago.monto }}', '{{ pago.tipopago }}', '{{ pago.idpaymenttype }}', '{{ pago.idpago }}', '{{ pago.isAutomatic }}');

            {% endfor %}
            {% endif %}

            {% if cupon %}
            $("#cupon-descuento").val('{{ cupon.codigo }}');
            $(".agregar-cupon").addClass("d-none");
            $(".quitar-cupon").removeClass("d-none");
            $("#cupon-descuento").attr("disabled", true);

            savedVentaCupon = "{{ cupon.idventacupon }}"
            curCupon = JSON.parse('{{ cuponJson|raw }}')

            $(".producto-seleccionado").each(function () {

                id = $(this).attr("id");
                if (curCupon && curCupon.nombre) {

                    nombreMarca = $(this).data("marca");
                    //console.log(nombreMarca)
                    if (curCupon.nombre == nombreMarca) {
                        $("#producto-descuento-" + id).prop("disabled", true)
                        //console.log(id)
                    }
                } else $("#producto-descuento-" + id).prop("disabled", true)
            });

            {% if venta.cotizacion != '1' %}
            $(".quitar-cupon").attr("disabled", true);
            {% endif %}

            {% endif %}

            {% else %}
            saleDocumentTable();
            saleDocumentForm();
            resetCampos();
            reset();
            {% endif %}

            $("#producto-codigo").on('keyup', function (e) {
                e.preventDefault();
                if (e.key === 'Enter' || e.keyCode === 13) {
                    buscarProductoPorCódigo($(this).val());
                }
            });

            tableSelectedBeneficiaries();
        });

        function resaltarFormularioRegistrar(opcion) {

            if (opcion == "1") {
                resetCampos();

                $(".registro").addClass("border border-warning");

                $(".btn-nuevo-cliente").addClass("d-none");
                $(".btn-reset-cliente").removeClass("d-none");


                $(".numero-empleado").autocomplete("disable");
                $(".cliente").autocomplete("disable");
                //$( ".sin-convenio-email-cliente" ).autocomplete( "disable" );
                //$( ".cliente-telefono" ).autocomplete( "disable" );
                $("#beneficiario").val("");
                $("#beneficiario").attr("disabled", "disabled");
            } else {
                $(".numero-empleado").autocomplete("enable");
                $(".cliente").autocomplete("enable");
                resetCampos();

                $(".registro").removeClass("border border-warning");

                $(".btn-nuevo-cliente").removeClass("d-none");
                $(".btn-reset-cliente").addClass("d-none");
                //$(".sin-convenio-email-cliente").addClass("d-none");
                //$(".cliente-telefono").addClass("d-none");
                $("#beneficiario").attr("disabled", "false");
            }

        }

        function BuscarCliente() {
            nombre = $("#BuscarNombre").val();
            apellidop = $("#BuscarApellidoP").val();
            apellidom = $("#BuscarApellidoM").val();
            email = $("#BuscarEmail").val();
            telefono = $("#BuscarTelefono").val();
            numeroempleado = $("#BuscarNumeroEmpleado").val();
            var urlBuscarCliente = $("#url-buscar-cliente").val();

            $("#tableBodyBuscarCliente").html("");

            // Mostrar el GIF de carga
            $("#loading-gif-container").show();

            $.ajax({
                url: urlBuscarCliente,
                data: {
                    email: email, telefono: telefono, nombre: nombre,
                    apellidop: apellidop, apellidom: apellidom,
                    numeroempleado: numeroempleado
                },
                beforeSend: function () {
                    // Mostrar el GIF de carga
                    $("#loading-gif-container").show();
                },

            }).done(function (suggestions) {

                suggestions = Object.values(suggestions);
                var tableBody = document.querySelector("#tableBodyBuscarCliente");
                $("#loading-gif-container").hide();  // Ocultar el GIF de carga
                tableBody.innerHTML = "";

                if (suggestions[0].length === 0) {
                    // Mostrar mensaje de "No hay resultados"
                    var noResultRow = document.createElement("tr");
                    var noResultCell = document.createElement("td");
                    noResultCell.setAttribute('colspan', '8'); // El número de columnas en tu tabla
                    noResultCell.classList.add('text-center');
                    noResultCell.textContent = "No hay resultados";
                    noResultRow.appendChild(noResultCell);
                    tableBody.appendChild(noResultRow);
                } else {
                    for (var i = 0; i < suggestions[0].length; i++) {
                        var newRow = document.createElement("tr");

                        var newCell = document.createElement("th");
                        newCell.setAttribute('scope', 'row');
                        newCell.classList.add('text-center');
                        newCell.textContent = i + 1;
                        newRow.appendChild(newCell);

                        newCell = document.createElement("td");
                        newCell.setAttribute('id', 'apellidoP_' + suggestions[0][i].data);
                        newCell.classList.add('text-center');
                        newCell.textContent = suggestions[0][i].apellidopaterno;
                        newRow.appendChild(newCell);

                        newCell = document.createElement("td");
                        newCell.setAttribute('id', 'apellidoM_' + suggestions[0][i].data);
                        newCell.classList.add('text-center');
                        newCell.textContent = suggestions[0][i].apellidomaterno;
                        newRow.appendChild(newCell);

                        newCell = document.createElement("td");
                        newCell.setAttribute('id', 'nombre_' + suggestions[0][i].data);
                        newCell.classList.add('text-center');
                        newCell.textContent = suggestions[0][i].value;
                        newRow.appendChild(newCell);

                        newCell = document.createElement("td");
                        newCell.setAttribute('id', 'email_' + suggestions[0][i].data);
                        newCell.classList.add('text-center');
                        newCell.textContent = suggestions[0][i].email;
                        newRow.appendChild(newCell);

                        newCell = document.createElement("td");
                        newCell.setAttribute('id', 'telefono_' + suggestions[0][i].data);
                        newCell.classList.add('text-center');
                        newCell.textContent = suggestions[0][i].telefono;
                        newRow.appendChild(newCell);

                        newCell = document.createElement("td");
                        newCell.setAttribute('id', 'numeroEmpleado_' + suggestions[0][i].data);
                        newCell.classList.add('text-center');
                        newCell.textContent = suggestions[0][i].numeroEmpleado;
                        newRow.appendChild(newCell);

                        newCell = document.createElement("td");
                        newCell.classList.add('text-center');
                        var newButton = document.createElement("button");

                        var remainingCredit = parseFloat(suggestions[0][i].authorizedcredit) - parseFloat(suggestions[0][i].debt);

                        var funcionSCD = 'seleccionarClienteDatos(' + suggestions[0][i].data + ',' + suggestions[0][i].diascredito + ',"' + (suggestions[0][i].idempresacliente || '') + '","' + (suggestions[0][i].nombreempresa || '') + '","' + remainingCredit + '","' + suggestions[0][i].idsellreference + '")';

                        newButton.setAttribute('class', 'btn btn-info');
                        newButton.setAttribute('type', 'button');
                        newButton.setAttribute('onclick', funcionSCD);
                        newButton.setAttribute('data-bs-dismiss', "mod");

                        newButton.textContent = 'Seleccionar';

                        newCell.appendChild(newButton);
                        newRow.appendChild(newCell);

                        tableBody.appendChild(newRow);
                    }
                }

            }).fail(function () {
                $("#loading-gif-container").hide();  // Ocultar el GIF de carga en caso de error
                alert("error");
            });
        }

        function seleccionarClienteDatos(id, diascredito, idempresa, nombreempresa, authorizedcredit, sellreferenceid) {
            var telefono = $("#telefono_" + id).text();
            var email = $("#email_" + id).text();

            var apellidoP = $("#apellidoP_" + id).text();
            var apellidoM = $("#apellidoM_" + id).text();
            var nombre = $("#nombre_" + id).text();
            var numeroEmpleado = $("#numeroEmpleado_" + id).text();

            if (sellreferenceid) $("#usuario-donde-nos-conocio").val(sellreferenceid).trigger('change');

            $("#client-credit").val(authorizedcredit);
            ponerFormatoPesos("#client-credit");

            buscarBeneficiarios(id);

            // Permitir ingresar cualquier empresa, limpiar el id oculto
            $("#empresa-cliente").val(nombreempresa);
            $("#empresa-cliente-id").val("");

            $('#diasCredito').val(diascredito).trigger('change');

            $('#cliente-telefono').val(telefono);
            $('#sin-convenio-email-cliente').val(email);
            $('#apellidoP').val(apellidoP);
            $('#apellidoM').val(apellidoM);
            $('#nombre').val(nombre);
            $('#numero-empleado').val(numeroEmpleado);

            tempClientId = $("#cliente-id").val();

            if (tempClientId != id) {
                beneficiaries.clear();
                tableSelectedBeneficiaries();
            }

            $("#cliente-id").val(id);

            // Actualiza el estado de los campos según si están vacíos o no
            actualizarEstadoCampo('#apellidoP', apellidoP);
            actualizarEstadoCampo('#apellidoM', apellidoM);
            actualizarEstadoCampo('#nombre', nombre);

            $("#boton-agregar-beneficiario").removeClass("d-none");
        }

        // Función para actualizar el estado disabled/enabled de un campo
        function actualizarEstadoCampo(selector, valor) {
            // Habilitar si el campo está vacío, deshabilitar si tiene contenido
            if (valor.trim() === '') {
                $(selector).attr("disabled", false);
            } else {
                $(selector).attr("disabled", true);
            }
        }


        function resetCampos() {
            $("#producto-codigo").focus();
            $("#anticipo-total-pagado").val("");
            $("#anticipo-restan").val("");
            $("#cliente").val("");
            $("#sin-convenio-email-cliente").val("");
            $("#cliente-telefono").val("");
            $("#cliente-id").val("");
            $("#numero-empleado").val("");
            $("#beneficiario").val("");
            $("#beneficiario-nombre").val("");
            $("#beneficiario-id").val("");
            // $("#convenio").val(null);
            $('#mySelect2').val(null).trigger('change');


        }

        function seleccionarBeneficiario(input) {
            var nombre = $(input).find('option:selected').text();
            var id = $(input).val();
            $("#beneficiario-nombre").val(nombre);
            $("#beneficiario-id").val(id);
        }


    </script>

    <script>

        let priceModifiedManually = false;


        $("#producto-precio").on('input', function () {
            priceModifiedManually = true; // El usuario ha modificado el precio
        });


        function buscarProductoPorCódigo() {

            var url = $("#url-buscar-producto-codigo").val();

            var valor = $("#producto-codigo").val();


            $.ajax({
                method: "POST",
                data: {query: valor},
                url: url,
                dataType: "json",
                beforeSend: function (xhr) {

                }
            }).done(function (resultado) {
                if (resultado.exito) {

                    let curPrice = (parseFloat(resultado.producto.stockPrice) > 0) ? resultado.producto.stockPrice : resultado.producto.precio;

                    // Restablecer otras propiedades sin problemas
                    $("#producto-descripcion").val(resultado.producto.descripcion);

                    $("#producto-precio").val(curPrice);

                    $("#producto-descripcion").val(resultado.producto.descripcion);

                    ponerFormatoPesos("#producto-precio");
                    $("#producto-nombre-id").val(resultado.producto.data);
                    $("#producto-nombre").val(resultado.producto.nombre);
                    $("#producto-tipoproducto").val(resultado.producto.tipoproducto);
                    $("#producto-idproducto").val(resultado.producto.idproducto);
                    $("#producto-marca").val(resultado.producto.marca);
                    $("#producto-codigobarras").val(resultado.producto.value);
                    $("#producto-codigobarrasuniversal").val(resultado.producto.codigobarrasuniversal);
                    $("#producto-masivounico").val(resultado.producto.masivounico);
                    $("#categoria-producto").val(resultado.producto.clase);
                    $("#producto-precioespecial").val(resultado.producto.precioespecial);
                    $("#producto-preciosubdistribuidor").val(resultado.producto.preciosubdistribuidor);
                    $("#producto-preciodistribuidor").val(resultado.producto.preciodistribuidor);
                    $("#producto-cantidad-max").val(resultado.producto.cantidad);
                    //ponemos el id
                    //  calcularTotal(suggestion.data);
                    agregarProducto(resultado.producto.data);
                } else {
                    $("#producto-codigo").val("");
                    Swal.fire(
                        'No se encontraron resultados!',
                        resultado.msj,
                        'warning'
                    );
                }
                //  if($("#producto-nombre-id").val() !=""){

            });
        }

        $(document).ready(function () {
            //Paso 2
            var urlSeleccionarClientes = $("#url-seleccionar-clientes").val();
            var urlSeleccionarEmail = $("#url-seleccionar-email").val();
            var urlSeleccionarTelefono = $("#url-seleccionar-telefono").val();

            var urlBuscarCliente = $("#url-buscar-cliente").val();
            var urlBuscarNumeroCliente = $("#url-buscar-numero-cliente").val();
            var urlBuscarProductoCodigo = $("#url-buscar-producto-codigo").val();
            var urlBuscarProductoNombre = $("#url-buscar-producto-nombre").val();
            var urlBuscarTratamiento = $("#url-buscar-tratamiento").val();
            var urlBuscarEmpresaCliente = $("#url-buscar-empresa-cliente").val();
            var urlSearchClientEmail = $("#url-venta-search-client-email").val();
            var urlSearchClientPhone = $("#url-venta-search-client-phone").val();

            // Eliminar autocomplete para empresa-cliente, permitir texto libre
            if ($.isFunction($('#empresa-cliente').autocomplete)) {
                try { $('#empresa-cliente').autocomplete('destroy'); } catch (e) {}
            }

            //Paso 1
            $('.cliente').autocomplete({
                delay: 2000,
                serviceUrl: urlSeleccionarClientes,
                onSelect: function (suggestion) {
                    resetCampos();
                    $("#cliente").val(suggestion.nombre);
                    $("#sin-convenio-email-cliente").val(suggestion.email);
                    $("#cliente-telefono").val(suggestion.telefono);
                    $("#cliente-id").val(suggestion.data);
                    $("#numero-empleado").val(suggestion.numeroempleado);
                    buscarBeneficiarios(suggestion.data);


                },
                onSearchComplete: function (event, ui) {
                    /* $("#cliente-id").val("");
           $("#numero-empleado").val("");*/
                }
            });

            $('#producto-nombre').autocomplete({
                delay: 500,
                serviceUrl: urlBuscarProductoNombre,
                onSelect: function (suggestion) {

                    const barcodeService = (suggestion.codigo) ? suggestion.codigo : suggestion.codigobarrasuniversal
                    $("#producto-precio").val(suggestion.precio);
                    $("#producto-descripcion").val(suggestion.descripcion);
                    $("#producto-precio").val(suggestion.precio);
                    ponerFormatoPesos("#producto-precio");
                    $("#producto-nombre-id").val(suggestion.data);
                    $("#producto-codigo").val(suggestion.codigo);
                    $("#producto-codigobarras").val(barcodeService);
                    $("#producto-tipoproducto").val(suggestion.tipoproducto);
                    $("#producto-idproducto").val(suggestion.data);
                    $("#producto-precioespecial").val(suggestion.precioespecial);
                    $("#producto-preciosubdistribuidor").val(suggestion.preciosubdistribuidor);
                    $("#producto-preciodistribuidor").val(suggestion.preciodistribuidor);
                    $("#producto-masivounico").val("");
                    $("#producto-cantidad-max").val("");
                    $("#estratamiento").val("si");

                    //calcularTotal();
                    agregarProducto(suggestion.data);
                }
            });
            $('#tratamiento').autocomplete({
                serviceUrl: urlBuscarTratamiento,
                onSelect: function (suggestion) {
                    $("#tratamiento-id").val(suggestion.data);
                    $("#tratamiento-precio").val(suggestion.precio);
                    agregarTratamiento(suggestion.data);
                }
            });
            //para seleccionar el tipo de formulario dependeiendo del convenio
            //cambiarFormulario();
            $.ajax({
                url: "{{ path('obtener-meses') }}",


                dataType: "html"
            }).done(function (html) {
                $("#meses").html(html);
            }).fail(function () {
                alert("error");
            });
            $("#meses").addClass("d-none")
        });

        function beneficiaryTable() {

            let url = $("#url-ventas-beneficiary-table").val();

            idclient = $("#cliente-id").val();

            $.ajax({
                method: "POST",
                data: {idclient: idclient},
                url: url,
                beforeSend: loadingGif("select-beneficiaries-container"),
            }).done(function (html) {
                $("#select-beneficiaries-container").html(html);
            });
        }

        function tableSelectedBeneficiaries() {

            var tableBody = document.querySelector("#beneficiary-tablebody");

            $("#beneficiary-tablebody").html("");

            if (beneficiaries.size > 0) $("#beneficiary-selected-table").removeClass("d-none");
            else $("#beneficiary-selected-table").addClass("d-none");

            beneficiaries.forEach((value, key) => {
                value = JSON.parse(value);
                var newRow = document.createElement("tr");
                var newCell = document.createElement("td");
                newCell.setAttribute('class', 'text-center');
                newCell.textContent = value.apellidopaterno;
                newRow.appendChild(newCell);
                var newCell = document.createElement("td");
                newCell.setAttribute('class', 'text-center');
                newCell.textContent = value.apellidomaterno;
                newRow.appendChild(newCell);
                var newCell = document.createElement("td");
                newCell.setAttribute('class', 'text-center');
                newCell.textContent = value.nombre;
                newRow.appendChild(newCell);
                var newCell = document.createElement("td");
                newCell.setAttribute('class', 'text-center');
                newCell.textContent = value.beneficiarytype;
                newRow.appendChild(newCell);
                var newCell = document.createElement("td");
                var newButton = document.createElement("button");
                newButton.setAttribute('class', 'btn btn-danger');
                newButton.setAttribute('type', 'button');
                newButton.setAttribute('onclick', 'deleteSelectedBeneficiary("' + value.idcliente + '")');
                //newButton.setAttribute('data-bs-dismiss', "mod");
                newButton.textContent = 'Eliminar';
                newCell.appendChild(newButton);
                ;
                newRow.appendChild(newCell);

                tableBody.appendChild(newRow);
            });
        }

        function deleteSelectedBeneficiary(beneficiaryId) {

            beneficiaries.delete(beneficiaryId)

            tableSelectedBeneficiaries();
        }

        function buscarBeneficiarios(idcliente, nombreCliente = "", beneficiario = "") {

            $("#beneficiario").html("");
            var url = $("#url-buscar-beneficiarios").val();
            $.ajax({
                method: "POST",
                data: {idcliente: idcliente},
                url: url,
                beforeSend: function (xhr) {

                }
            }).done(function (response) {

                //  var res=JSON.parse(response);
                $("#beneficiario").append('<option value=""></option>');
                let selected = false;
                for (beneficiario in response) {

                    if ((response[beneficiario].nombre + ' ' + response[beneficiario].apellidopaterno + ' ' + response[beneficiario].apellidomaterno) == nombreCliente) {
                        $("#beneficiario-id").val(response[beneficiario].idcliente);
                        selected = true;
                    } else {
                        selected = false;
                    }
//      $("#beneficiario").append('<option value="'+response[beneficiario].idbeneficiario+'">'+response[beneficiario].nombre+"</option>");
                    $("#beneficiario").append(new Option(response[beneficiario].nombre + ' ' + response[beneficiario].apellidopaterno + ' ' + response[beneficiario].apellidomaterno, response[beneficiario].idcliente, selected, selected));
                }
            });
        }

        function imprimir2() {
            PrintElem("ticket");
        }

        function PrintElem(elem) {
            var mywindow = window.open('', 'PRINT', 'height=400,width=800');
            mywindow.document.write('<html><head><title>' + document.title + '</title>');
            mywindow.document.write('</head><body >');
            mywindow.document.write('<h1>' + document.title + '</h1>');
            mywindow.document.write(document.getElementById(elem).innerHTML);
            mywindow.document.write('</body></html>');
            mywindow.document.close(); // necessary for IE >= 10
            mywindow.focus(); // necessary for IE >= 10*/
            mywindow.print();
            mywindow.close();

            return true;
        }

        function countServices() {
            $(".producto-seleccionado").each(function () {
                var tipoproducto = $(this).data("tipoproducto");
                var dbproduct = $(this).data("dbproduct");
                var id = $(this).attr("id");
                var quantity = quitarFormato($("#producto-cantidad-" + id).val());
                if (tipoproducto == 2 && !dbproduct) servicesQuantities.set(parseInt(id), quantity)
                if (tipoproducto == 2 && dbproduct) dbServicesQuantities.set(parseInt(id), quantity)
            });
        }

        function countStorables() {

            storablesQuantities.clear();

            storablesQuantities = new Map();
            dbStorablesQuantities = new Map();

            $(".producto-seleccionado").each(function () {
                var tipoproducto = $(this).data("tipoproducto");
                var dbproduct = $(this).data("dbproduct");
                var masivounico = $(this).data("masivounico");
                var convenio = $(this).data("omitted");
                var id = $(this).attr("id");
                var quantity = parseFloat(quitarFormato($("#producto-cantidad-" + id).val())) || 0;


                console.log("Es convenio", convenio)


                if (tipoproducto == 1 && !dbproduct && !convenio) {
                    console.log("Registrando producto almacenable ID:", id, "Cantidad:", quantity);
                    storablesQuantities.set(parseInt(id), quantity);
                } else if (tipoproducto == 1 && dbproduct && !convenio) dbStorablesQuantities.set(parseInt(id), quantity)
            });

        }

        function calcularTotalProducto(id) {
            var productoCantidad = quitarFormato($("#producto-cantidad-" + id).val());
            var productoPrecio = quitarFormato($("#producto-precio-" + id).val());

            console.log(productoPrecio, productoCantidad)
            var productoDescuento = quitarFormato($("#producto-descuento-" + id).val());

            $("#producto-final-" + id).val(productoPrecio * (1 - (productoDescuento * .01)));

            $("#producto-total-" + id).val(productoPrecio * (1 - (productoDescuento * .01)) * productoCantidad);

            ponerFormatoPesos("#producto-total-" + id);
        }

        async function guardarVenta(esCotizacion = 0) {

            var validEmail = /^\w+([.-_+]?\w+)*@\w+([.-]?\w+)*(\.\w{2,10})+$/;

            changeButton("guardar-cotizacion", 0, 1);
            changeButton("guardar-venta", 0, 1);
            //var beneficiario=$("#beneficiario").val();
            //    alert("beneficiario "+beneficiario);
            var idventa = $("#idventa").val();

            var msj = "";
            var apellidoP = $("#apellidoP").val();
            var apellidoM = $("#apellidoM").val();
            var nombre = $("#nombre").val();
            var empresaclienteid = $("#empresa-cliente-id").val();
            var ventaCredito = $("#ventaCredito").is(":checked");
            var idcliente = $("#cliente-id").val();
            var convenio = $("#convenio").val();
            var diascredito = $('#diasCredito').val();
            var numeroEmpleado = $("#numero-empleado").val();
            var idunidad = $("#unidad").val();
            var unidadNombre = $("#unidad").find('option:selected').text();
            var pidioFactura = "";
            var tipoPago = "";
            var subtotal = quitarFormato($("#subtotal").val());
            var iva = quitarFormato($("#iva").val());
            var total = quitarFormato($("#total").val());
            var sinConvenioNombreCliente = $("#sin-convenio-nombre-cliente").val();
            var clienteTelefono = $("#cliente-telefono").val();
            var sinConvenioEmailCliente = $("#sin-convenio-email-cliente").val();
            var idUsuarioVenta = $("#usuario-venta").val();
            var cuponDescuento = $("#cupon-descuento").val();
            var beneficiarioNombre = $("#beneficiario-nombre").val();
            var beneficiario = $("#beneficiario-id").val();
            var anticipoTotalPagado = parseFloat(quitarFormato($("#anticipo-total-pagado").val()));
            var authorizationNumber = $("#authorization-number-input").val();
            var authorizationScan = $("#authorization-scan-input").val();
            var notas = $("#notas").val();
            var porcentajeiva = ivaValue;
            var url = $("#url-guardar-venta").val();
            var documentoobligatoriocerrarventa = $("#documentoobligatoriocerrarventa").val();
            var tieneDocumento = parseInt(numberUploadedFiles);

             console.log("tieneDocumento", tieneDocumento)

            var preciofijoproductosalmacenables = $("#preciofijoproductosalmacenables").val();
            var preciofijoservicios = $("#preciofijoservicios").val();

            var foliogarantia = "";
            var empresagarantia = "";

            if ($("#folio-venta-garantia").hasClass("check")) {
                foliogarantia = $("#folio-venta-garantia").val();
                empresagarantia = $("#empresas").val();
            }

            //  obtenemos los poroductos
            var productos = [];
            //esto es para el aparato auditivo
            var numeroProductos = 0;
            var todoBien = false;
            var todoBienDocumento = false;
            var nombreDocumento = "";

             console.log('G U A R D A R C O T I Z A C I O N ', productos);

            //validamos los datos
            $(".producto-seleccionado").each(function () {

                var id = $(this).attr('id');
                var idproductoventa = $(this).data('idproductoventa');
                var idstockventa = $(this).data('idstockventa');
                var idproducto = $(this).data('idproducto');
                var idstock = $(this).data('idstock');
                var tipo = $(this).data('tipo');
                var precioOriginal = $(this).data('precio'); // precio original
                var descripcion = $(this).data('descripcion');
                var nombre = $(this).data('nombre');
                var tipoproducto = $(this).data('tipoproducto');
                var codigobarras = $(this).data('codigobarras');
                var fixproduct = $(this).data('fixproduct');
                var productoTotal = quitarFormato($("#producto-total-" + id).val());
                var productoPrecio = quitarFormato($("#producto-precio-" + id).val());
                var productoCantidad = quitarFormato($("#producto-cantidad-" + id).val());
                var numeroProductos = quitarFormato($("#producto-cantidad-" + id).val());
                var productoDescuento = quitarFormato($("#producto-descuento-" + id).val());
                var productoPrecioFinal = quitarFormato($("#producto-final-" + id).val());
                var omittable = $("#producto-omit-" + id).prop('checked');

                let formattedOmittable = (omittable) ? '1' : '0';
                let formattedFixProduct = (fixproduct) ? '1' : '0';


                if (omittable) {
                    $("#ticket-especial").removeClass("d-none");
                }

                producto = {
                    id: id,
                    codigobarras: codigobarras,
                    tipoproducto: tipoproducto,
                    precioOriginal: precioOriginal,
                    idstockventa: idstockventa,
                    idstock: idstock,
                    idproducto: idproducto,
                    productoDescuento: productoDescuento,
                    descripcion: descripcion,
                    nombre: nombre,
                    productoTotal: productoTotal,
                    productoPrecio: productoPrecio,
                    productoPrecioFinal: productoPrecioFinal,
                    productoCantidad: productoCantidad,
                    tipo: tipo,
                    idproductoventa: idproductoventa,
                    fixproduct: formattedFixProduct,
                    isOmittable: formattedOmittable
                };

                console.log("productos", producto);
                productos.push(producto);
                console.log("productos2", productos)

            });

            var checkNumber = /^.{10,}$/;
            var dondeNosConocio = $("#usuario-donde-nos-conocio").val();
            var otroInput = document.getElementById("otro-input");
            var otroValor = otroInput.value;

            if (dondeNosConocio == '18') dondeNosConocio = otroValor;

            if (nombre != "") {
                if (productos.length > 0) {
                    if (idUsuarioVenta != "") {
                        if (convenio != "" && convenio != null) {
                            //si el email es diferente entonces los validamos

                            if (clienteTelefono != "") {

                                if (checkNumber.test(clienteTelefono)) {
                                    //si los campos extra estan visibles entonces tambien los validamos
                                    if (!$(".datos-extra").hasClass("d-none")) {
                                        //validamos
                                        if (numeroEmpleado != "") {
                                            if (unidad != "") {
                                                todoBien = true;
                                            } else {
                                                $("#unidad").focus();
                                                msj = "La unida de procedencia es obligatoria";
                                            }
                                        } else {
                                            $("#numero-empleado").focus();
                                            msj = "El número de empleado es obligatorio";
                                        }

                                    } else {
                                        todoBien = true;
                                    }
                                } else {
                                    $("#cliente-telefono").focus();
                                    msj = "El teléfono es inválido";
                                }

                            } else {
                                $("#cliente-telefono").focus();
                                msj = "El teléfono es obligatorio";
                            }


                        } else {
                            $("#convenio").focus();
                            msj = "El tipo de venta es obligatorio";
                        }
                    } else {
                        msj = "Debe seleccionar el usuario que realizó la venta";
                        $("#usuario-venta").focus();
                    }

                } else {
                    $("#producto-codigo").focus();
                    msj = "Debe seleccionar por lo menos un producto";
                }
            } else {
                $("#cliente").focus();
                msj = "El nombre del cliente es obligatorio";
            }

            if (validEmail.test(sinConvenioEmailCliente) || sinConvenioEmailCliente == "" || sinConvenioEmailCliente == null) {

            } else {
                todoBien = false;
                msj = "El correo eletrónico tiene un formato inválido";
            }


            if ((documentoobligatoriocerrarventa == "1" && parseInt(numberUploadedFiles) > 0) || esCotizacion == "1" || documentoobligatoriocerrarventa == "0") {
                todoBienDocumento = true;
            }

            var preciofijoproductosalmacenables = $("#preciofijoproductosalmacenables").val();
            var preciofijoservicios = $("#preciofijoservicios").val();

            countServices()
            countStorables()

            /*if (preciofijoproductosalmacenables > 0 && (storablesQuantities.size + dbStorablesQuantities.size) <= 0) {
                todoBien = false;
                msj = "Necesitas al menos un producto almacenable";
            }*/

            if (preciofijoservicios > 0 && (servicesQuantities.size + dbServicesQuantities.size) <= 0) {
                todoBien = false;
                msj = "Necesitas al menos un servicio";
            }

            if (todoBien && todoBienDocumento) {


                meses = (curMSI) ? $("#cantidad_meses").val() : null;
                beneficiaryIds = Array.from(beneficiaries.keys());

                //if (esCotizacion != "1" || (esCotizacion == "1" && tipoPago == "")) {
                //agregamos los pagos
                pagos = [];


                //si no es cotizacion tomamos los pagos
                //if (esCotizacion != "1") {
                $(".pagos").each(function () {
                    pagos.push({
                        monto: parseFloat(quitarFormato($(this).val())),
                        tipopagoanticipo: $(this).data('tipopagoanticipo'),
                        idpago: $(this).data('idpago')
                    });
                });
                //}
                $.ajax({
                    method: "POST",
                    data: {
                        cuponDescuento: cuponDescuento,
                        idventa: idventa,

                        pagos: pagos,
                        tieneDocumento: tieneDocumento,
                        dondeNosConocio: dondeNosConocio,

                        idUsuarioVenta: idUsuarioVenta,
                        pidioFactura: pidioFactura,

                        tipoPago: tipoPago,
                        esCotizacion: esCotizacion,

                        unidadNombre: unidadNombre,
                        idunidad: idunidad,

                        sinConvenioNombreCliente: sinConvenioNombreCliente,

                        sinConvenioEmailCliente: sinConvenioEmailCliente,

                        clienteTelefono: clienteTelefono,
                        porcentajeiva: porcentajeiva,

                        subtotal: subtotal,
                        iva: iva,

                        total: total,
                        beneficiarioNombre: beneficiarioNombre,

                        idcliente: idcliente,
                        convenio: convenio,

                        numeroEmpleado: numeroEmpleado,
                        beneficiario: beneficiario,

                        productos: productos,
                        meses: meses,
                        nombre: nombre,
                        apellidoP: apellidoP,

                        apellidoM: apellidoM,
                        notas: notas,
                        foliogarantia: foliogarantia,

                        empresagarantia: empresagarantia,
                        ventaCredito: ventaCredito,

                        empresaclienteid: empresaclienteid,
                        diascredito: diascredito,

                        beneficiaryIds: beneficiaryIds,
                        authorizationNumber: authorizationNumber,
                        authorizationScan: authorizationScan,
                        savedVentaCupon: savedVentaCupon,
                        graduaciones: JSON.stringify(graduacionesPendientes),
                    },
                    url: url,
                }).done(function (response) {

                    console.log("response", response);
                    changeButton("guardar-cotizacion", 1, 1);
                    changeButton("guardar-venta", 1, 1);

                    uploadSaleDocument(response.idventa, 1);

                    if (response.exito) {
                        $("#folio").val(response.folio);
                        $("#guardar-venta").hide();
                        $("#guardar-cotizacion").hide();

                        console.log("graduacionesPendientes", graduacionesPendientes);
                        console.log("productos", productos);

                        // Guardar las graduaciones pendientes

                        const graduacionPromises = [];
                        for (const [idProducto, graduacion] of Object.entries(graduacionesPendientes)) {

                            console.log("Enviando graduación para ID", idProducto, graduacion);

                            if (/^\d+$/.test(idProducto)) {
                                const graduacionData = {
                                    idVenta: response.idventa,
                                    idProducto: idProducto,
                                    odEsfera: graduacion.odEsfera,
                                    odCilindro: graduacion.odCilindro,
                                    odEje: graduacion.odEje,
                                    odAdicion: graduacion.odAdicion,
                                    oiEsfera: graduacion.oiEsfera,
                                    oiCilindro: graduacion.oiCilindro,
                                    oiEje: graduacion.oiEje,
                                    oiAdicion: graduacion.oiAdicion,
                                    distanciaPupilar: graduacion.distanciaPupilar,
                                    altura: graduacion.altura,
                                    _aco: graduacion._aco,
                                    diagnosis: graduacion.diagnostico,
                                    notas: graduacion.notasGraduacion,
                                    stage: graduacion.stage || "9" // Agregar el stage
                                };

                                console.log("=== ENVIANDO GRADUACION ===");
                                console.log("Producto ID:", idProducto);
                                console.log("Stage enviado:", graduacionData.stage);
                                console.log("Datos completos:", graduacionData);

                                graduacionPromises.push(
                                    $.ajax({
                                        method: "POST",
                                        url: "{{ path('agregar_graduacion') }}",
                                        contentType: "application/json",
                                        data: JSON.stringify(graduacionData)
                                    })
                                );
                            }

                        }

                        console.log("graduacionesPendientes", graduacionesPendientes);
                        console.log("productos", productos);


                        Promise.all(graduacionPromises).then(results => {
                            console.log('resultados', results)
                            // Verifica si todas las graduaciones se guardaron correctamente
                            const errores = results.filter(r => !r.exito);
                            if (errores.length === 0) {
                                crearTicket(subtotal, iva, total, porcentajeiva, productos, unidadNombre, response.folio, esCotizacion, beneficiaryIds);
                                Swal.fire('¡Éxito!', 'La venta/cotización y todas las graduaciones se guardaron correctamente.', 'success');
                                graduacionesPendientes = {};
                            } else {
                                Swal.fire('¡Atención!', 'Algunas graduaciones no se guardaron correctamente.', 'warning');
                            }
                        }).catch(() => {
                            Swal.fire('¡Error!', 'Ocurrió un error al guardar las graduaciones.', 'error');
                        });

                        // ... resto del código existente ...

                    } else {
                        Swal.fire(
                            '¡Revisar datos!',
                            response.msj,
                            'warning'
                        );
                    }

                }).fail(function (jqXHR, textStatus) {

                    Swal.fire(
                        "Error al imprimir1: ",
                        JSON.stringify(jqXHR) + JSON.stringify(textStatus),
                        'warning'
                    )
                });
                //}

            } else if (todoBien) {

                Swal.fire(
                    'Error al subir el documento',
                    'Se debe cargar al menos un archivo',
                    'warning'
                )
            } else {
                Swal.fire(
                    '¡Datos incorrectos!',
                    msj,
                    'warning'
                )
            }

            changeButton("guardar-cotizacion", 1, 1);
            changeButton("guardar-venta", 1, 1);


        }

        function selectOther() {
            var selectElement = document.getElementById("usuario-donde-nos-conocio");
            var selectedValue = selectElement.value;
            var otroContainer = document.getElementById("otro-container");

            if (selectedValue == '18') {

                otroContainer.classList.add("visible");
                otroContainer.classList.remove("hidden");

            } else {
                otroContainer.classList.add("hidden");
                otroContainer.classList.remove("visible");
            }
        }


        function crearTicket(subtotal, iva, total, porcentajeiva, productos, unidadNombre, folio, esCotizacion, beneficiaryIds) {

            var apellidoP = $("#apellidoP").val();
            var apellidoM = $("#apellidoM").val();
            var nombre = $("#nombre").val();

            var preciofijotipoventa = $("#totalventaiva").val();

            var cliente = apellidoP + " " + apellidoM + " " + nombre;

            var convenio = $("#convenio").val();
            var numeroEmpleado = $("#numero-empleado").val();
            var beneficiarioNombre = $("#beneficiario-nombre").val();
            var beneficiario = $("#beneficiario-id").val();
            var sinConvenioNombreCliente = $("#sin-convenio-nombre-cliente").val();
            var clienteTelefono = $("#cliente-telefono").val();
            var sinConvenioEmailCliente = $("#sin-convenio-email-cliente").val();
            var cuponDescuento = $("#cupon-descuento").val();
            var usuarioVenta = $("#usuario-venta").val();
            var idempresa = $("#idempresa").val();

            var url = $("#url-ticket").val();
            var msj = "";

            if (productos.length <= 0) msj = "Debe agregar por lo menos un producto";
            if (cliente.length <= 0) msj = "Debe agregar el nombre del cliente";

            if (msj == "") {

                $.ajax({
                    method: "POST",
                    data: {
                        cuponDescuento: cuponDescuento,
                        folio: folio,
                        unidadNombre: unidadNombre,
                        sinConvenioNombreCliente: sinConvenioNombreCliente,
                        sinConvenioEmailCliente: sinConvenioEmailCliente,
                        clienteTelefono: clienteTelefono,
                        subtotal: subtotal,
                        iva: iva,
                        total: total,
                        porcentajeiva: porcentajeiva,
                        beneficiarioNombre: beneficiarioNombre,
                        cliente: cliente,
                        convenio: convenio,
                        numeroEmpleado: numeroEmpleado,
                        beneficiario: beneficiario,
                        productos: productos,
                        esCotizacion: esCotizacion,
                        usuarioVenta: usuarioVenta,
                        preciofijotipoventa: preciofijotipoventa,
                        idempresa: idempresa,
                        beneficiaryIds: beneficiaryIds

                    },
                    url: url,
                    beforeSend: function (xhr) {

                    }
                }).done(function (response) {
                    if (response.exito) {
                        $(".nueva-venta").addClass("d-none");
                        $(".botones-finales").removeClass("d-none");


                    } else {
                        Swal.fire(
                            '¡Algo ocurrió!',
                            'Error al Imprimir !' + response.msj,
                            'warning'
                        )
                    }

                    reset();
                }).fail(function (jqXHR, textStatus) {

                    Swal.fire(
                        "Error al imprimir: ",
                        JSON.stringify(jqXHR) + JSON.stringify(textStatus),
                        'warning'
                    )
                });
                /*   },
             onClose: () => {

             }
           }).then((result) => {

           })*/
            } else {
                Swal.fire(
                    'Datos incorrectos!',
                    msj,
                    'warning'
                )
            }

        }


        function calcularTotal() {

            preciototaltipoventa = $("#totalventaiva").val();
            ivafijo = $("#ivafijo").val();
            subtotalfijo = $("#subtotalfijo").val();


            if (preciototaltipoventa >= 0) {

                $("#total").val("$" + formatMoney(preciototaltipoventa));

                {% if venta is not null or venta is not empty %}
                {% if venta.totalventaconiva != null %}
                $("#total").val("$" + formatMoney("{{ venta.pagado }}"));
                {% endif %}
                {% endif %}

                $("#subtotal").val("$" + formatMoney(subtotalfijo));
                $("#iva").val("$" + formatMoney(ivafijo));

                $("#textopreciofijotipoventa").removeClass("d-none");


            } else {

                $("#textopreciofijotipoventa").addClass("d-none");

                var total = 0;
                var iva = 0;
                var subtotal = 0;

                var conveniolimit = 0;

                $(".producto-seleccionado").each(function () {
                    var id = $(this).attr('id');
                    var productoTotal = quitarFormato($("#producto-total-" + id).val());

                    productoTotalSinIva = productoTotal / (1 + ivaValue);
                    subtotalAux = (productoTotalSinIva);
                    subtotal += subtotalAux;
                    ivaAux = (productoTotalSinIva * ivaValue);
                    iva += ivaAux;
                    totalAux = (productoTotalSinIva + ivaAux);
                    total += totalAux;

                    var convenio = $("#producto-omit-" + id).prop('checked');
                    if (convenio) {
                        conveniolimit += totalAux;
                    }
                });
                $("#subtotal").val("$" + formatMoney(subtotal));
                $("#iva").val("$" + formatMoney(iva));

                $("#total").val("$" + formatMoney(total));

                $("#total-limit").val("$" + formatMoney(conveniolimit));

            }

            fixPaymentId = $("#fix-payment-id").val()


            if (fixPaymentId) {
                {% if venta is null or venta is empty or venta.cotizacion == '1' %}
                removePayments()
                curtotal = $("#total").val()
                fixPaymentType = $("#fix-payment-type").val()
                //agregarPago(curtotal, fixPaymentType, fixPaymentId)
                {% if venta is null or venta is empty %}

                //$(".fix-payment-btn").addClass("d-none");
                {% endif %}
                {% endif %}
            }

            calcularPagos();
        }


        function agregarTratamiento(idtratamiento) {
            var d = new Date();
            var n = d.getMilliseconds();
            var s = d.getSeconds();
            var id = n + s;
            var msj = "";
            var tratamiento = $("#tratamiento").val();
            var precio = quitarFormato($("#tratamiento-precio").val());


            if (msj == "") {
                var producto = '<tr id="producto-' + id + '" class="text-center">' +
                    '<td ><input id="' + id + '" disabled class="producto-seleccionado" data-tipo="tratamiento" data-idproducto="' + idtratamiento + '" data-descripcion="" data-nombre="' + tratamiento + '" data-precio="' + precio + '"  type="hidden" />' + tratamiento + '</td>' +
                    '<td><input id="producto-precio-' + id + '" class="form-control text-center valor-pesos" type="text" value="' + formatMoney(precio, 2, ".", ",") + '" onkeydown="calcularTotalProducto(' + id + ')" onkeyup="calcularTotalProducto(' + id + ')"> </td>' +
                    '<td><input class="form-control text-center" id="producto-cantidad-' + id + '" type="text" class="form-control text-center valor-entero" value="1" onkeydown="validarNumero(this);calcularTotalProducto(' + id + ')" onkeyup="validarNumero(this);calcularTotalProducto(' + id + ')" /></td>' +
                    '<td><input id="producto-total-' + id + '" disabled type="text" value="$' + precio + '" class="form-control text-center valor-pesos" ></td>' +
                    '<td><button class="btn btn-danger" onclick="eliminar(' + id + ');return false;"><i class="fa fa-trash-o" aria-hidden="true"></i></button>' +
                    '</td>' +
                    '</tr>';
                $("#productos").append(producto);

                $("#producto-nombre").val("");
                $("#producto-precio").val("");
                $("#producto-cantidad").val(1);
                $("#producto-total").val("");
                $("#producto-codigo").val("");
                $("#producto-nombre-id").val("");
                $("#tratamiento").val("");
                $("#tratamiento-precio").val("");
                $("#tratamiento-id").val("");
                //  calcularTotalProducto(id);
                calcularTotal();
            } else {
                Swal.fire(
                    "Producto no Encontrado: ",
                    "Intente nuevamente",
                    'warning'
                )
            }
        }

        function loadFixProducts(fixedProducts) {

            const url = $("#url-ventas-get-fixed-products").val()

            $(".producto-seleccionado").each(function () {
                var id = $(this).attr('id');
                var fixproduct = $(this).data('fixproduct');
                if (fixproduct) eliminar(id, 1)
            });

            if (fixedProducts) {
                $.ajax({
                    method: "POST",
                    data: {fixedProducts: fixedProducts},
                    url: url,
                    dataType: "json"
                }).done(function (response) {

                    for (const product of response.fixedProducsResults) {
                        const barcodeService = (product.codigo) ? product.codigo : product.codigobarrasuniversal
                        $('#producto-nombre').val(product.modelo)
                        $("#producto-precio").val(product.precio);
                        $("#producto-descripcion").val(product.descripcion);
                        $("#producto-precio").val(product.precio);
                        ponerFormatoPesos("#producto-precio");
                        $("#producto-nombre-id").val(product.data);
                        $("#producto-codigo").val(product.codigo);
                        $("#producto-codigobarras").val(barcodeService);
                        $("#producto-tipoproducto").val(product.tipoproducto);
                        $("#producto-idproducto").val(product.data);
                        $("#producto-precioespecial").val(product.precioespecial);
                        $("#producto-preciosubdistribuidor").val(product.preciosubdistribuidor);
                        $("#producto-preciodistribuidor").val(product.preciodistribuidor);
                        $("#producto-masivounico").val("");
                        $("#producto-cantidad-max").val("");
                        $("#estratamiento").val("si");

                        agregarProducto(product.data, 0, 1);
                        revisarPreciosDescuentosProductos();

                    }
                })

            }

        }

        function agregarProducto(idstock, recargar = 0, fixProduct = 0) {

            var producto = "";
            var id = curProducts;
            curProducts++;
            var msj = "";
            var productoNombre = $("#producto-nombre").val();
            var productoPrecio = quitarFormato($("#producto-precio").val());


            var productoDescripcion = $("#producto-descripcion").val();
            var tipoproducto = $("#producto-tipoproducto").val();
            var idproducto = $("#producto-idproducto").val();
            var marca = $("#producto-marca").val();
            var codigobarras = $("#producto-codigobarras").val();
            var codigobarrasuniversal = $("#producto-codigobarrasuniversal").val();
            var masivounico = $("#producto-masivounico").val();
            var categoria = $("#categoria-producto").val();
            var estratamiento = $("#estratamiento").val();

            var cantidadMax = $("#producto-cantidad-max").val();

            var precioespecial = $("#producto-precioespecial").val();
            var preciosubdistribuidor = $("#producto-preciosubdistribuidor").val();
            var preciodistribuidor = $("#producto-preciodistribuidor").val();

            const isFixed = fixProduct != 0

            const barcode = (codigobarras) ? codigobarras : codigobarrasuniversal

            let discount = 0;
            if (curCupon && curCupon.idcuponmarca) {
                if (curCupon.nombre) {
                    if (curCupon.nombre == marca) discount = curCupon.porcentajedescuento
                } else discount = curCupon.porcentajedescuento
            }

            if (checkProductos.has(barcode)) {
                msj = "El producto ya está en la lista";
                $('#producto-nombre').val("");
            } else checkProductos.add(barcode);

            if (productoNombre != "" || recargar != 0) {
                /* if(productoPrecio !=""){*/

                /* }else{
          msj="El precio del producto es necesario (No se agregó)";
        }*/
            } else {
                msj = "El nombre del producto es necesario (No se Agregó)";
            }

            checkMasivoUnico = "";
            if (masivounico == "1") checkMasivoUnico = " disabled ";

            const eraseButtonClass = (isFixed) ? "d-none" : '';

            if (msj == "") {
                var producto = '<tr id="producto-' + id + '" class="text-center">' +
                    '<td >' +

                    '<input id="' + id + '" disabled class="producto-seleccionado" data-dbproduct="false" data-tipo="' + categoria + '" data-idstockventa="" data-idproductoventa="" data-idstock="' + idstock + '" data-descripcion="' + productoDescripcion + '" data-fixproduct="' + isFixed + '" data-nombre="' + productoNombre + '" data-precio="' + productoPrecio + '" data-tipoproducto="' + tipoproducto + '" data-idproducto="' + idproducto + '" data-marca="' + marca + '"  data-codigobarras="' + codigobarras + '"  data-sku="' + codigobarrasuniversal + '"  data-masivounico="' + masivounico
                    + '"  data-estratamiento="' + estratamiento + '"  data-nombre="' + productoNombre + '"  data-precioespecial="' + precioespecial + '"  data-preciosubdistribuidor="' + preciosubdistribuidor + '"  data-preciodistribuidor="' + preciodistribuidor + '" data-omitted="false"  type="hidden" />'
                    + productoNombre + '' +
                    '</td>' +
                    '<td>' +
                    '<input id="producto-precio-' + id + '" class="form-control text-center" type="text" value="' + formatMoney(productoPrecio, 2, ".", ",") + '" onkeydown="calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="calcularTotalProducto(' + id + ');calcularTotal()"> ' +
                    '</td>' +
                    '<td>' +
                    '<input id="producto-descuento-' + id + '" class="form-control text-center" type="text" value="' + discount + '" onkeydown="calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="calcularTotalProducto(' + id + ');calcularTotal()"> ' +
                    '</td>' +
                    '<td>' +
                    '<input id="producto-final-' + id + '" class="form-control text-center" type="text" value="' + formatMoney(productoPrecio, 2, ".", ",") + '" onkeydown="calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="calcularTotalProducto(' + id + ');calcularTotal()"> ' +
                    '</td>' +
                    '<td>' +
                    '<input' + checkMasivoUnico + ' class="form-control text-center" min="1" max="' + cantidadMax + '" type="number" id="producto-cantidad-' + id + '" class="form-control text-center" value="1" onkeydown="validarNumero(this);calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="validarNumero(this);calcularTotalProducto(' + id + ');calcularTotal()" onchange="validarNumero(this);calcularTotalProducto(' + id + ');calcularTotal();revisarPreciosDescuentosProductos()"/>' +
                    '</td>' +
                    '<td>' +
                    '<input id="producto-total-' + id + '" disabled type="text" value="" class="form-control text-center" ></td>' +

                    '<td>' +(
                        (tipoproducto === '1' && isFixed === false &&
                            /armaz[oó]n|frame/i.test(categoria)
                        ) ||
                        (codigobarras === '111389607921')
                            ?
                    '<button class="btn btn-graduacion" style="background-color: #00BFFF; color: white;" ' +
                    'data-idproducto="' + idproducto +'" data-idrow="producto-' + id +'">' +
                    '<i class="fa-solid fa-glasses"></i>' +
                    '</button>' : '') +
                    '</td>' +


                    '<td><input type="checkbox" class="btn-check" name="omit" value="none" id="producto-omit-' + id + '" autocomplete="off" onchange="toggleOriginalValue(' + id + ')" >' +
                    '<label class="btn btn-outline-warning text-black border border-warning" for="producto-omit-' + id + '">¿convenio?</label></td>' +

                    '<td><button class="btn btn-danger ' + eraseButtonClass + '" onclick="eliminar(' + id + ');return false;">' +
                    '<i class="fa fa-trash-o" aria-hidden="true"></i>' +
                    '</button>' +
                    '</td>' +
                    '</tr>';
                $("#productos").append(producto);

                $("#producto-nombre").val("");
                $("#producto-precio").val("");
                $("#producto-cantidad").val(1);
                $("#producto-total").val("");
                $("#producto-codigo").val("");
                $("#producto-nombre-id").val("");
                $("#producto-tipoproducto").val("");
                $("#producto-idproducto").val("");
                $("#producto-marca").val("");
                $("#producto-codigobarras").val("");
                calcularTotalProducto(id);
                calcularTotal();

                // Establecer automáticamente stage pendiente si el producto tiene graduación (SOLO PRODUCTOS NUEVOS)
                if ((tipoproducto === '1' && isFixed === false && /armaz[oó]n|frame/i.test(categoria)) ||
                    (codigobarras === '111389607921')) {
                    console.log("Producto NUEVO con graduación detectado, estableciendo stage pendiente automático");
                    establecerStagePendienteAutomatico(idproducto, "producto-" + id, false); // false = crear graduación nueva
                }

                recargarProductos();
            } else {
                //$("#msj-error-armazon-codigo").html(msj);
                Swal.fire(
                    msj,
                    "Intente nuevamente",
                    'warning'
                )
            }
            comprobarSiHayProductosSeleccionados();
            revisarPreciosDescuentosProductos();

        }

        function mostrarIdVenta(btn) {
            const idVenta = $(btn).data('idventa');
            if (idVenta) {
                Swal.fire({
                    title: 'ID de la Venta',
                    text: 'El ID de esta venta es: ' + idVenta,
                    icon: 'info',
                    confirmButtonColor: '#00BFFF'
                });
            } else {
                Swal.fire({
                    title: 'Información',
                    text: 'No hay ID de venta disponible aún',
                    icon: 'info',
                    confirmButtonColor: '#00BFFF'
                });
            }
        }

        function comprobarSiHayProductosSeleccionados() {
            let hayProductos = false;

            $(".producto-seleccionado").each(function () {
                hayProductos = true;
            });
            if (hayProductos) {
                $(".tabla-productos-seleccionados").removeClass("d-none");
            } else {
                $(".tabla-productos-seleccionados").addClass("d-none");
            }

        }

        function toggleOriginalValue(id) {

            var checkboxInput = $("#producto-omit-" + id);
            var checkboxValue = checkboxInput.val() == "none" ? $("#" + id).data('precio') : checkboxInput.val();
            // console.log("Checboxvalue", checkboxValue);
            // console.log("Checboxvalue", checkboxInput);

            var precioInput = $("#producto-precio-" + id);
            var precioValue = precioInput.val();
            //console.log(precioValue);

            checkboxInput.val(precioValue);
            precioInput.val(checkboxValue);

            $("#producto-final-" + id).val(precioInput.val());

            var overriddenStatus = $("#" + id).data('omitted') == false ? true : false;

            $("#producto-descuento-" + id).val(0);
            $("#producto-descuento-" + id).prop('disabled', !overriddenStatus);

            $("#" + id).data('omitted', overriddenStatus);
            //console.log($("#" + id).data('omitted'));

            revisarPreciosDescuentosProductos();
            calcularTotal();
        }


        function eliminar(id, recargar = 0, basePrice = null) {
            var productoTotal = 0;
            var productoIva = 0;
            var productoSubtotal = 0;
            var total = 0;
            var iva = 0;
            var subtotal = 0;

            if (servicesQuantities.has(parseInt(id))) servicesQuantities.delete(parseInt(id))
            if (storablesQuantities.has(parseInt(id))) storablesQuantities.delete(parseInt(id))

            if (recargar == 0) {
                Swal.fire({
                    title: '¿Está seguro?',
                    text: "¡Se eliminará el producto de la lista!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: '¡Sí, Borrarlo!'
                }).then((result) => {
                    if (result.value) {

                        var codigobarrasValue = $('#' + id).data('codigobarras');
                        var codigobarrasUniversalValue = $('#' + id).data('sku');

                        const barcode = (codigobarrasValue) ? codigobarrasValue : codigobarrasUniversalValue

                        checkProductos.delete(barcode.toString());

                        $("#producto-" + id).remove();
                        calcularTotal();
                        comprobarSiHayProductosSeleccionados();
                        revisarPreciosDescuentosProductos();

                        swal.close();
                        /* Swal.fire(
              'Listo!',
              'Eliminado Exitosamente.',
              'success'
            )*/
                    }
                })
            } else {

                var codigobarrasValue = $('#' + id).data('codigobarras');

                checkProductos.delete(codigobarrasValue.toString());

                $("#producto-" + id).remove();
                calcularTotal();
                comprobarSiHayProductosSeleccionados();
                revisarPreciosDescuentosProductos();

                if (basePrice) {
                    Swal.fire({
                        title: "¡Atención!",
                        text: "Se quitará el producto porque requiere un " + basePrice,
                        icon: "warning"
                    });
                }

            }


        }


        function formatMoney(amount, decimalCount = 2, decimal = ".", thousands = ",") {
            try {
                decimalCount = Math.abs(decimalCount);
                decimalCount = isNaN(decimalCount) ? 2 : decimalCount;

                const negativeSign = amount < 0 ? "-" : "";

                let i = parseInt(amount = Math.abs(Number(amount) || 0).toFixed(decimalCount)).toString();
                let j = (i.length > 3) ? i.length % 3 : 0;

                return negativeSign + (j ? i.substr(0, j) + thousands : '') + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousands) + (decimalCount ? decimal + Math.abs(amount - i).toFixed(decimalCount).slice(2) : "");
            } catch (e) {
                //console.log(e)
            }
        }
    </script>
    <script>
        input: 'text',
            function imprimir2() {
                PrintElem("ticket");

            }

        function PrintElem(elem) {
            var mywindow = window.open('', 'PRINT', 'height=400,width=800');
            mywindow.document.write('<html><head><title>' + document.title + '</title>');
            mywindow.document.write('</head><body >');
            mywindow.document.write('<h1>' + document.title + '</h1>');
            mywindow.document.write(document.getElementById(elem).innerHTML);
            mywindow.document.write('</body></html>');
            mywindow.document.close(); // necessary for IE >= 10
            mywindow.focus(); // necessary for IE >= 10*/
            mywindow.print();
            mywindow.close();

            return true;
        }


        function revisarPreciosDescuentosProductos() {

            var descuentoproductosalmacenables = parseFloat($("#descuentoproductosalmacenables").val()) || 0;
            var descuentoservicios = parseFloat($("#descuentoservicios").val()) || 0;
            var preciofijoproductosalmacenables = parseFloat($("#preciofijoproductosalmacenables").val()) || 0;
            var preciofijoservicios = parseFloat($("#preciofijoservicios").val()) || 0;



            {% if venta is null or venta is empty or venta.cotizacion == '1' %}


            if (preciofijoservicios >= 0) {
                countServices();
                const totalServices = Array.from(servicesQuantities.values()).reduce((acc, curr) => acc + curr, 0);

                var individualFixServicePrice = (totalServices > 0)
                    ? preciofijoservicios / totalServices
                    : 0;
            }

            if (preciofijoproductosalmacenables > 0) {

                countStorables();
                const totalStorables = Array.from(storablesQuantities.values()).reduce((acc, curr) => acc + curr, 0);

                let totalAuditivos = 0;
                $(".producto-seleccionado[data-tipoproducto='1']").each(function () {
                    const cat = $(this).data("tipo");
                    const omitted = $(this).data("omitted") ?? false;
                    if (!omitted && cat === "Auditivo") {
                        totalAuditivos++;
                    }
                });

                var individualFixAuditivoPrice = (totalAuditivos > 0)
                    ? (preciofijoproductosalmacenables / totalAuditivos)
                    : 0;
            }

            {% endif %}


            $(".producto-seleccionado").each(function () {

                var precio = parseFloat($(this).data("precio")) || 0;
                var categoria = $(this).data("tipo");
                var tipoproducto = $(this).data("tipoproducto");
                var id = $(this).attr("id");
                var masivounico = $(this).data("masivounico");
                var dbproduct = $(this).data("dbproduct");
                var omitted = $(this).data("omitted") ?? false;

                // Solo aplicamos si NO está omitido
                if (!omitted) {
                    // A) PRODUCTOS ALMACENABLES
                    if (tipoproducto == "1") {

                        // Aplicamos descuento masivo a almacenables, si existe
                        if (descuentoproductosalmacenables > 0) {
                            $("#producto-descuento-" + id).val(descuentoproductosalmacenables);
                        }

                        // Solo si es cotización o venta no final
                        {% if venta is null or venta is empty or venta.cotizacion == '1' %}

                        // Si hay precio fijo > 0, no es DB product y masivounico = 1 o 2
                        if (preciofijoproductosalmacenables > 0 && !dbproduct && (masivounico == 1 || masivounico == 2)) {

                            if (categoria === "Auditivo") {
                                // 1) Auditivo => dividir
                                $("#producto-precio-" + id).val(individualFixAuditivoPrice.toFixed(2));
                            } else if (categoria === "ARMAZÓN") {
                                // 2) Armazón => precio fijo completo
                                $("#producto-precio-" + id).val(preciofijoproductosalmacenables.toFixed(2));
                            } else {
                                // 3) Cualquier otra categoría => tal vez dejas su precio original
                                //    o defines otra regla
                                $("#producto-precio-" + id).val(precio.toFixed(2));
                            }
                        }

                        // Si precio fijo >= 0 y masivounico=1, bloqueamos fields
                        /*if (preciofijoproductosalmacenables >= 0 && masivounico == 1) {
                            $("#producto-descuento-" + id).prop('disabled', true);
                            $("#producto-precio-" + id).prop('disabled', true);
                        } else {
                            // Si no hay cupon guardado
                            if (!savedVentaCupon) {
                                $("#producto-descuento-" + id).prop('disabled', false);
                            }
                            $("#producto-precio-" + id).prop('disabled', false);
                        }*/

                        {% endif %}

                        // B) SERVICIOS
                    } else if (tipoproducto == "2") {
                        if (descuentoservicios > 0) {
                            $("#producto-descuento-" + id).val(descuentoservicios);
                        }

                        {% if venta is null or venta is empty or venta.cotizacion == '1' %}
                        // Para servicios, usamos el precio dividido que calculamos arriba
                        if (preciofijoservicios >= 0 && !dbproduct) {
                            $("#producto-precio-" + id).val(individualFixServicePrice);
                            // Borramos descuento (opcional, según tu lógica actual)
                            $("#producto-descuento-" + id).val('');
                        }
                        if (preciofijoservicios >= 0) {
                            $("#producto-descuento-" + id).prop('disabled', true);
                            $("#producto-precio-" + id).prop('disabled', true);
                        } else {
                            if (!savedVentaCupon) {
                                $("#producto-descuento-" + id).prop('disabled', false);
                            }
                            $("#producto-precio-" + id).prop('disabled', false);
                        }
                        {% endif %}
                    }

                    // C) Recalcula el total de cada producto y luego el total global
                    calcularTotalProducto(id);
                    calcularTotal();
                }
            });
        }


        function cambiarFormulario(select) {
            var idtipoventa = $("#convenio").val();

            pagoalfinal = $('option:selected', select).data('pagoalfinal');

            $("#pagoAlFinal").val(pagoalfinal);

            //debemos traer la infromación para poder hacer los claculos

            var url = $("#url-obtener-informacion-tipo-venta").val();
            $.ajax({
                method: "POST",
                data: {idtipoventa: idtipoventa},
                url: url,
                beforeSend: function (xhr) {

                }
            }).done(function (response) {
                if (response.exito) {

                    let paymentType = response.Tipoventa.paymenttypeIdpaymenttype
                    let fixedProducts = response.Tipoventa.fixproducts

                    {% if venta is null or venta is empty %}
                    loadFixProducts(fixedProducts)
                    checkPoli(response.Tipoventa.idtipoventa, "121377612614")
                    {% endif %}

                    if (paymentType) {
                        removePayments()
                        $("#fix-payment-type").val(paymentType.name)
                        $("#fix-payment-id").val(paymentType.idpaymenttype)
                        //$(".fix-payment-btn").addClass("d-none")
                    } else {
                        tempPaymentId = $("#fix-payment-id").val()

                        if (tempPaymentId) {
                            removePayments()
                            $("#fix-payment-type").val('')
                            $("#fix-payment-id").val('')
                        }

                        //$(".fix-payment-btn").removeClass("d-none")
                    }


                    let fixpriceServicios = response.Tipoventa.preciofijoservicios
                    let fixpriceAlmacenables = response.Tipoventa.preciofijoproductosalmacenables

                    fixpriceServicios = (fixpriceServicios) ? fixpriceServicios : -1;
                    fixpriceAlmacenables = (fixpriceAlmacenables) ? fixpriceAlmacenables : -1;

                    {% if venta is null or venta is empty or venta.cotizacion == '1' %}
                    if (fixpriceServicios != -1 || fixpriceAlmacenables != -1) {
                        quitarCupon()
                        $("#cupon-descuento").attr("disabled", true);
                        $(".agregar-cupon").attr("disabled", true);
                    } else {
                        if (!savedVentaCupon) $("#cupon-descuento").attr("disabled", false);
                        $(".agregar-cupon").attr("disabled", false);
                    }
                    {% endif %}



                    if (fixpriceServicios == -1 || fixpriceAlmacenables == -1) response.Tipoventa.totalventaconiva = -1;
                    else response.Tipoventa.totalventaconiva = fixpriceServicios + fixpriceAlmacenables;

                    total = parseFloat(response.Tipoventa.totalventaconiva);
                    iva = (total * ivaValue) / (1 + ivaValue);
                    subtotal = total - iva;


                    $("#totalventaiva").val(total);
                    $("#ivafijo").val(iva);
                    $("#subtotalfijo").val(subtotal);


                    $("#documentoobligatoriocerrarventa").val(response.Tipoventa.documentoobligatoriocerrarventa);
                    $("#preciobase").val(response.Tipoventa.preciobase);

                    //console.log("LenonciniPrecioBase, preciobase: " + response.Tipoventa.preciobase);

                    var notaConSaltosDeLinea = "";
                    if (response.Tipoventa.nota != null) {
                        notaConSaltosDeLinea = response.Tipoventa.nota.replace(/\n/g, "<br>");
                    }

                    /*if (response.Tipoventa.mostrarcampogarantia == 1){
          $("#select-empresas").removeClass("d-none");
          $("#contenedor-folio-venta-garantia").removeClass("d-none");
        }else{
          $("#select-empresas").addClass("d-none");
          $("#contenedor-folio-venta-garantia").addClass("d-none");
        }*/

                    $("#tipo-venta-nota").html(notaConSaltosDeLinea);
                    $("#msj-tipo-venta").html(notaConSaltosDeLinea);
                    $("#tipo-venta-nota").html(response.Tipoventa.nota);
                    //$("#msj-tipo-venta").html(response.Tipoventa.nota);
                    $("#descuentoproductosalmacenables").val(response.Tipoventa.descuentoproductosalmacenables);
                    $("#descuentoservicios").val(response.Tipoventa.descuentoservicios);
                    $("#preciofijoproductosalmacenables").val(fixpriceAlmacenables);
                    $("#preciofijoservicios").val(fixpriceServicios);

                    recargarProductos();
                    revisarPreciosDescuentosProductos();

                } else {
                    $("#msj-tipo-venta").html(response.msj);
                }

            }).fail(function (jqXHR, textStatus) {

                Swal.fire(
                    "Error al obtener datos del tipo de venta ",
                    JSON.stringify(jqXHR) + JSON.stringify(textStatus),
                    'warning'
                )
            });


            $(".datos-extra").addClass("d-none");
            valor = $(select).val();
            mostrarnumeroempleado = $('option:selected', select).data('mostrarnumeroempleado');
            mostrarbeneficiario = $('option:selected', select).data('mostrarbeneficiario');
            mostrarunidadprocedencia = $('option:selected', select).data('mostrarunidadprocedencia');
            mostrarcampogarantia = $('option:selected', select).data('mostrarcampogarantia');

            /*var notaConSaltosDeLinea = "";
          if(response.Tipoventa.nota != null){
             notaConSaltosDeLinea = response.Tipoventa.nota.replace(/\n/g, "<br>");
          }*/

            if (mostrarnumeroempleado == "1") {
                $("#contenedor-numero-empleado").removeClass("d-none");
            }
            if (mostrarbeneficiario == "1") {
                $("#contenedor-beneficiario").removeClass("d-none");
            } else $("#contenedor-beneficiario").addClass("d-none");
            if (mostrarunidadprocedencia == "1") {
                $("#contenedor-unidad-procedencia").removeClass("d-none");
            }
            if (mostrarcampogarantia == "1") {
                $("#title-warranty").removeClass("d-none");
                $("#select-empresas").removeClass("d-none");
                $("#contenedor-folio-venta-garantia").removeClass("d-none");
            }

        }

        function checkPoli(idSaleType, barcode) {

            const url = $("#ventas-check-poli").val()

            if (idSaleType == 1748) {
                $.ajax({
                    method: "POST",
                    data: {barcode: barcode, idSaleType: idSaleType},
                    url: url,
                    dataType: "json"
                }).done(function (response) {

                    if (response.success) {
                        const product = response.product
                        const barcodeService = (product.codigo) ? product.codigo : product.codigobarrasuniversal
                        $('#producto-nombre').val(product.modelo)
                        $("#producto-precio").val(product.precio);
                        $("#producto-descripcion").val(product.descripcion);
                        $("#producto-precio").val(product.precio);
                        ponerFormatoPesos("#producto-precio");
                        $("#producto-nombre-id").val(product.data);
                        $("#producto-codigo").val(product.codigo);
                        $("#producto-codigobarras").val(barcodeService);
                        $("#producto-tipoproducto").val(product.tipoproducto);
                        $("#producto-idproducto").val(product.data);
                        $("#producto-precioespecial").val(product.precioespecial);
                        $("#producto-preciosubdistribuidor").val(product.preciosubdistribuidor);
                        $("#producto-preciodistribuidor").val(product.preciodistribuidor);
                        $("#producto-masivounico").val("");
                        $("#producto-cantidad-max").val("");
                        $("#estratamiento").val("si");

                        agregarProducto(product.data, 0);
                        revisarPreciosDescuentosProductos();

                    }

                })

            } else {
                $(".producto-seleccionado").each(function () {
                    const id = $(this).attr('id');
                    const codigobarras = $(this).data('codigobarras');
                    if (codigobarras == barcode) eliminar(id, 1)
                });
            }

        }

        function removePayments() {
            {% if venta is null or venta is empty %} //or venta.cotizacion == '1' %}
            $("#tabla-body-pagos").html('')
            calcularPagos();
            {% endif %}
        }

        function recargarProductos() {

            var preciofijoproductosalmacenables = $("#preciofijoproductosalmacenables").val();
            var preciofijoservicios = $("#preciofijoservicios").val();

            $(".producto-seleccionado").each(function () {
                const id = $(this).attr('id');

                const preciobase = $("#preciobase").val();

                //eliminar(id,1);
                const codigoDeBarrasValue = $(this).data('codigobarras');
                const codigobarrasuniversal = $(this).data('sku');

                const idproductoServicio = $(this).data('idproducto');

                const precioespecial = $(this).data('precioespecial');
                const preciosubdistribuidor = $(this).data('preciosubdistribuidor');
                const preciodistribuidor = $(this).data('preciodistribuidor');
                const precio = $(this).data('precio');
                const dbproduct = $(this).data('dbproduct');
                const tipoproducto = $(this).data('tipoproducto');
                const masivounico = $(this).data('masivounico');

                var productoPrecio = precioespecial > 0 ? precioespecial : precio;
                const hasFixedPrice = (tipoproducto == 1 && preciofijoproductosalmacenables > 0) || (tipoproducto == 2 && preciofijoservicios >= 0)

                if (!dbproduct) {

                    barcode = (codigoDeBarrasValue) ? codigoDeBarrasValue : codigobarrasuniversal

                    //console.log("preciobase:", preciobase);
                    //console.log("preciofijoproductosalmacenables:", preciofijoproductosalmacenables);
                    //console.log("preciofijoservicios:", preciofijoservicios);
                    //console.log("precioespecial:", precioespecial);
                    //console.log("preciosubdistribuidor:", preciosubdistribuidor);
                    //console.log("preciodistribuidor:", preciodistribuidor);
                    //console.log("precio:", precio);
                    //console.log("productoPrecio:", productoPrecio);
                    //console.log("dbproduct:", dbproduct);
                    //console.log("tipoproducto:", tipoproducto);
                    //console.log("masivounico:", masivounico);
                    //console.log("hasFixedPrice:", hasFixedPrice);


                    if (preciobase == "1" && !hasFixedPrice) {

                        if (precioespecial == null || precioespecial == 0) {
                            productoPrecio = precio;

                        }

                        if (!productoPrecio) eliminar(id, 1, "precio especial")
                        else $("#producto-precio-" + id).val(productoPrecio);

                    } else if (preciobase == "2" && !hasFixedPrice) {

                        if (!preciodistribuidor) eliminar(id, 1, "precio distribuidor")
                        else $("#producto-precio-" + id).val(preciodistribuidor);


                    } else if (preciobase == "3" && !hasFixedPrice) {

                        if (!preciosubdistribuidor) eliminar(id, 1, "precio subdistribuidor")
                        else $("#producto-precio-" + id).val(preciosubdistribuidor);

                    } else {
                        $("#producto-precio-" + id).val(precio);
                        //console.log("PRECIO:", precio);
                    }
                } else {
                    $("#producto-precio-" + id).val(precio);
                }

                calcularTotalProducto(id);
                calcularTotal();

            });
        }


        function buscarProductoPorNombre(nombre) {

            var urlBuscarProductoNombre = $("#url-buscar-producto-nombre").val();
            var preciobase = $("#preciobase").val();
            $.ajax({
                method: "POST",
                data: {query: nombre},
                url: urlBuscarProductoNombre,
                dataType: "json",
                beforeSend: function (xhr) {

                }
            }).done(function (response) {

                suggestion = response.suggestions[0]
                $("#producto-nombre").val(nombre);
                $("#producto-precio").val(suggestion.precio);
                $("#producto-descripcion").val(suggestion.descripcion);
                //$("#producto-precio").val(suggestion.precio);


                ponerFormatoPesos("#producto-precio");
                $("#producto-nombre-id").val(suggestion.data);
                $("#producto-codigo").val(suggestion.codigo);
                $("#producto-codigobarras").val(suggestion.codigo);
                $("#producto-tipoproducto").val(suggestion.tipoproducto);
                $("#producto-idproducto").val(suggestion.idproducto);
                //calcularTotal();
                agregarProducto(suggestion.data);

            })


        }


        function delay(callback, ms) {
            var timer = 0;
            return function () {
                var context = this, args = arguments;
                clearTimeout(timer);
                timer = setTimeout(function () {
                    callback.apply(context, args);
                }, ms || 0);
            };
        }


        function buscarCotizacion() {

            var numeroFolio = quitarFormato($("#busqueda-numero-folio").val());
            var urlNuevaVenta = $("#url-nueva-venta").val();
            var urlBuscarVenta = $("#url-buscar-venta").val();

            var idempresa = $("#permisosempresas").val();


            reset();

            $.ajax({
                method: "POST",
                data: {numeroFolio: numeroFolio, idempresa: idempresa},
                url: urlBuscarVenta,
                beforeSend: function (xhr) {

                }
            }).done(function (response) {
                if (response.exito) {
                    document.location.href = urlNuevaVenta + "/" + numeroFolio + "?idempresa=" + response.empresa;
                } else {
                    $("#modal-busqueda-msj").html(response.msj);
                }
            }).fail(function (jqXHR, textStatus) {

                Swal.fire(
                    "Error al buscar el ticket: ",
                    JSON.stringify(jqXHR) + JSON.stringify(textStatus),
                    'warning'
                )
            });


        }


        function agregarProductoDB(productoNombre, productoPrecio, productoDescripcion, descuento, modelo, marca, tipoproducto, idproducto, idproductoventa,
                                   idstockventa, idstock, marca, codigobarras, cantidad, masivoUnico, cantidadMax, codigobarrasuniversal, codigo, fixproduct, omitted, categoria) {
            var producto = "";
            var id = curProducts;
            curProducts++;
            var msj = "";
            var esCotizacion = $("#escotizacion").val();
            var idventa = $("#idventa").val();
            //para quitar la opcion de eliminra si es venta
            barcode = (codigobarras) ? codigobarras : codigobarrasuniversal
            barcode = (barcode) ? barcode : codigo

            if (checkProductos.has(barcode)) {

                msj = "El producto ya está en la lista";
                $('#producto-nombre').val("");
            } else checkProductos.add(barcode);

            var disabled = "";

            if (esCotizacion != "1" && idventa != "") {
                disabled = "disabled";
            }

            var disabledQuantity = "";

            if (esCotizacion != "1" || tipoproducto == "1") {
                disabledQuantity = "disabled";
            }

            var productoDescripcion = "";
            if (tipoproducto == "2") {
                productoDescripcion = modelo
            } else {
                productoDescripcion = marca + " / " + modelo + " / " + productoDescripcion;
            }

            /*var productoNombre=$("#producto-nombre").val();
            var productoPrecio=quitarFormato($("#producto-precio").val());

            var productoDescripcion=$("#producto-descripcion").val();*/

            if (productoNombre != "") {
                if (productoPrecio != "") {

                } else {
                    msj = "El precio del producto es necesario (No se agregó)";
                }
            } else {
                msj = "El nombre del producto es necesario (No se Agregó)";
            }

            const boolFixProduct = (fixproduct == '1') ? true : false
            const isFixed = boolFixProduct;

            cantidadMax = parseInt(cantidadMax) + parseInt(cantidad);

            omittedValue = omitted == '1';

            if (msj == "") {
                var producto = '<tr id="producto-' + id + '" class="text-center">' +

                    '<td >' +

                    '<input id="' + id + '" disabled class="producto-seleccionado" data-dbproduct="true" data-fixproduct="' + boolFixProduct + '" data-idstockventa="' + idstockventa + '" data-idstock="' + idstock + '" data-idproductoventa="' + idproductoventa + '" data-tipo="' + masivoUnico + '"  data-idproducto="' + idproducto + '" data-descripcion="' + productoDescripcion + '" data-nombre="' + productoNombre + '" data-precio="' + productoPrecio + '"   data-tipoproducto="' + tipoproducto + '" data-marca="' + marca + '" data-codigobarras="' + barcode + '"  data-masivounico="' + masivoUnico + '"  data-omitted="' + omittedValue + '" type="hidden" />'
                    + productoDescripcion + '' + codigobarras +
                    '</td>' +
                    '<td>' +
                    '<input id="producto-precio-' + id + '" class="form-control text-center" type="text" ' + disabled + ' value="' + formatMoney(productoPrecio, 2, ".", ",") + '" onkeydown="calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="calcularTotalProducto(' + id + ');calcularTotal()"> ' +
                    '</td>' +
                    '<td>' +
                    '<input id="producto-descuento-' + id + '" class="form-control text-center" ' + disabled + ' type="text" value="' + descuento + '" onkeydown="calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="calcularTotalProducto(' + id + ');calcularTotal()"> ' +
                    '</td>' +
                    '<td>' +
                    '<input id="producto-final-' + id + '" class="form-control text-center" type="text" ' + disabled + ' value="' + formatMoney(productoPrecio, 2, ".", ",") + '" onkeydown="calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="calcularTotalProducto(' + id + ');calcularTotal()"> ' +
                    '</td>' +
                    '<td>' +
                    '<input ' + disabledQuantity + ' class="form-control text-center" min="1" max="' + cantidadMax + '" type="number" id="producto-cantidad-' + id + '" class="form-control text-center" value="' + cantidad + '" onkeydown="validarNumero(this);calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="validarNumero(this);calcularTotalProducto(' + id + ');calcularTotal()" onchange="validarNumero(this);calcularTotalProducto(' + id + ');calcularTotal();revisarPreciosDescuentosProductos()"/>' +
                    '</td>' +
                    '<td>' +
                    '<input id="producto-total-' + id + '" disabled type="text" ' + disabled + ' value="" class="form-control text-center" ></td>' +

                    '<td>' +
                    (
                        (tipoproducto === '1' && isFixed === false &&
                            /armaz[oó]n|frame/i.test(categoria)
                        ) ||
                        (barcode === '111389607921') ||
                        // Mostrar el botón si el producto ya tiene graduación
                        (graduacionesPendientes && (graduacionesPendientes[idproducto] || graduacionesPendientes["producto-" + id]))
                            ?
                    '<button class="btn btn-graduacion" style="background-color: #00BFFF; color: white;" ' +
                    'data-idproducto="' + idproducto + '" data-idrow="producto-' + id + '">' +
                    '<i class="fa-solid fa-glasses"></i>' +
                    '</button>' : '') +
                    '</td>'
                ;
                if (esCotizacion == "1" && idventa != "" && !boolFixProduct) {
                    producto += '<td><input type="checkbox" class="btn-check" name="omit" value="none" id="producto-omit-' + id + '" autocomplete="off" onchange="toggleOriginalValue(' + id + ')" ' + (omittedValue ? "checked" : "") + '>' +
                        '<label class="btn btn-outline-warning text-black border border-warning" for="producto-omit-' + id + '">¿convenio?</label></td>' +

                        '<td><button class="btn btn-danger" onclick="eliminar(' + id + ');return false;"><i class="fa fa-trash-o" aria-hidden="true"></i>' +
                        '</button>';
                }

                producto +=
                    '</td>' +
                    '</tr>';
                $("#productos").append(producto);

                $("#producto-nombre").val("");
                $("#producto-precio").val("");
                $("#producto-cantidad").val(1);
                $("#producto-total").val("");
                $("#producto-codigo").val("");
                $("#producto-nombre-id").val("");

                //console.log("aqui");
                calcularTotalProducto(id);
                calcularTotal();

                // Para productos existentes, solo aplicar color si ya tienen graduación en memoria
                const tieneBotonGraduacion = (tipoproducto === '1' && !boolFixProduct && /armaz[oó]n|frame/i.test(categoria)) ||
                                           (codigobarras === '111389607921');

                if (tieneBotonGraduacion) {
                    console.log("Producto DB con graduación detectado, verificando graduación existente");
                    // Solo aplicar color, NO crear graduación automática para productos existentes
                    establecerStagePendienteAutomatico(idproducto, "producto-" + id, true); // true = solo aplicar color si existe
                }
            } else {
                //$("#msj-error-armazon-codigo").html(msj);
                /*Swal.fire(
                msj,
          "Intente nuevamente" ,
          'warning'
        )*/
            }
            comprobarSiHayProductosSeleccionados();
            revisarPreciosDescuentosProductos();
        }

        function agregarPagoDB(anticipo, tipoPagoAnticipo, idpaymentType, idpago, isAutomatic) {
            /*let anticipo=parseFloat(quitarFormato($("#anticipo").val()));
      let tipoPagoAnticipo=$("#tipo-pago-anticipo").val();*/
            let id = Date.now() + Math.floor(Math.random() * 100);
            if (anticipo > 0) {
                if (tipoPagoAnticipo != "") {
                    $("#tabla-body-pagos").append('<tr id="' + id + '">\n' +
                        '                            <td>' +

                        '<input id="valor-' + id + '" value="' + anticipo + '" type="text" class="form-control-plaintext valor-pesos pagos" data-idpago="' + idpago + '" data-tipopagoanticipo="' + idpaymentType + '" readonly></td>\n' +
                        '                            <td>' + tipoPagoAnticipo + '</td>\n' +
                        '                            <td>\n' +
                        (isAutomatic == '1' ? '<i class="fa fa-lock" aria-hidden="true"></i>' :
                            ('<button class="btn "  style="background:#fff"  onclick="quitarPago(' + id + ')">\n' +
                                '<img src="/img/icon-remove.jpg" alt="" width="25">\n' +
                                '</button>\n')) +

                        '                            </td>\n' +
                        '                          </tr>');
                    ponerFormatoPesos("#valor-" + id);
                    calcularPagos();
                    $("#anticipo").val("");
                    $("#tipo-pago-anticipo").val("");
                } else {
                    $("#tipo-pago-anticipo").focus();
                    Swal.fire(
                        'Campo(s) vacios!',
                        'debe seleccionar un tipo de pago!',
                        'warning'
                    );
                }
            } else {
                $("#anticipo").focus();
                Swal.fire(
                    'Campo(s) vacios!',
                    'La cantidad debe ser mayor a cero!',
                    'warning'
                );
            }


        }

        function buscarCupon() {

            var cuponDescuento = $("#cupon-descuento").val();
            var url = $("#url-buscar-cupon").val();

            $.ajax({
                method: "POST",
                data: {cuponDescuento: cuponDescuento},
                url: url,
                dataType: "json",
                beforeSend: function (xhr) {

                }
            }).done(function (response) {

                if (response.exito) {

                    curCupon = response.marcas[0];

                    //console.log(curCupon)

                    $(".producto-seleccionado").each(function () {
                        id = $(this).attr("id");
                        if (curCupon && curCupon.nombre) {
                            nombreMarca = $(this).data("marca");
                            if (curCupon.nombre == nombreMarca) {
                                //aplicamos el descuento
                                $("#producto-descuento-" + id).val(0);
                                //console.log("aqui no");
                                calcularTotalProducto(id);
                                calcularTotal();
                            }
                        } else {
                            $("#producto-descuento-" + id).val(0);
                            //console.log("aqui tampoco");
                            calcularTotalProducto(id);
                            calcularTotal();
                        }
                    });

                    $(".producto-seleccionado").each(function () {
                        id = $(this).attr("id");
                        if (curCupon && curCupon.nombre) {
                            nombreMarca = $(this).data("marca");
                            if (curCupon.nombre == nombreMarca) {
                                //aplicamos el descuento
                                $("#producto-descuento-" + id).val(curCupon.porcentajedescuento);
                                $("#producto-descuento-" + id).prop("disabled", true)
                                //console.log("aqui menos");
                                calcularTotalProducto(id);
                                calcularTotal();
                            }
                        } else {
                            $("#producto-descuento-" + id).val(curCupon.porcentajedescuento);
                            $("#producto-descuento-" + id).prop("disabled", true)
                            //console.log("hijole");
                            calcularTotalProducto(id);
                            calcularTotal();
                        }
                    });

                    $(".agregar-cupon").addClass("d-none");
                    $(".quitar-cupon").removeClass("d-none");
                    $("#cupon-descuento").attr("disabled", true);
                } else {
                    $("#cupon-descuento").val("");
                    Swal.fire(
                        response.msj,
                        '',
                        'warning'
                    );
                }

            }).fail(function (jqXHR, textStatus) {

                Swal.fire(
                    "Error al buscar el ticket: ",
                    JSON.stringify(jqXHR) + JSON.stringify(textStatus),
                    'warning'
                )
            });


        }

        function quitarCupon() {

            removeDiscounts()

            savedVentaCupon = null;

            $("#cupon-descuento").attr("disabled", false);
            $(".agregar-cupon").removeClass("d-none");
            $(".quitar-cupon").addClass("d-none");
            $("#cupon-descuento").val("");

            /*
      var cuponDescuento=$("#cupon-descuento").val();


      var url=$("#url-quitar-cupon").val();

      var idventa=$("#idventa").val();
      if(idventa !=""){
        $.ajax({
          method: "POST",
          data:{idventa:idventa,cuponDescuento:cuponDescuento},
          url: url,
          dataType:"json",
          beforeSend: function( xhr ) {

          }
        }).done(function( response ) {
          if(response.exito) {

            removeDiscounts()

            $("#cupon-descuento").attr("disabled",false);
            $(".agregar-cupon").removeClass("d-none");
            $(".quitar-cupon").addClass("d-none");
            $("#cupon-descuento").val("");
          }else{
            Swal.fire(
                    response.msj,
                    '',
                    'warning'
            );
          }

        }).fail(function( jqXHR, textStatus ) {

          Swal.fire(
                  "Error al buscar el ticket: " ,
                  JSON.stringify(jqXHR)+JSON.stringify(textStatus) ,
                  'warning'
          )
        });
      }else{

        removeDiscounts()

        $("#cupon-descuento").attr("disabled",false);
        $(".agregar-cupon").removeClass("d-none");
        $(".quitar-cupon").addClass("d-none");
        $("#cupon-descuento").val("");
      }*/
        }

        function removeDiscounts() {

            $(".producto-seleccionado").each(function () {
                id = $(this).attr("id");
                tipoproducto = $(this).data("tipoproducto");

                if (curCupon && curCupon.nombre) {
                    nombreMarca = $(this).data("marca");
                    if (curCupon.nombre == nombreMarca) {
                        $("#producto-descuento-" + id).val(0);
                        $("#producto-descuento-" + id).prop("disabled", false)
                        //console.log("descuento");
                        calcularTotalProducto(id);
                        calcularTotal();
                    }
                } else {
                    $("#producto-descuento-" + id).val(0);
                    $("#producto-descuento-" + id).prop("disabled", false)
                    //console.log("descuento no");
                    calcularTotalProducto(id);
                    calcularTotal();
                }
            });
            curCupon = {}
        }


        function cargarMeses() {

            const selectedOption = $("#tipo-pago-anticipo").children('option:selected');
            const paymentName = selectedOption.data('payment-name');

            let total = quitarFormato($("#total").val());
            let anticipo = quitarFormato($("#anticipo-total-pagado").val());

            const anticipoElement = document.getElementById('anticipo');

            let totallimit = parseFloat(quitarFormato($("#total-limit").val()));

            let pagoAlFinal = $("#pagoAlFinal").val();

            const convenio = document.getElementById('anticipo-restan');

            if (anticipoElement) {
                if (paymentName == "Tarjeta de crédito") {
                    //anticipoElement.value = (pagoAlFinal == '1' ? totallimit : total ) - anticipo;
                    //anticipoElement.disabled = true;
                    ponerFormatoPesos("#anticipo");
                    $("#meses").removeClass("d-none");
                    curMSI = true;

                } else {
                    anticipoElement.value = "";
                    anticipoElement.disabled = false;
                    $("#meses").addClass("d-none");
                    curMSI = false;
                }
            } else {
                console.error('El elemento con id="anticipo" no existe en el DOM.');
            }
        }


        function buscarVenta() {

            var numeroFolio = quitarFormato($("#folio-venta-garantia").val());

            //var idempresa = $("#empresas").val();

            idempresa = -1;

            var urlBuscarVenta = $("#url-buscar-venta").val();

            $.ajax({
                method: "POST",
                data: {numeroFolio: numeroFolio, idempresa: idempresa, garantia: 1},
                url: urlBuscarVenta,
                dataType: "json",
                beforeSend: function (xhr) {

                }
            }).done(function (response) {

                if (response.exito) {
                    $(".agregar-venta").addClass("d-none");
                    $(".quitar-venta").removeClass("d-none");
                    $("#folio-venta-garantia").attr("disabled", true);
                    $("#folio-venta-garantia").addClass("check");
                    $("#empresas").attr("disabled", true);
                } else {
                    Swal.fire(
                        response.msj,
                        '',
                        'warning'
                    );
                }

            }).fail(function (jqXHR, textStatus) {

                Swal.fire(
                    "Error al buscar el ticket: ",
                    JSON.stringify(jqXHR) + JSON.stringify(textStatus),
                    'warning'
                )
            });


        }

        function quitarVenta() {

            $(".agregar-venta").removeClass("d-none");
            $(".quitar-venta").addClass("d-none");
            $("#folio-venta-garantia").attr("disabled", false);
            $("#folio-venta-garantia").removeClass("check");
            $("#empresas").attr("disabled", false);

        }

        function saleDocumentTable(saleid = -1, userid = -1) {

            var url = $("#url-ventas-sale-document-table").val();

            $.ajax({
                url: url,
                data: {saleid: saleid, userid: userid},
                beforeSend: loadingGif("sale-documents-table-container"),
                dataType: "html"
            }).done(function (html) {
                $("#sale-documents-table-container").html(html);

            }).fail(function () {
                alert("error");
            });
        }

        function saleDocumentForm(userid = -1) {

            var url = $("#url-ventas-sale-document-form").val();

            $.ajax({
                url: url,
                data: {userid: userid},
                beforeSend: loadingGif("form-upload-sale-document-container"),
                dataType: "html"
            }).done(function (html) {
                $("#form-upload-sale-document-container").html(html);

            }).fail(function () {
                alert("error");
            });
        }

        function uploadSaleDocument(saleid = -1, saveSale = 0) {

            var url = $("#url-ventas-upload-sale-document").val();

            $.ajax({
                url: url,
                data: {saleid: saleid, saveSale: saveSale},
                dataType: "json"
            }).done(function (response) {

            });
        }

        function setNumberUploadedFiles(numberFiles) {
            numberUploadedFiles = numberFiles;
        }

        $(document).ready(function() {
            // Initialize Select2 for graduation modal selects
            $('.graduation-select').select2({
                width: '100%',
                theme: 'bootstrap-5',
                dropdownParent: $('#modal-graduacion'),
                allowClear: true,
                placeholder: 'Seleccionar',
                language: {
                    noResults: function() {
                        return "No se encontraron resultados";
                    }
                }
            });

            // Initialize Select2 when the graduation modal is shown
            $('#modal-graduacion').on('shown.bs.modal', function() {
                $('.graduation-select').select2({
                    width: '100%',
                    theme: 'bootstrap-5',
                    dropdownParent: $('#modal-graduacion'),
                    allowClear: true,
                    placeholder: 'Seleccionar',
                    language: {
                        noResults: function() {
                            return "No se encontraron resultados";
                        }
                    }
                });
            });

            // Destroy Select2 when the modal is hidden to prevent duplicates
            $('#modal-graduacion').on('hidden.bs.modal', function() {
                $('.graduation-select').select2('destroy');
            });
        });
    </script>


    <script>
        // Cargar graduaciones desde el backend para productos existentes
        window.graduacionesPendientes = window.graduacionesPendientes || {};
        {% for producto in productos %}
            {% if producto.graduacion is defined and producto.graduacion %}
                graduacionesPendientes['{{ producto.idproducto }}'] = {
                    odEsfera: '{{ producto.graduacion.odEsfera|default('') }}',
                    odCilindro: '{{ producto.graduacion.odCilindro|default('') }}',
                    odEje: '{{ producto.graduacion.odEje|default('') }}',
                    odAdicion: '{{ producto.graduacion.odAdicion|default('') }}',
                    oiEsfera: '{{ producto.graduacion.oiEsfera|default('') }}',
                    oiCilindro: '{{ producto.graduacion.oiCilindro|default('') }}',
                    oiEje: '{{ producto.graduacion.oiEje|default('') }}',
                    oiAdicion: '{{ producto.graduacion.oiAdicion|default('') }}',
                    distanciaPupilar: '{{ producto.graduacion.distanciaPupilar|default('') }}',
                    altura: '{{ producto.graduacion.altura|default('') }}',
                    _aco: '{{ producto.graduacion._aco|default('') }}',
                    notasGraduacion: '{{ producto.graduacion.notas|default('') }}',
                    diagnostico: '{{ producto.graduacion.diagnostico|default('') }}',
                    stage: '{{ producto.graduacion.stage|default('9') }}' // Agregar el stage desde la graduación
                };
                // Marcar visualmente que el producto tiene graduación cuando se carga la página
                $(document).ready(function() {
                    $("#producto-{{ producto.idproducto }} .fa-glasses").addClass("text-success");
                    // Actualizar color según el stage
                    actualizarColorProductoPorStage('{{ producto.idproducto }}', '{{ producto.graduacion.stage|default('9') }}');
                });
            {% endif %}
        {% endfor %}

        // Actualizar colores de todos los productos al cargar la página
        $(document).ready(function() {
            setTimeout(function() {
                actualizarColoresProductos();
            }, 1000); // Esperar un poco para que todo se cargue
        });
    </script>
{% endblock %}

