{% extends 'admin/layout.html.twig' %}

{% block title %}Gestión de Stages - Órdenes de Laboratorio{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .stage-pendiente {
            background-color: #fff3cd;
            color: #856404;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .stage-entregado {
            background-color: #d4edda;
            color: #155724;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .btn-stage {
            margin: 2px;
            padding: 5px 10px;
            font-size: 12px;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .alert {
            margin-top: 10px;
        }
    </style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Gestión de Stages - Órdenes de Laboratorio</h3>
                </div>
                <div class="card-body">
                    
                    <!-- <PERSON><PERSON><PERSON><PERSON> de alerta -->
                    <div id="alert-container"></div>
                    
                    <!-- Ejemplo de tabla con órdenes de laboratorio -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID Orden</th>
                                    <th>Cliente</th>
                                    <th>Código de Barras</th>
                                    <th>Stage Actual</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Ejemplo de fila - en la implementación real esto vendría del controlador -->
                                <tr data-order-id="123">
                                    <td>123</td>
                                    <td>Juan Pérez</td>
                                    <td>ABC123</td>
                                    <td>
                                        <span class="stage-display" data-stage="1">Stage 1</span>
                                    </td>
                                    <td>
                                        <button class="btn btn-warning btn-stage btn-sm" 
                                                onclick="setStageNueve(123)">
                                            Marcar como Pendiente (9)
                                        </button>
                                        <button class="btn btn-success btn-stage btn-sm" 
                                                onclick="setStageDiez(123)">
                                            Marcar como Entregado (10)
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- Más filas de ejemplo -->
                                <tr data-order-id="124">
                                    <td>124</td>
                                    <td>María García</td>
                                    <td>DEF456</td>
                                    <td>
                                        <span class="stage-display stage-pendiente" data-stage="9">Pendiente</span>
                                    </td>
                                    <td>
                                        <button class="btn btn-success btn-stage btn-sm" 
                                                onclick="setStageDiez(124)">
                                            Marcar como Entregado (10)
                                        </button>
                                        <button class="btn btn-secondary btn-stage btn-sm" 
                                                onclick="updateStageGenerico(124, '1')">
                                            Regresar a Stage 1
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Formulario de ejemplo para actualizar stage específico -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Actualizar Stage Manualmente</h5>
                                </div>
                                <div class="card-body">
                                    <form id="stage-form">
                                        <div class="form-group">
                                            <label for="orden-id">ID de Orden de Laboratorio:</label>
                                            <input type="number" class="form-control" id="orden-id" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="stage-select">Stage:</label>
                                            <select class="form-control" id="stage-select" required>
                                                <option value="">Seleccionar stage...</option>
                                                <option value="1">1 - Stage 1</option>
                                                <option value="9">9 - Pendiente</option>
                                                <option value="10">10 - Entregado</option>
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Actualizar Stage</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Acciones Rápidas</h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="quick-orden-id">ID de Orden:</label>
                                        <input type="number" class="form-control" id="quick-orden-id">
                                    </div>
                                    <div class="btn-group-vertical w-100">
                                        <button class="btn btn-warning mb-2" onclick="quickSetStageNueve()">
                                            🕐 Establecer como Pendiente (9)
                                        </button>
                                        <button class="btn btn-success mb-2" onclick="quickSetStageDiez()">
                                            ✅ Establecer como Entregado (10)
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/stage-manager.js') }}"></script>
    <script>
        // Funciones de utilidad para mostrar mensajes
        function showSuccessMessage(message) {
            showAlert(message, 'success');
        }
        
        function showErrorMessage(message) {
            showAlert(message, 'danger');
        }
        
        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            `;
            $('#alert-container').html(alertHtml);
            
            // Auto-ocultar después de 5 segundos
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }
        
        function showLoading() {
            $('body').addClass('loading');
        }
        
        function hideLoading() {
            $('body').removeClass('loading');
        }
        
        // Manejar el formulario de actualización manual
        $('#stage-form').on('submit', function(e) {
            e.preventDefault();
            
            const ordenId = $('#orden-id').val();
            const stage = $('#stage-select').val();
            
            if (!ordenId || !stage) {
                showErrorMessage('Por favor complete todos los campos');
                return;
            }
            
            updateStageGenerico(ordenId, stage, function(response) {
                // Limpiar formulario en caso de éxito
                $('#stage-form')[0].reset();
            });
        });
        
        // Funciones para acciones rápidas
        function quickSetStageNueve() {
            const ordenId = $('#quick-orden-id').val();
            if (!ordenId) {
                showErrorMessage('Por favor ingrese un ID de orden');
                return;
            }
            setStageNueve(ordenId);
        }
        
        function quickSetStageDiez() {
            const ordenId = $('#quick-orden-id').val();
            if (!ordenId) {
                showErrorMessage('Por favor ingrese un ID de orden');
                return;
            }
            setStageDiez(ordenId);
        }
        
        // Inicialización cuando el documento esté listo
        $(document).ready(function() {
            console.log('Stage Manager inicializado');
        });
    </script>
{% endblock %}
