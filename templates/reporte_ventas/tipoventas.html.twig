<div class="cont">
    <label class="filter-title">Tipos de venta: </label> 
    <div class="col-md-9 miLista">
        <fieldset id="idtipoventa">
            
            <label class="check" for="todasTipoVenta">
                <input type="checkbox" name="todasTipoVenta" onclick="toggleTiposVenta(this)" id="todasTipoVenta" checked>TODAS</label> 
            {% for tipoventa in tiposVenta %}  
                <label class="check" for="{{ tipoventa.idtipoventa }}">
                    <input type="checkbox" id="{{ tipoventa.idtipoventa }}" name="tipoVenta" value="{{ tipoventa.idtipoventa }}" checked>
                {% if tipoventa.nombre == "" %}Público en General
                {% elseif tipoventa.nombre == "UAM" %}Prestación UAM
                {% else %}{{ tipoventa.nombre }}
                {% endif %}</label>
            {% endfor %}
        </fieldset>
    </div>
</div>

<script>
    function toggleTiposVenta(source) {
        checkboxes = document.getElementsByName('tipoVenta');
        for(var i=0, n=checkboxes.length; i<n; i++) {
            checkboxes[i].checked = source.checked;
        }
    }
</script>