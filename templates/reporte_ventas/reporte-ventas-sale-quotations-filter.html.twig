<div id="sale-quotation-container" class="cont">
    <label class="filter-title">Cotización / Venta: </label> 
    <div class="col-md-9 miLista">
        <fieldset>
            <label class="check" for="allSaleQuotations">
                <input type="checkbox" name="allSaleQuotations" onclick="toggleSaleQuotation(this)" id="allSaleQuotations" checked>
                TODAS</label> 
            <label class="check">
                <input type="checkbox" name="sale-quotation" value="1" checked>
                Cotización
            </label>
            <label class="check">
                <input type="checkbox" name="sale-quotation" value="0"checked>
                Venta
            </label>
        </fieldset>
    </div>
</div>

<script>

    function toggleSaleQuotation(source){
        var checkboxes = document.getElementsByName('sale-quotation');
        for(var i=0, n=checkboxes.length; i<n; i++) {
            checkboxes[i].checked = source.checked;
        }
    }

</script>
